"""
Purpose: Provides transliteration services between Latin script and other scripts
         like Hebrew, Greek, and Coptic for gematria calculations.

This file is part of the gematria pillar and serves as a service component.
It is responsible for converting user input from a familiar script (e.g., Latin)
to the target script required for specific gematria methods.

Key components:
- TransliterationService: Class containing transliteration logic and mappings.
- Language-specific transliteration maps following official documentation rules.

Dependencies:
- gematria.models.calculation_type.Language (for Language enum)

Related files:
- gematria/services/gematria_service.py: Will use this service.
- gematria/ui/*: UI components will interact with this for input.
"""

from typing import Dict

from loguru import logger

from gematria.models.calculation_type import (
    Language,
)


class TransliterationService:
    """Service for handling transliteration between scripts following official documentation rules."""

    def __init__(self):
        """Initialize the transliteration service with official script mappings."""
        
        # Official Hebrew Transliteration System (from docs/Gematria_Methods.md)
        # Hebrew letters to ASCII according to official documentation
        self._hebrew_to_latin_map = {
            "א": "a",
            "ב": "b",
            "ג": "g",
            "ד": "d",
            "ה": "h",
            "ו": "v",
            "ז": "z",
            "ח": "x",
            "ט": "j",
            "י": "y",
            "כ": "k",
            "ך": "K",  # Final Kaph
            "ל": "l",
            "מ": "m",
            "ם": "M",  # Final Mem
            "נ": "n",
            "ן": "N",  # Final Nun
            "ס": "s",
            "ע": "o",
            "פ": "p",
            "ף": "P",  # Final Pe
            "צ": "c",
            "ץ": "C",  # Final Tsade
            "ק": "q",
            "ר": "r",
            "ש": "$",
            "ת": "t",
        }
        
        # Official Latin to Hebrew mapping (reverse of above)
        self._latin_to_hebrew_map = {
            "a": "א",
            "b": "ב",
            "g": "ג",
            "d": "ד",
            "h": "ה",
            "v": "ו",
            "z": "ז",
            "x": "ח",
            "j": "ט",
            "y": "י",
            "k": "כ",
            "K": "ך",  # Final Kaph
            "l": "ל",
            "m": "מ",
            "M": "ם",  # Final Mem
            "n": "נ",
            "N": "ן",  # Final Nun
            "s": "ס",
            "o": "ע",
            "p": "פ",
            "P": "ף",  # Final Pe
            "c": "צ",
            "C": "ץ",  # Final Tsade
            "q": "ק",
            "r": "ר",
            "$": "ש",
            "t": "ת",
        }

        # Official Greek Transliteration System (from docs/Gematria_Methods.md)
        # Greek letters to ASCII according to official documentation
        self._greek_to_latin_map = {
            "α": "a", "Α": "a",
            "β": "b", "Β": "b",
            "γ": "g", "Γ": "g",
            "δ": "d", "Δ": "d",
            "ε": "e", "Ε": "e",
            "ζ": "z", "Ζ": "z",
            "η": "h", "Η": "h",
            "θ": "q", "Θ": "q",
            "ι": "i", "Ι": "i",
            "κ": "k", "Κ": "k",
            "λ": "l", "Λ": "l",
            "μ": "m", "Μ": "m",
            "ν": "n", "Ν": "n",
            "ξ": "c", "Ξ": "c",
            "ο": "o", "Ο": "o",
            "π": "p", "Π": "p",
            "ρ": "r", "Ρ": "r",
            "σ": "s", "ς": "s", "Σ": "s",  # Both sigma forms
            "τ": "t", "Τ": "t",
            "υ": "u", "Υ": "u",
            "φ": "f", "Φ": "f",
            "χ": "x", "Χ": "x",
            "ψ": "y", "Ψ": "y",
            "ω": "w", "Ω": "w",
        }
        
        # Official Latin to Greek mapping (reverse of above)
        self._latin_to_greek_map = {
            "a": "α",
            "b": "β",
            "g": "γ",
            "d": "δ",
            "e": "ε",
            "z": "ζ",
            "h": "η",
            "q": "θ",  # Theta
            "i": "ι",
            "k": "κ",
            "l": "λ",
            "m": "μ",
            "n": "ν",
            "c": "ξ",  # Xi
            "o": "ο",
            "p": "π",
            "r": "ρ",
            "s": "σ",
            "t": "τ",
            "u": "υ",
            "f": "φ",
            "x": "χ",
            "y": "ψ",
            "w": "ω",
        }

        # Coptic to Latin (using the values from user docs primarily)
        self._coptic_to_latin_map = {
            "ⲁ": "a", "Ⲁ": "a",
            "ⲃ": "b", "Ⲃ": "b",
            "ⲅ": "g", "Ⲅ": "g",
            "ⲇ": "d", "Ⲇ": "d",
            "ⲉ": "e", "Ⲉ": "e",
            "ⲋ": "so", "Ⲋ": "so",
            "ⲍ": "z", "Ⲍ": "z",
            "ⲏ": "h", "Ⲏ": "h",
            "ⲑ": "th", "Ⲑ": "th",
            "ⲓ": "i", "Ⲓ": "i",
            "ⲕ": "k", "Ⲕ": "k",
            "ⲗ": "l", "Ⲗ": "l",
            "ⲙ": "m", "Ⲙ": "m",
            "ⲛ": "n", "Ⲛ": "n",
            "ⲝ": "ks", "Ⲝ": "ks",
            "ⲟ": "o", "Ⲟ": "o",
            "ⲡ": "p", "Ⲡ": "p",
            "ⲣ": "r", "Ⲣ": "r",
            "ⲥ": "s", "Ⲥ": "s",
            "ⲧ": "t", "Ⲧ": "t",
            "ⲩ": "u", "Ⲩ": "u",
            "ⲫ": "ph", "Ⲫ": "ph",
            "ⲭ": "kh", "Ⲭ": "kh",
            "ⲯ": "ps", "Ⲯ": "ps",
            "ⲱ": "w", "Ⲱ": "w",
            "ϣ": "sh", "Ϣ": "sh",
            "ϥ": "f", "Ϥ": "f",
            "ϧ": "kh", "Ϧ": "kh",
            "ϩ": "h", "Ϩ": "h",
            "ϫ": "j", "Ϫ": "j",
            "ϭ": "ch", "Ϭ": "ch",
            "ϯ": "ti", "Ϯ": "ti",
        }
        
        # Latin to Coptic (reverse mapping with multi-character handling)
        self._latin_to_coptic_map = {}
        for coptic, latin in self._coptic_to_latin_map.items():
            # Use lowercase coptic letters for consistency
            if coptic.islower():
                self._latin_to_coptic_map[latin] = coptic
        
        # Arabic to Latin (Simplified - keeping existing for compatibility)
        self._arabic_to_latin_map = {
            "ا": "a",
            "ب": "b",
            "ج": "j",
            "د": "d",
            "ه": "h",
            "و": "w",
            "ز": "z",
            "ح": "H",
            "ط": "T",
            "ي": "y",
            "ك": "k",
            "ل": "l",
            "م": "m",
            "ن": "n",
            "س": "s",
            "ع": "'",
            "ف": "f",
            "ص": "S",
            "ق": "q",
            "ر": "r",
            "ش": "sh",
            "ت": "t",
            "ث": "th",
            "خ": "kh",
            "ذ": "dh",
            "ض": "D",
            "ظ": "Z",
            "غ": "gh",
        }

        # Latin to Arabic (reverse mapping)
        self._latin_to_arabic_map = {v: k for k, v in self._arabic_to_latin_map.items()}
        
        # Create ordered keys for multi-character sequences (longest first)
        self._latin_to_coptic_ordered_keys = sorted(
            self._latin_to_coptic_map.keys(), key=len, reverse=True
        )
        self._latin_to_arabic_ordered_keys = sorted(
            self._latin_to_arabic_map.keys(), key=len, reverse=True
        )

    def transliterate_to_latin(self, text: str, source_language: Language) -> str:
        """Transliterates text from the source script to Latin."""
        target_map = {}
        if source_language == Language.HEBREW:
            target_map = self._hebrew_to_latin_map
        elif source_language == Language.GREEK:
            target_map = self._greek_to_latin_map
        elif source_language == Language.COPTIC:
            target_map = self._coptic_to_latin_map
        elif source_language == Language.ARABIC:
            target_map = self._arabic_to_latin_map
        else:
            logger.warning(
                f"Transliteration from {source_language} to Latin not supported."
            )
            return text

        # Character-by-character mapping
        output = []
        for char_in in text:
            output.append(target_map.get(char_in, char_in))
        return "".join(output)

    def transliterate_to_script(self, text: str, target_language: Language) -> str:
        """Transliterates Latin text to the target script (Hebrew, Greek, Coptic, Arabic)."""
        if target_language == Language.HEBREW:
            return self._transliterate_hebrew_from_latin(text)
        elif target_language == Language.GREEK:
            return self._transliterate_greek_from_latin(text)
        elif target_language == Language.COPTIC:
            return self._transliterate_coptic_from_latin(text)
        elif target_language == Language.ARABIC:
            return self._transliterate_arabic_from_latin(text)
        else:
            logger.warning(f"Transliteration to {target_language} not supported.")
            return text

    def _transliterate_hebrew_from_latin(self, text: str) -> str:
        """Transliterates Latin text to Hebrew using official rules."""
        output = []
        for char in text:
            hebrew_char = self._latin_to_hebrew_map.get(char, char)
            output.append(hebrew_char)
        return "".join(output)

    def _transliterate_greek_from_latin(self, text: str) -> str:
        """Transliterates Latin text to Greek using official rules."""
        output = []
        for char in text:
            greek_char = self._latin_to_greek_map.get(char, char)
            output.append(greek_char)
        return "".join(output)

    def _transliterate_coptic_from_latin(self, text: str) -> str:
        """Transliterates Latin text to Coptic, handling multi-character sequences."""
        output = []
        i = 0
        text_len = len(text)
        
        while i < text_len:
            matched_key = None
            # Try to match the longest possible key first
            for key in self._latin_to_coptic_ordered_keys:
                if text.startswith(key, i):
                    matched_key = key
                    break

            if matched_key:
                output.append(self._latin_to_coptic_map[matched_key])
                i += len(matched_key)
            else:
                # If no known key matches, append the character as is
                output.append(text[i])
                i += 1
        return "".join(output)

    def _transliterate_arabic_from_latin(self, text: str) -> str:
        """Transliterates Latin text to Arabic, handling multi-character sequences."""
        output = []
        i = 0
        text_len = len(text)
        
        while i < text_len:
            matched_key = None
            # Try to match the longest possible key first
            for key in self._latin_to_arabic_ordered_keys:
                if text.startswith(key, i):
                    matched_key = key
                    break

            if matched_key:
                output.append(self._latin_to_arabic_map[matched_key])
                i += len(matched_key)
            else:
                # If no known key matches, append the character as is
                output.append(text[i])
                i += 1
        return "".join(output)
