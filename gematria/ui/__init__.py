"""
UI components for the Gematria pillar.

This module contains UI components used in the Gematria pillar.
Each component provides a specific interface for gematria functionality.
"""

# Import dialog/window classes
from gematria.ui.dialogs.custom_cipher_dialog import CustomCipherDialog
from gematria.ui.dialogs.custom_cipher_dialog_modern import ModernCustomCipherWidget
from gematria.ui.dialogs.gematria_help_dialog import GematriaHelpDialog

# Import window classes
from gematria.ui.windows.calculation_history_window import CalculationHistoryWindow
from gematria.ui.windows.help_window import HelpWindow
from gematria.ui.windows.modern_search_window import ModernSearchWindow
from gematria.ui.windows.number_dictionary_search_window import NumberDictionarySearchWindow
from gematria.ui.windows.number_dictionary_window import NumberDictionaryWindow
from gematria.ui.windows.search_window import SearchWindow
from gematria.ui.windows.tag_management_window import Tag<PERSON>anagementWindow
from gematria.ui.windows.word_abacus_window import <PERSON><PERSON><PERSON><PERSON><PERSON>indow
from gematria.ui.windows.search_results_window import SearchResultsWindow

# Import widget classes
from gematria.ui.widgets.calculation_detail_widget import CalculationDetailWidget
from gematria.ui.widgets.modern_search_widget import ModernSearchWidget
from gematria.ui.widgets.virtual_keyboard_widget import VirtualKeyboardWidget
from gematria.ui.widgets.word_abacus_widget import WordAbacusWidget

# Import panel classes
from gematria.ui.panels.calculation_history_panel import CalculationHistoryPanel
from gematria.ui.panels.search_panel import SearchPanel

# Export all classes
__all__ = [
    # Dialog classes
    "CustomCipherDialog",
    "ModernCustomCipherWidget", 
    "GematriaHelpDialog",
    # Window classes
    "CalculationHistoryWindow",
    "HelpWindow",
    "ModernSearchWindow",
    "NumberDictionarySearchWindow",
    "NumberDictionaryWindow",
    "SearchWindow",
    "TagManagementWindow",
    "WordAbacusWindow",
    "SearchResultsWindow",
    # Widget classes
    "CalculationDetailWidget",
    "ModernSearchWidget",
    "VirtualKeyboardWidget",
    "WordAbacusWidget",
    # Panel classes
    "CalculationHistoryPanel",
    "SearchPanel",
]
