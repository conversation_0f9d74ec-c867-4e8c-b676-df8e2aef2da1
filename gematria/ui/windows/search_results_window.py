"""
@file search_results_window.py
@description Search results window for displaying gematria search results
<AUTHOR> Assistant
@created 2024-12-19
@lastModified 2024-12-19
@dependencies PyQt6, gematria services, TQ styling system
"""

from typing import List, Optional

from loguru import logger
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QHeaderView,
    QLabel,
    QMainWindow,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
)

from gematria.models.calculation_result import CalculationResult
from gematria.services.calculation_database_service import CalculationDatabaseService
from gematria.services.custom_cipher_service import CustomCipherService
from gematria.ui.widgets.calculation_detail_widget import CalculationDetailWidget
from shared.ui.window_management import WindowManager
from tq.ui.styles.tq_colors import TQColors


class SearchResultsWindow(QMainWindow):
    """Window for displaying gematria search results."""

    def __init__(
        self,
        results: List[CalculationResult],
        search_criteria: str,
        calculation_db_service: CalculationDatabaseService,
        custom_cipher_service: CustomCipherService,
        window_manager: Optional[WindowManager] = None,
        parent: Optional[QWidget] = None,
    ) -> None:
        """Initialize the search results window.

        Args:
            results: List of calculation results to display
            search_criteria: Description of the search criteria used
            calculation_db_service: Service for database operations
            custom_cipher_service: Service for custom cipher operations
            window_manager: Application window manager
            parent: Parent widget
        """
        super().__init__(parent)
        self.results = results
        self.search_criteria = search_criteria
        self.calculation_db_service = calculation_db_service
        self.custom_cipher_service = custom_cipher_service
        self.window_manager = window_manager
        
        # Set window properties
        self.setWindowTitle(f"🔍 Search Results - {len(results)} found")
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        
        # Apply modern styling
        self._apply_modern_styling()
        
        # Set up UI
        self._setup_ui()
        
        # Display results
        self._display_results()
        
        logger.debug(f"SearchResultsWindow initialized with {len(results)} results")

    def _apply_modern_styling(self) -> None:
        """Apply modern styling to the window."""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {TQColors.BACKGROUND_LIGHT},
                    stop: 1 #ffffff);
                border: none;
            }}
        """)

    def _setup_ui(self) -> None:
        """Set up the user interface."""
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # Header section
        self._create_header(layout)
        
        # Results table
        self._create_results_table(layout)

    def _create_header(self, layout: QVBoxLayout) -> None:
        """Create the header section.
        
        Args:
            layout: The layout to add the header to
        """
        # Title
        title_label = QLabel(f"🔍 Search Results ({len(self.results)} found)")
        title_font = QFont("Segoe UI", 18, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {TQColors.PRIMARY_DARK};
                background: transparent;
                border: none;
                margin-bottom: 10px;
            }}
        """)
        layout.addWidget(title_label)
        
        # Search criteria
        if self.search_criteria:
            criteria_label = QLabel(f"Search: {self.search_criteria}")
            criteria_font = QFont("Segoe UI", 12)
            criteria_label.setFont(criteria_font)
            criteria_label.setStyleSheet(f"""
                QLabel {{
                    color: {TQColors.TEXT_SECONDARY};
                    background: transparent;
                    border: none;
                    margin-bottom: 15px;
                }}
            """)
            layout.addWidget(criteria_label)

    def _create_results_table(self, layout: QVBoxLayout) -> None:
        """Create the results table.
        
        Args:
            layout: The layout to add the table to
        """
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels(
            ["Text", "Value", "Method", "Tags", "★"]
        )
        
        # Style the table
        self.results_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid {TQColors.GRID_BORDER};
                border-radius: 8px;
                gridline-color: {TQColors.GRID_BORDER};
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {TQColors.GRID_BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {TQColors.PRIMARY_LIGHT};
                color: white;
            }}
            QTableWidget::item:hover {{
                background-color: #f8fbff;
            }}
            QHeaderView::section {{
                background-color: {TQColors.PRIMARY};
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: 600;
                font-size: 14px;
            }}
        """)

        # Set column widths for better display
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Text column stretches
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Value column
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Method column
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # Tags column - fixed width
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Favorite column

        # Set specific width for Tags column
        self.results_table.setColumnWidth(3, 200)

        # Enable sorting
        self.results_table.setSortingEnabled(True)

        # Configure table behavior
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.results_table.itemDoubleClicked.connect(self._open_detail_window)

        # Make the rows taller for better readability
        self.results_table.verticalHeader().setDefaultSectionSize(40)
        
        layout.addWidget(self.results_table)

    def _display_results(self) -> None:
        """Display search results in the table."""
        self.results_table.setRowCount(len(self.results))
        
        for i, result in enumerate(self.results):
            # Text column
            text_item = QTableWidgetItem(result.input_text)
            text_item.setData(Qt.ItemDataRole.UserRole, result)
            self.results_table.setItem(i, 0, text_item)

            # Value column
            value_item = QTableWidgetItem(str(result.result_value))
            value_item.setData(Qt.ItemDataRole.UserRole, result.result_value)
            value_item.setTextAlignment(Qt.AlignmentFlag.AlignRight | Qt.AlignmentFlag.AlignVCenter)
            self.results_table.setItem(i, 1, value_item)

            # Method column
            method_name = self._get_method_name(result)
            method_item = QTableWidgetItem(method_name)
            self.results_table.setItem(i, 2, method_item)

            # Tags column - handle tags safely
            tags_item = QTableWidgetItem()
            try:
                if hasattr(result, 'tags') and result.tags:
                    # Get tag names from the database service
                    tag_names = self.calculation_db_service.get_calculation_tag_names(result)
                    tags_item.setText(", ".join(tag_names) if tag_names else "")
                else:
                    tags_item.setText("")
            except Exception as e:
                logger.debug(f"Error processing tags for result {result.id}: {e}")
                tags_item.setText("")
            self.results_table.setItem(i, 3, tags_item)

            # Favorite column
            favorite_item = QTableWidgetItem("★" if getattr(result, 'is_favorite', False) or getattr(result, 'favorite', False) else "")
            favorite_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.results_table.setItem(i, 4, favorite_item)

    def _get_method_name(self, calculation: CalculationResult) -> str:
        """Get display name for calculation method.
        
        Args:
            calculation: The calculation result
            
        Returns:
            Display name for the method
        """
        try:
            # First check for custom method name
            if hasattr(calculation, 'method_name') and calculation.method_name:
                method_name = calculation.method_name
                # If it's a tuple, extract the first element (the name)
                if isinstance(method_name, tuple) and len(method_name) > 0:
                    return str(method_name[0])
                return str(method_name)
            
            # Then check for calculation_type
            if hasattr(calculation, 'calculation_type') and calculation.calculation_type:
                calc_type = calculation.calculation_type
                
                # Handle tuple format (name, description, language)
                if isinstance(calc_type, tuple) and len(calc_type) > 0:
                    return str(calc_type[0])
                
                # Handle string format
                elif isinstance(calc_type, str):
                    # Check if it's a string representation of a tuple
                    if calc_type.startswith("(") and calc_type.endswith(")"):
                        # Parse the tuple string manually
                        try:
                            # Remove outer parentheses and split by comma
                            inner = calc_type[1:-1]
                            # Find the first quoted string (the method name)
                            if inner.startswith("'") or inner.startswith('"'):
                                quote_char = inner[0]
                                end_quote = inner.find(quote_char, 1)
                                if end_quote > 0:
                                    return inner[1:end_quote]
                        except:
                            pass
                    return calc_type.replace("_", " ").title()
                
                # Handle object with name attribute
                elif hasattr(calc_type, 'name'):
                    name = calc_type.name
                    if isinstance(name, tuple) and len(name) > 0:
                        return str(name[0])
                    return str(name).replace("_", " ").title()
                
                # Fallback - try to parse string representation of tuple
                else:
                    result = str(calc_type)
                    # If it looks like a tuple string representation
                    if result.startswith("(") and "," in result and result.endswith(")"):
                        try:
                            # Use eval safely to parse the tuple (only if it looks safe)
                            if all(char in "(),'\" abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789._:-<>" for char in result):
                                parsed = eval(result)
                                if isinstance(parsed, tuple) and len(parsed) > 0:
                                    return str(parsed[0])
                        except:
                            # If eval fails, try manual parsing
                            inner = result[1:-1]  # Remove parentheses
                            if inner.startswith("'") or inner.startswith('"'):
                                quote_char = inner[0]
                                end_quote = inner.find(quote_char, 1)
                                if end_quote > 0:
                                    return inner[1:end_quote]
                    
                    return result.replace("_", " ").title()
            
            return "Unknown"
        except Exception as e:
            logger.debug(f"Error getting method name for calculation {getattr(calculation, 'id', 'unknown')}: {e}")
            return "Unknown"

    def _open_detail_window(self, item: QTableWidgetItem) -> None:
        """Open detail window for selected calculation.
        
        Args:
            item: The clicked table item
        """
        # Get the calculation result from the first column of the same row
        row = item.row()
        result_item = self.results_table.item(row, 0)
        
        if result_item and self.window_manager:
            result = result_item.data(Qt.ItemDataRole.UserRole)
            if result:
                try:
                    # Create detail widget with required services
                    detail_widget = CalculationDetailWidget(
                        self.calculation_db_service, 
                        self.custom_cipher_service
                    )
                    detail_widget.set_calculation(result)
                    
                    # Open window with correct signature (only window_id and content)
                    window = self.window_manager.open_window(
                        f"calculation_detail_{result.id}",
                        detail_widget
                    )
                    
                    # Set the window title after creation
                    window.setWindowTitle(f"Calculation Details - {result.input_text}")
                    
                except Exception as e:
                    logger.error(f"Error opening detail window: {e}")
                    import traceback
                    traceback.print_exc() 