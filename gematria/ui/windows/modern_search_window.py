"""
Modern Gematria Search Window.

This module provides a modern, beautifully styled standalone window for gematria search functionality.
It features a card-based layout, modern styling, and enhanced user experience.

Key components:
- ModernSearchWindow: Modern standalone window for gematria search

Dependencies:
- PyQt6: For UI components
- gematria.ui.widgets.modern_search_widget: For the modern search widget content
- gematria.services: For the database and cipher services
"""

from typing import Optional

from loguru import logger
from PyQt6.QtCore import QPropertyAnimation, QRect, Qt, QTimer
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import QLabel, QMainWindow, QVBoxLayout, QWidget

from gematria.services.calculation_database_service import CalculationDatabaseService
from gematria.services.custom_cipher_service import CustomCipherService
from gematria.ui.widgets.modern_search_widget import ModernSearchWidget
from shared.ui.window_management import WindowManager
from tq.ui.styles.tq_colors import TQColors


class ModernSearchWindow(QMainWindow):
    """Modern standalone window for gematria search functionality."""

    def __init__(
        self,
        window_manager: Optional[WindowManager] = None,
        parent: Optional[QWidget] = None,
        exact_value: Optional[int] = None,
    ) -> None:
        """Initialize the modern search window.

        Args:
            window_manager: Application window manager
            parent: Parent widget
            exact_value: Optional exact value to search for immediately
        """
        super().__init__(parent)
        self.setWindowTitle("🔍 Gematria Search")
        
        # Set much larger window sizing to ensure everything fits properly
        self.setMinimumSize(1200, 900)
        self.resize(1400, 1000)  # Much bigger window to accommodate all content
        
        # Apply modern styling
        self._apply_modern_styling()

        # Set up central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Create layout with proper sizing
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create header section
        self._create_header(layout)

        # Create services needed by the search widget
        db_service = CalculationDatabaseService()
        cipher_service = CustomCipherService()

        # Create the modern search widget
        self.search_widget = ModernSearchWidget(db_service, cipher_service, window_manager)
        layout.addWidget(self.search_widget, 1)  # Give search widget stretch factor

        # If an exact value was provided, set it and perform search
        if exact_value is not None:
            self.set_exact_value(exact_value)

        # Add fade-in animation
        self._setup_animations()

        logger.debug("ModernSearchWindow initialized")

    def _apply_modern_styling(self) -> None:
        """Apply modern styling to the window."""
        self.setStyleSheet(f"""
            QMainWindow {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {TQColors.BACKGROUND_LIGHT},
                    stop: 1 #ffffff);
                border: none;
            }}
        """)

    def _create_header(self, layout: QVBoxLayout) -> None:
        """Create the header section with title and subtitle.
        
        Args:
            layout: The main layout to add the header to
        """
        # Header container
        header_widget = QWidget()
        header_widget.setFixedHeight(120)
        header_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 {TQColors.PRIMARY},
                    stop: 0.5 {TQColors.PRIMARY_LIGHT},
                    stop: 1 {TQColors.ACCENT});
                border: none;
            }}
        """)

        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(30, 20, 30, 20)
        header_layout.setSpacing(5)

        # Main title
        title_label = QLabel("🔍 Gematria Search")
        title_font = QFont("Segoe UI", 24, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {TQColors.TEXT_LIGHT};
                background: transparent;
                border: none;
            }}
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(title_label)

        # Subtitle
        subtitle_label = QLabel("Discover hidden connections through numerical values")
        subtitle_font = QFont("Segoe UI", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {TQColors.TEXT_LIGHT};
                background: transparent;
                border: none;
                opacity: 0.9;
            }}
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.addWidget(subtitle_label)

        layout.addWidget(header_widget)

    def _setup_animations(self) -> None:
        """Set up fade-in animation for the window."""
        # Start with window slightly transparent
        self.setWindowOpacity(0.0)
        
        # Create fade-in animation
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(500)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        
        # Start animation after a short delay
        QTimer.singleShot(50, self.fade_animation.start)

    def set_exact_value(self, value: int) -> None:
        """Set the exact value field and perform a search.

        Args:
            value: The value to search for
        """
        # Set the exact value in the search widget
        self.search_widget.set_exact_value(value)

    def showEvent(self, event) -> None:
        """Override show event to ensure proper window sizing after window manager setup."""
        super().showEvent(event)
        
        # Force our desired size after the window manager has done its setup
        self.resize(1400, 1000)
        
        # Also ensure minimum size is respected
        self.setMinimumSize(1200, 900)
        
        logger.debug(f"Applied proper sizing in showEvent: {self.size().width()}x{self.size().height()}") 