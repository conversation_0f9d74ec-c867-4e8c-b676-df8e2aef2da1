"""
@file modern_search_widget.py
@description Modern, styled search widget for gematria functionality with card-based design
<AUTHOR> Assistant
@created 2024-12-19
@lastModified 2024-12-19
@dependencies PyQt6, gematria services, TQ styling system
"""

from typing import Any, Dict, List, Optional, cast

from loguru import logger
from PyQt6.QtCore import QRect, QRegularExpression, Qt, pyqtSignal, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import (
    QColor,
    QFont,
    QIcon,
    QPen,
    QRegularExpressionValidator,
)
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFormLayout,
    QGroupBox,
    QHBoxLayout,
    QHeaderView,
    QLabel,
    QLineEdit,
    QPushButton,
    QStyle,
    QStyledItemDelegate,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
    <PERSON><PERSON><PERSON><PERSON>,
    QSizePolicy,
    QSpacer<PERSON><PERSON>,
    QGrid<PERSON>ayout,
    QScrollArea,
)

from gematria.models.calculation_result import CalculationResult
from gematria.models.calculation_type import CalculationType, Language
from gematria.models.custom_cipher_config import CustomCipherConfig
from gematria.services.calculation_database_service import CalculationDatabaseService
from gematria.services.custom_cipher_service import CustomCipherService
from shared.ui.window_management import WindowManager
from tq.ui.styles.tq_colors import TQColors


class ModernCard(QFrame):
    """A modern card widget with elegant styling and hover effects."""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet(f"""
            ModernCard {{
                background-color: #ffffff;
                border: 1px solid {TQColors.GRID_BORDER};
                border-radius: 12px;
                margin: 8px;
                padding: 0px;
            }}
            ModernCard:hover {{
                border-color: {TQColors.PRIMARY_LIGHT};
                background-color: #f8fbff;
                border-width: 2px;
            }}
        """)
        
        # Main layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 16, 20, 20)
        self.layout.setSpacing(12)
        
        # Title if provided
        if title:
            self.title_label = QLabel(title)
            self.title_label.setStyleSheet(f"""
                QLabel {{
                    font-size: 16px;
                    font-weight: 600;
                    color: {TQColors.PRIMARY_DARK};
                    margin-bottom: 8px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid {TQColors.PRIMARY_LIGHT};
                    background: transparent;
                }}
            """)
            self.layout.addWidget(self.title_label)


class ModernButton(QPushButton):
    """A modern styled button with hover animations."""
    
    def __init__(self, text: str, style: str = "primary", parent=None):
        super().__init__(text, parent)
        
        if style == "primary":
            bg_color = TQColors.PRIMARY
            hover_color = TQColors.PRIMARY_DARK
        elif style == "secondary":
            bg_color = TQColors.SECONDARY
            hover_color = TQColors.SECONDARY_DARK
        elif style == "success":
            bg_color = TQColors.SUCCESS
            hover_color = "#388E3C"
        else:
            bg_color = TQColors.PRIMARY
            hover_color = TQColors.PRIMARY_DARK
            
        self.setStyleSheet(f"""
            ModernButton {{
                background-color: {bg_color};
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                min-width: 100px;
            }}
            ModernButton:hover {{
                background-color: {hover_color};
            }}
            ModernButton:pressed {{
                background-color: {hover_color};
            }}
        """)


class ModernLineEdit(QLineEdit):
    """A modern styled line edit with focus effects."""
    
    def __init__(self, placeholder: str = "", parent=None):
        super().__init__(parent)
        if placeholder:
            self.setPlaceholderText(placeholder)
            
        self.setStyleSheet(f"""
            ModernLineEdit {{
                padding: 12px 16px;
                border: 2px solid {TQColors.GRID_BORDER};
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: {TQColors.TEXT_PRIMARY};
            }}
            ModernLineEdit:focus {{
                border-color: {TQColors.PRIMARY};
                background-color: #f8fbff;
            }}
            ModernLineEdit:hover {{
                border-color: {TQColors.PRIMARY_LIGHT};
            }}
        """)


class ModernComboBox(QComboBox):
    """A modern styled combo box."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setStyleSheet(f"""
            ModernComboBox {{
                padding: 12px 16px;
                border: 2px solid {TQColors.GRID_BORDER};
                border-radius: 8px;
                font-size: 14px;
                background-color: white;
                color: {TQColors.TEXT_PRIMARY};
                min-width: 150px;
            }}
            ModernComboBox:focus {{
                border-color: {TQColors.PRIMARY};
            }}
            ModernComboBox:hover {{
                border-color: {TQColors.PRIMARY_LIGHT};
            }}
            ModernComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            ModernComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {TQColors.TEXT_SECONDARY};
                margin-right: 10px;
            }}
        """)


class ModernCheckBox(QCheckBox):
    """A modern styled checkbox."""
    
    def __init__(self, text: str = "", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet(f"""
            ModernCheckBox {{
                font-size: 14px;
                color: {TQColors.TEXT_PRIMARY};
                spacing: 8px;
            }}
            ModernCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border: 2px solid {TQColors.GRID_BORDER};
                border-radius: 4px;
                background-color: white;
            }}
            ModernCheckBox::indicator:hover {{
                border-color: {TQColors.PRIMARY_LIGHT};
            }}
            ModernCheckBox::indicator:checked {{
                background-color: {TQColors.PRIMARY};
                border-color: {TQColors.PRIMARY};
                image: none;
            }}
            ModernCheckBox::indicator:checked:hover {{
                background-color: {TQColors.PRIMARY_DARK};
            }}
        """)


# Import the existing delegates from the original search panel
from gematria.ui.panels.search_panel import NumericTableWidgetItem, TagItemDelegate


class ModernSearchWidget(QWidget):
    """Modern search widget with card-based layout and enhanced styling."""

    def __init__(
        self,
        calculation_db_service: CalculationDatabaseService,
        custom_cipher_service: CustomCipherService,
        window_manager: Optional[WindowManager] = None,
        parent: Optional[QWidget] = None,
    ):
        """Initialize the modern search widget.

        Args:
            calculation_db_service: Service for database operations
            custom_cipher_service: Service for custom cipher operations
            window_manager: Window manager for opening additional windows
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Set proper size constraints - MUCH BIGGER to fit all content
        self.setMinimumSize(1200, 800)  # Much larger minimum size
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        self.calculation_db_service = calculation_db_service
        self.custom_cipher_service = custom_cipher_service
        self.window_manager = window_manager

        # If no window manager is provided, try to find it in the parent chain
        if self.window_manager is None:
            self.window_manager = self._find_window_manager()

        self._setup_ui()
        logger.debug("ModernSearchWidget initialized")

    def _find_window_manager(self) -> Optional[WindowManager]:
        """Find window manager in parent chain."""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'window_manager'):
                return parent.window_manager
            parent = parent.parent()
        return None

    def _setup_ui(self) -> None:
        """Set up the modern user interface."""
        # Main layout - no scroll area, direct layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Create search criteria cards
        self._create_search_cards(main_layout)

        # Create action buttons
        self._create_action_buttons(main_layout)

        # Don't add stretch - let the cards take up the space naturally

        # Initialize components
        self._update_method_combo()
        self._update_tag_combo()

    def _create_search_cards(self, layout: QVBoxLayout) -> None:
        """Create the search criteria cards in a 2x2 grid.
        
        Args:
            layout: The layout to add cards to
        """
        # Cards container
        cards_widget = QWidget()
        cards_widget.setMinimumHeight(500)  # Increase minimum height for cards
        cards_layout = QGridLayout(cards_widget)
        cards_layout.setSpacing(16)
        cards_layout.setContentsMargins(0, 0, 0, 0)

        # Text Search Card
        text_card = ModernCard("🔤 Text Search")
        text_card.setMinimumHeight(220)  # Increase minimum height for each card
        
        # Text search input
        self.text_search = ModernLineEdit("Enter text to search...")
        text_card.layout.addWidget(QLabel("Search Text:"))
        text_card.layout.addWidget(self.text_search)
        
        # Exact match checkbox
        self.exact_text_match = ModernCheckBox("Exact match only")
        text_card.layout.addWidget(self.exact_text_match)
        
        cards_layout.addWidget(text_card, 0, 0)

        # Value Search Card
        value_card = ModernCard("🔢 Value Search")
        value_card.setMinimumHeight(220)  # Increase minimum height for each card
        
        # Exact value input
        self.exact_value = ModernLineEdit("Enter exact value...")
        validator = QRegularExpressionValidator(QRegularExpression("^[0-9]*$"))
        self.exact_value.setValidator(validator)
        value_card.layout.addWidget(QLabel("Exact Value:"))
        value_card.layout.addWidget(self.exact_value)
        
        cards_layout.addWidget(value_card, 0, 1)

        # Method Filters Card
        method_card = ModernCard("⚙️ Method Filters")
        method_card.setMinimumHeight(220)  # Increase minimum height for each card
        
        # Language filter
        method_card.layout.addWidget(QLabel("Language:"))
        self.language_combo = ModernComboBox()
        self.language_combo.addItems(["Any Language", "Hebrew", "Greek", "English"])
        self.language_combo.currentIndexChanged.connect(self._update_method_combo)
        method_card.layout.addWidget(self.language_combo)
        
        # Method filter
        method_card.layout.addWidget(QLabel("Method:"))
        self.method_combo = ModernComboBox()
        self.method_combo.addItem("Any Method")
        method_card.layout.addWidget(self.method_combo)
        
        cards_layout.addWidget(method_card, 1, 0)

        # Additional Filters Card
        filters_card = ModernCard("🏷️ Additional Filters")
        filters_card.setMinimumHeight(220)  # Increase minimum height for each card
        
        # Favorites filter
        self.favorites_only = ModernCheckBox("Favorites only")
        filters_card.layout.addWidget(self.favorites_only)
        
        # Tags filter
        tags_layout = QHBoxLayout()
        self.has_tags = ModernCheckBox("Has tags")
        self.has_tags.stateChanged.connect(self._on_has_tags_changed)
        tags_layout.addWidget(self.has_tags)
        
        self.tag_combo = ModernComboBox()
        self.tag_combo.setEnabled(False)
        tags_layout.addWidget(self.tag_combo)
        filters_card.layout.addLayout(tags_layout)
        
        # Notes filter
        self.has_notes = ModernCheckBox("Has notes")
        filters_card.layout.addWidget(self.has_notes)
        
        cards_layout.addWidget(filters_card, 1, 1)

        layout.addWidget(cards_widget)

    def _create_action_buttons(self, layout: QVBoxLayout) -> None:
        """Create the action buttons section.
        
        Args:
            layout: The layout to add buttons to
        """
        buttons_widget = QWidget()
        buttons_widget.setMinimumHeight(120)  # Increase minimum height for buttons
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 30, 0, 30)  # Increase vertical margins
        
        # Add stretch to center buttons
        buttons_layout.addStretch()
        
        # Search button
        self.search_button = ModernButton("🔍 Search", "primary")
        self.search_button.clicked.connect(self._perform_search)
        buttons_layout.addWidget(self.search_button)
        
        # Clear button
        self.clear_button = ModernButton("🗑️ Clear", "secondary")
        self.clear_button.clicked.connect(self._clear_search)
        buttons_layout.addWidget(self.clear_button)
        
        buttons_layout.addStretch()
        
        layout.addWidget(buttons_widget)

    def _create_results_section(self, layout: QVBoxLayout) -> None:
        """Create the results table section.
        
        Args:
            layout: The layout to add results to
        """
        # Results card
        results_card = ModernCard("📊 Search Results")
        
        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(5)
        self.results_table.setHorizontalHeaderLabels(
            ["Text", "Value", "Method", "Tags", "★"]
        )
        
        # Set minimum height for the table to ensure it's visible
        self.results_table.setMinimumHeight(300)
        
        # Set size policy to expand
        self.results_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        # Style the table
        self.results_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: white;
                border: 1px solid {TQColors.GRID_BORDER};
                border-radius: 8px;
                gridline-color: {TQColors.GRID_BORDER};
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {TQColors.GRID_BORDER};
            }}
            QTableWidget::item:selected {{
                background-color: {TQColors.PRIMARY_LIGHT};
                color: white;
            }}
            QTableWidget::item:hover {{
                background-color: #f8fbff;
            }}
            QHeaderView::section {{
                background-color: {TQColors.PRIMARY};
                color: white;
                padding: 12px 8px;
                border: none;
                font-weight: 600;
                font-size: 14px;
            }}
        """)

        # Set column widths for better display
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # Text column stretches
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Value column
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Method column
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Fixed)  # Tags column - fixed width
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Favorite column

        # Set specific width for Tags column
        self.results_table.setColumnWidth(3, 200)

        # Enable sorting
        self.results_table.setSortingEnabled(True)

        # Configure table behavior
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.results_table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        self.results_table.itemSelectionChanged.connect(self._on_result_selected)
        self.results_table.itemDoubleClicked.connect(self._open_detail_window)

        # Make the rows taller for better readability
        self.results_table.verticalHeader().setDefaultSectionSize(40)
        
        results_card.layout.addWidget(self.results_table)
        
        # Give the results card a stretch factor so it expands
        layout.addWidget(results_card, 1)

    def _update_method_combo(self) -> None:
        """Update the method combo box based on the selected language."""
        self.method_combo.clear()
        self.method_combo.addItem("Any Method")

        language_idx = self.language_combo.currentIndex()

        # Get calculation types for selected language
        if language_idx == 0:  # Any Language
            methods = CalculationType.get_all_types()
        elif language_idx == 1:  # Hebrew
            methods = CalculationType.get_types_for_language(Language.HEBREW)
        elif language_idx == 2:  # Greek
            methods = CalculationType.get_types_for_language(Language.GREEK)
        elif language_idx == 3:  # English
            methods = CalculationType.get_types_for_language(Language.ENGLISH)
        else:
            methods = []

        # Add standard methods to combo
        for method in methods:
            # Use the first element of the tuple which contains the clean display name
            try:
                display_name = method.value[0]
            except (IndexError, TypeError):
                # Fallback to formatting the name if tuple access fails
                display_name = method.name.replace("_", " ").title()
            self.method_combo.addItem(display_name, method)

        # Add custom methods if applicable
        if language_idx > 0:
            language = [Language.HEBREW, Language.GREEK, Language.ENGLISH][language_idx - 1]
            custom_methods = self.custom_cipher_service.get_methods_for_language(language)

            if custom_methods:
                self.method_combo.insertSeparator(self.method_combo.count())
                for custom_method in custom_methods:
                    custom_method_obj: CustomCipherConfig = custom_method
                    display_name = f"Custom: {custom_method_obj.name}"
                    self.method_combo.addItem(display_name, custom_method_obj.name)

    def _perform_search(self) -> None:
        """Perform search based on current criteria."""
        logger.debug("Performing search in ModernSearchWidget")
        
        # Get search criteria
        text_query = self.text_search.text().strip()
        exact_text = self.exact_text_match.isChecked()
        exact_value_text = self.exact_value.text().strip()
        
        # Convert exact value to int if provided
        exact_value = None
        if exact_value_text:
            try:
                exact_value = int(exact_value_text)
            except ValueError:
                logger.warning(f"Invalid exact value: {exact_value_text}")
                return

        # Get method filter
        method_data = self.method_combo.currentData()
        
        # Get other filters
        favorites_only = self.favorites_only.isChecked()
        has_tags = self.has_tags.isChecked()
        selected_tag = self.tag_combo.currentText() if has_tags else None
        has_notes = self.has_notes.isChecked()

        # Build criteria dictionary for the search
        criteria = {}
        
        # Add text search criteria
        if text_query:
            if exact_text:
                criteria["input_text"] = text_query
            else:
                criteria["input_text_like"] = f"%{text_query}%"
        
        # Add value search criteria
        if exact_value is not None:
            criteria["result_value"] = exact_value
            
        # Add method filter
        if method_data and method_data != "Any Method":
            if isinstance(method_data, str) and method_data.startswith("Custom:"):
                # Get the clean method name without "Custom: " prefix
                custom_method = method_data.replace("Custom: ", "")
                # If the custom method is a tuple, extract first element
                if isinstance(custom_method, tuple) and len(custom_method) > 0:
                    criteria["custom_method_name"] = custom_method[0]
                else:
                    criteria["custom_method_name"] = custom_method
            else:
                criteria["calculation_type"] = method_data
                
        # Add other filters
        if favorites_only:
            criteria["favorite"] = True
            
        if has_tags:
            criteria["has_tags"] = True
            if selected_tag:
                # Get tag ID from tag name
                try:
                    tags = self.calculation_db_service.get_all_tags()
                    for tag in tags:
                        # Handle both string tags and tag objects
                        tag_name = tag.name if hasattr(tag, 'name') else str(tag)
                        if tag_name == selected_tag:
                            tag_id = tag.id if hasattr(tag, 'id') else tag
                            criteria["tag_id"] = tag_id
                            break
                except Exception as e:
                    logger.error(f"Error getting tag ID: {e}")
                    
        if has_notes:
            criteria["has_notes"] = True

        # Perform search
        try:
            results = self.calculation_db_service.search_calculations(criteria)
            
            # Build search criteria description for the results window
            criteria_parts = []
            if text_query:
                criteria_parts.append(f"Text: '{text_query}'" + (" (exact)" if exact_text else ""))
            if exact_value is not None:
                criteria_parts.append(f"Value: {exact_value}")
            if method_data and method_data != "Any Method":
                method_name = method_data if isinstance(method_data, str) else str(method_data)
                criteria_parts.append(f"Method: {method_name}")
            if favorites_only:
                criteria_parts.append("Favorites only")
            if has_tags:
                if selected_tag:
                    criteria_parts.append(f"Tag: {selected_tag}")
                else:
                    criteria_parts.append("Has tags")
            if has_notes:
                criteria_parts.append("Has notes")
            
            search_criteria = ", ".join(criteria_parts) if criteria_parts else "All calculations"
            
            # Open results in a new window
            self._open_results_window(results, search_criteria)
            
            logger.debug(f"Search completed with {len(results)} results")
            
        except Exception as e:
            logger.error(f"Error performing search: {e}")
            import traceback
            traceback.print_exc()

    def _open_results_window(self, results: List[CalculationResult], search_criteria: str) -> None:
        """Open search results in a new window.
        
        Args:
            results: List of calculation results to display
            search_criteria: Description of the search criteria used
        """
        if self.window_manager:
            try:
                from gematria.ui.windows.search_results_window import SearchResultsWindow
                
                # Create unique window ID based on search criteria and timestamp
                import time
                window_id = f"search_results_{int(time.time())}"
                
                # Create the results window
                results_window = SearchResultsWindow(
                    results=results,
                    search_criteria=search_criteria,
                    calculation_db_service=self.calculation_db_service,
                    custom_cipher_service=self.custom_cipher_service,
                    window_manager=self.window_manager
                )
                
                # Open the window using the window manager
                self.window_manager.open_window(window_id, results_window)
                
                logger.debug(f"Opened search results window with {len(results)} results")
                
            except Exception as e:
                logger.error(f"Error opening results window: {e}")
                import traceback
                traceback.print_exc()
        else:
            logger.warning("No window manager available to open results window")

    def _clear_search(self) -> None:
        """Clear all search criteria and results."""
        self.text_search.clear()
        self.exact_text_match.setChecked(False)
        self.exact_value.clear()
        self.language_combo.setCurrentIndex(0)
        self.method_combo.setCurrentIndex(0)
        self.favorites_only.setChecked(False)
        self.has_tags.setChecked(False)
        self.tag_combo.setEnabled(False)
        self.has_notes.setChecked(False)
        logger.debug("Search criteria cleared")

    def _on_has_tags_changed(self) -> None:
        """Handle has tags checkbox state change."""
        self.tag_combo.setEnabled(self.has_tags.isChecked())

    def _update_tag_combo(self) -> None:
        """Update the tag combo box with available tags."""
        self.tag_combo.clear()
        try:
            # Get all available tags from the database
            tags = self.calculation_db_service.get_all_tags()
            for tag in tags:
                # Handle both string tags and tag objects
                tag_name = tag.name if hasattr(tag, 'name') else str(tag)
                self.tag_combo.addItem(tag_name)
        except Exception as e:
            logger.error(f"Error loading tags: {e}")

    def set_exact_value(self, value: int) -> None:
        """Set the exact value field and perform a search.

        Args:
            value: The value to search for
        """
        self.exact_value.setText(str(value))
        # Clear other search criteria to focus on the value
        self.text_search.clear()
        # Perform the search
        self._perform_search() 