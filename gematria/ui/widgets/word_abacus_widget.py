"""
Purpose: Provides a beautifully redesigned widget for performing gematria calculations

This file is part of the gematria pillar and serves as a UI component.
It provides a modern, jazzy calculator interface that allows users to perform gematria
calculations on Hebrew, Greek, or English text using various methods.

Key improvements:
- Modern card-based layout with elegant shadows
- Enhanced visual hierarchy and typography
- Interactive elements with micro-animations
- Sophisticated color palette and theming
- Improved user flow and accessibility
- Beautiful gradient buttons and hover effects

Dependencies:
- PyQt6: For building the graphical user interface
- gematria.services.gematria_service: For performing the actual calculations
- gematria.models.calculation_type: For the available calculation methods

Related files:
- gematria/services/gematria_service.py: Service for performing calculations
- gematria/models/calculation_result.py: Model for storing calculation results
- gematria/ui/dialogs/custom_cipher_dialog.py: For managing custom ciphers
"""

from typing import List, Optional, Union, cast

from loguru import logger
from PyQt6.QtCore import pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QTimer
from PyQt6.QtWidgets import (
    QCheckBox,
    QComboBox,
    QFrame,
    QGridLayout,
    QGroupBox,
    QHBoxLayout,
    QLabel,
    QLineEdit,
    QMenu,
    QPushButton,
    QScrollArea,
    QSizePolicy,
    QSpacerItem,
    QSplitter,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
    QFileDialog,
    QMessageBox,
    QApplication,
)
from PyQt6.QtGui import QFont, QPalette, QColor, QPainter, QPen, QBrush, QLinearGradient
from PyQt6.QtCore import Qt

from gematria.models.calculation_result import CalculationResult
from gematria.models.calculation_type import CalculationType, Language
from gematria.models.custom_cipher_config import CustomCipherConfig, LanguageType
from gematria.services.custom_cipher_service import CustomCipherService
from gematria.services.gematria_service import GematriaService
from gematria.services.history_service import HistoryService
from gematria.ui.widgets.virtual_keyboard_widget import VirtualKeyboardWidget
from gematria.ui.dialogs.transliteration_help_dialog import TransliterationHelpDialog
from gematria.ui.dialogs.all_methods_results_dialog import AllMethodsResultsDialog

# Import the TQ analysis service for sending numbers to Quadset Analysis
try:
    from tq.services import tq_analysis_service
    TQ_AVAILABLE = True
except ImportError:
    TQ_AVAILABLE = False

# Define method type as a union of CalculationType and CustomCipherConfig
MethodType = Union[CalculationType, CustomCipherConfig]


class ModernCard(QFrame):
    """A modern card widget with elegant styling."""
    
    def __init__(self, title: str = "", parent=None):
        super().__init__(parent)
        self.setFrameStyle(QFrame.Shape.Box)
        self.setStyleSheet("""
            ModernCard {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 12px;
                margin: 8px;
            }
            ModernCard:hover {
                border-color: #4a90e2;
                background-color: #f8fbff;
            }
        """)
        
        # Main layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(20, 16, 20, 20)
        self.layout.setSpacing(12)
        
        # Title if provided
        if title:
            self.title_label = QLabel(title)
            self.title_label.setStyleSheet("""
                QLabel {
                    font-size: 16px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 8px;
                    padding-bottom: 8px;
                    border-bottom: 2px solid #e8f4fd;
                }
            """)
            self.layout.addWidget(self.title_label)


class GradientButton(QPushButton):
    """A beautiful gradient button with hover effects."""
    
    def __init__(self, text: str, color_scheme: str = "primary", parent=None):
        super().__init__(text, parent)
        self.color_scheme = color_scheme
        self.setMinimumHeight(44)
        self.setFont(QFont("Segoe UI", 10, QFont.Weight.Medium))
        
        # Color schemes
        schemes = {
            "primary": {
                "gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4a90e2, stop:1 #357abd)",
                "hover": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5ba0f2, stop:1 #4a90e2)",
                "pressed": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #357abd, stop:1 #2968a3)",
                "text": "#ffffff"
            },
            "success": {
                "gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #27ae60, stop:1 #219a52)",
                "hover": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2ecc71, stop:1 #27ae60)",
                "pressed": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #219a52, stop:1 #1e8449)",
                "text": "#ffffff"
            },
            "danger": {
                "gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e74c3c, stop:1 #c0392b)",
                "hover": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ec7063, stop:1 #e74c3c)",
                "pressed": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #c0392b, stop:1 #a93226)",
                "text": "#ffffff"
            },
            "secondary": {
                "gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #95a5a6, stop:1 #7f8c8d)",
                "hover": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a6b4b5, stop:1 #95a5a6)",
                "pressed": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #7f8c8d, stop:1 #6c7b7d)",
                "text": "#ffffff"
            }
        }
        
        scheme = schemes.get(color_scheme, schemes["primary"])
        
        self.setStyleSheet(f"""
            GradientButton {{
                background: {scheme["gradient"]};
                border: none;
                border-radius: 8px;
                color: {scheme["text"]};
                padding: 12px 24px;
                font-weight: 500;
                text-align: center;
            }}
            GradientButton:hover {{
                background: {scheme["hover"]};
            }}
            GradientButton:pressed {{
                background: {scheme["pressed"]};
            }}
            GradientButton:disabled {{
                background: #bdc3c7;
                color: #7f8c8d;
            }}
        """)


class ModernComboBox(QComboBox):
    """A modern styled combo box."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(40)
        self.setStyleSheet("""
            QComboBox {
                background-color: #ffffff;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 14px;
                color: #2c3e50;
                selection-background-color: #4a90e2;
            }
            QComboBox:focus {
                border-color: #4a90e2;
                background-color: #f8fbff;
            }
            QComboBox:hover {
                border-color: #74b9ff;
                background-color: #f8fbff;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #4a90e2;
                margin-right: 10px;
            }
            QComboBox QAbstractItemView {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                selection-background-color: #e8f4fd;
                selection-color: #2c3e50;
                padding: 4px;
            }
        """)


class ModernLineEdit(QLineEdit):
    """A modern styled line edit with floating label effect."""
    
    def __init__(self, placeholder: str = "", parent=None):
        super().__init__(parent)
        self.setMinimumHeight(44)
        self.setPlaceholderText(placeholder)
        self.setStyleSheet("""
            QLineEdit {
                background-color: #ffffff;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 12px 16px;
                font-size: 14px;
                color: #2c3e50;
                selection-background-color: #4a90e2;
                selection-color: #ffffff;
            }
            QLineEdit:focus {
                border-color: #4a90e2;
                background-color: #f8fbff;
            }
            QLineEdit:hover {
                border-color: #74b9ff;
                background-color: #f8fbff;
            }
        """)


class ModernCheckBox(QCheckBox):
    """A modern styled checkbox."""
    
    def __init__(self, text: str = "", parent=None):
        super().__init__(text, parent)
        self.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 2px solid #e1e8ed;
                border-radius: 4px;
                background-color: #ffffff;
            }
            QCheckBox::indicator:hover {
                border-color: #4a90e2;
                background-color: #f8fbff;
            }
            QCheckBox::indicator:checked {
                background-color: #4a90e2;
                border-color: #4a90e2;
                image: none;
            }
        """)


class ResultDisplay(QWidget):
    """A beautiful result display widget with context menu support."""
    
    # Signals for context menu actions
    send_to_quadset_requested = pyqtSignal(int, str)  # value, input_text
    send_to_polycalc_requested = pyqtSignal(int, str)  # value, input_text
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._current_value = 0
        self._current_input = ""
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Result container
        self.result_container = QFrame()
        self.result_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
            }
        """)
        
        # Enable context menu
        self.result_container.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.result_container.customContextMenuRequested.connect(self._show_context_menu)
        
        result_layout = QVBoxLayout(self.result_container)
        
        # Result label
        self.result_label = QLabel("Calculation Result")
        self.result_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 8px;
            }
        """)
        
        # Result value
        self.result_value = QLabel("0")
        self.result_value.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 36px;
                font-weight: 700;
                text-align: center;
            }
        """)
        self.result_value.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Add tooltip to indicate right-click functionality
        self.result_container.setToolTip("Right-click for more options")
        
        result_layout.addWidget(self.result_label)
        result_layout.addWidget(self.result_value)
        
        layout.addWidget(self.result_container)
        
    def set_result(self, value: int, input_text: str = ""):
        """Set the result value with animation."""
        self._current_value = value
        self._current_input = input_text
        self.result_value.setText(str(value))
        # Add a subtle pulse animation
        self.result_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #74b9ff, stop:1 #0984e3);
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
            }
        """)
        # Reset to original style after a moment
        QTimer.singleShot(300, self.reset_style)
        
    def reset_style(self):
        """Reset to original gradient style."""
        self.result_container.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1, 
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                padding: 20px;
                margin: 8px;
            }
        """)
        
    def _show_context_menu(self, position):
        """Show context menu for result actions."""
        if self._current_value == 0:
            return  # No context menu for zero values
            
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
        """)
        
        # Send to Quadset Analysis action
        quadset_action = menu.addAction("📊 Send to Quadset Analysis")
        quadset_action.triggered.connect(
            lambda: self.send_to_quadset_requested.emit(self._current_value, self._current_input)
        )
        
        # Send to PolyCalc action
        polycalc_action = menu.addAction("📐 Send to PolyCalc")
        polycalc_action.triggered.connect(
            lambda: self.send_to_polycalc_requested.emit(self._current_value, self._current_input)
        )
        
        # Show the menu
        global_pos = self.result_container.mapToGlobal(position)
        menu.exec(global_pos)


class WordAbacusWidget(QWidget):
    """Beautifully redesigned widget for calculating gematria values."""

    # Signal emitted when a calculation is performed
    calculation_performed = pyqtSignal(CalculationResult)
    
    # Signal emitted when save is requested (to be handled by parent panel)
    save_requested = pyqtSignal()
    
    # Signal emitted when import is requested (to be handled by parent panel)
    import_requested = pyqtSignal()

    # Signal emitted when custom cipher manager is requested
    custom_cipher_manager_requested = pyqtSignal()

    def __init__(
        self,
        calculation_service: GematriaService,
        custom_cipher_service: CustomCipherService,
        history_service: HistoryService,
    ) -> None:
        """Initialize the WordAbacusWidget.

        Args:
            calculation_service: Service for performing gematria calculations
            custom_cipher_service: Service for managing custom ciphers
            history_service: Service for managing calculation history
        """
        super().__init__()
        self._calculation_service = calculation_service
        self._custom_cipher_service = custom_cipher_service
        self._history_service = history_service

        # Set main widget styling
        self.setStyleSheet("""
            QWidget {
                background-color: #f8fafc;
                font-family: 'Segoe UI', 'Roboto', sans-serif;
            }
        """)

        # Initialize UI components
        self._setup_ui()

        # Connect signals
        self._connect_signals()

        # Set initial state
        self._update_ui()

    def _setup_ui(self) -> None:
        """Initialize the beautiful UI components with side-by-side pane layout."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Header section with beautiful title and action buttons
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 20)
        
        # Main title
        title_label = QLabel("✨ Word Abacus")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 32px;
                font-weight: 700;
                color: #2c3e50;
                text-align: center;
                margin-bottom: 8px;
            }
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Subtitle
        subtitle_label = QLabel("Discover the hidden numerical values in sacred texts")
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                text-align: center;
                margin-bottom: 20px;
            }
        """)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # Action buttons toolbar
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(12)
        
        # Import Word/Phrase List button
        self._import_list_button = GradientButton("📋 Import Word/Phrase List", "success")
        self._import_list_button.setToolTip("Import a list of words or phrases for batch calculation")
        
        # Help button
        self._help_button = GradientButton("❓ Help", "secondary")
        self._help_button.setToolTip("Show help and documentation")
        
        toolbar_layout.addWidget(self._import_list_button)
        toolbar_layout.addStretch()  # Push help button to the right
        toolbar_layout.addWidget(self._help_button)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_layout.addLayout(toolbar_layout)
        main_layout.addWidget(header_widget)

        # Create splitter for side-by-side layout
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e1e8ed;
                width: 2px;
                border-radius: 1px;
            }
            QSplitter::handle:hover {
                background-color: #4a90e2;
            }
        """)
        main_layout.addWidget(splitter)

        # Left pane: Input and Method selection
        left_pane = QWidget()
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        left_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        left_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        left_scroll.setWidget(left_pane)
        
        left_layout = QVBoxLayout(left_pane)
        left_layout.setSpacing(20)
        left_layout.setContentsMargins(0, 0, 10, 0)

        # Input section in a beautiful card
        input_card = ModernCard("📝 Text Input")
        input_layout = QVBoxLayout()
        
        # Language and transliteration row
        lang_row = QHBoxLayout()
        lang_row.setSpacing(16)
        
        # Language selection
        lang_label = QLabel("Language:")
        lang_label.setStyleSheet("font-weight: 500; color: #2c3e50; font-size: 14px;")
        self._language_combo = ModernComboBox()
        self._language_combo.addItems(["Hebrew", "Greek", "English", "Coptic", "Arabic"])
        
        # Transliteration toggle with help button
        transliteration_layout = QHBoxLayout()
        transliteration_layout.setSpacing(8)
        
        self._transliterate_chk = ModernCheckBox("Enable Transliteration")
        self._transliterate_chk.setToolTip(
            "Convert Latin characters to the selected script before calculation"
        )
        
        # Help button for transliteration mappings
        self._transliteration_help_btn = QPushButton("?")
        self._transliteration_help_btn.setMaximumSize(24, 24)
        self._transliteration_help_btn.setToolTip("Show transliteration mapping tables")
        self._transliteration_help_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 12px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self._transliteration_help_btn.clicked.connect(self._show_transliteration_help)
        
        transliteration_layout.addWidget(self._transliterate_chk)
        transliteration_layout.addWidget(self._transliteration_help_btn)
        transliteration_layout.addStretch()
        
        lang_row.addWidget(lang_label)
        lang_row.addWidget(self._language_combo, 1)
        lang_row.addLayout(transliteration_layout)
        
        # Input field row
        input_row = QHBoxLayout()
        input_row.setSpacing(12)
        
        self._input_field = ModernLineEdit("Enter your text here...")
        self._vk_button = GradientButton("⌨️", "secondary")
        self._vk_button.setMaximumWidth(50)
        self._vk_button.setToolTip("Open Virtual Keyboard")
        
        input_row.addWidget(self._input_field, 1)
        input_row.addWidget(self._vk_button)
        
        input_layout.addLayout(lang_row)
        input_layout.addLayout(input_row)
        input_card.layout.addLayout(input_layout)
        left_layout.addWidget(input_card)

        # Method selection card
        method_card = ModernCard("🔢 Calculation Method")
        method_layout = QVBoxLayout()
        
        # Method selection
        method_row = QHBoxLayout()
        method_label = QLabel("Method:")
        method_label.setStyleSheet("font-weight: 500; color: #2c3e50; font-size: 14px;")
        self._method_combo = ModernComboBox()
        
        method_row.addWidget(method_label)
        method_row.addWidget(self._method_combo, 1)
        
        method_layout.addLayout(method_row)
        method_card.layout.addLayout(method_layout)
        left_layout.addWidget(method_card)

        # Add stretch to push cards to top
        left_layout.addStretch()

        # Right pane: Calculate & Results and History
        right_pane = QWidget()
        right_scroll = QScrollArea()
        right_scroll.setWidgetResizable(True)
        right_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        right_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        right_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        right_scroll.setWidget(right_pane)
        
        right_layout = QVBoxLayout(right_pane)
        right_layout.setSpacing(20)
        right_layout.setContentsMargins(10, 0, 0, 0)

        # Action and result section
        action_card = ModernCard("🎯 Calculate & Results")
        action_layout = QVBoxLayout()
        
        # Action buttons row
        button_row = QHBoxLayout()
        button_row.setSpacing(12)
        
        self._calc_button = GradientButton("🧮 Calculate", "primary")
        self._calc_button.setEnabled(False)
        
        self._save_calc_button = GradientButton("💾 Save", "secondary")
        self._save_calc_button.setEnabled(False)
        self._save_calc_button.setToolTip("Save the current calculation result")
        
        button_row.addWidget(self._calc_button, 1)
        button_row.addWidget(self._save_calc_button, 1)
        
        # Result display
        self._result_display = ResultDisplay()
        
        action_layout.addLayout(button_row)
        action_layout.addWidget(self._result_display)
        action_card.layout.addLayout(action_layout)
        right_layout.addWidget(action_card)

        # History section
        history_card = ModernCard("📚 Calculation History")
        history_layout = QVBoxLayout()
        
        # History table with modern styling
        self._history_table = QTableWidget(0, 5)
        self._history_table.setHorizontalHeaderLabels(
            ["Input", "Method", "Result", "Time", "Notes"]
        )
        self._history_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self._history_table.customContextMenuRequested.connect(self._show_history_context_menu)
        self._history_table.setStyleSheet("""
            QTableWidget {
                background-color: #ffffff;
                border: 1px solid #e1e8ed;
                border-radius: 8px;
                gridline-color: #f1f3f4;
                selection-background-color: #e8f4fd;
                font-size: 13px;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1, 
                    stop:0 #f8fafc, stop:1 #e2e8f0);
                border: 1px solid #e1e8ed;
                padding: 8px 12px;
                font-weight: 600;
                color: #2c3e50;
            }
            QTableWidget::item {
                padding: 8px 12px;
                border-bottom: 1px solid #f1f3f4;
            }
            QTableWidget::item:selected {
                background-color: #e8f4fd;
                color: #2c3e50;
            }
        """)
        self._history_table.horizontalHeader().setStretchLastSection(True)
        
        # Clear history button
        clear_button = GradientButton("🗑️ Clear History", "danger")
        
        history_layout.addWidget(self._history_table)
        history_layout.addWidget(clear_button)
        history_card.layout.addLayout(history_layout)
        right_layout.addWidget(history_card)

        # Add stretch to push everything up
        right_layout.addStretch()

        # Add panes to splitter
        splitter.addWidget(left_scroll)
        splitter.addWidget(right_scroll)
        
        # Set equal sizes for both panes
        splitter.setSizes([400, 400])

        # Initialize the language-specific UI
        self._populate_methods_for_selected_language()
        self._language_combo.setCurrentIndex(0)  # Default to Hebrew

        # Track the virtual keyboard dialog
        self._virtual_keyboard = None
        self._update_virtual_keyboard_button()
        
        # Connect the clear button
        clear_button.clicked.connect(self.clear_history)

    def _populate_methods_for_selected_language(self) -> None:
        """Populate the methods combobox based on the selected language."""
        self._method_combo.clear()

        current_language_str = self._language_combo.currentText()
        language_enum: Optional[Language] = None

        if current_language_str == "Hebrew":
            language_enum = Language.HEBREW
        elif current_language_str == "Greek":
            language_enum = Language.GREEK
        elif current_language_str == "English":
            language_enum = Language.ENGLISH
        elif current_language_str == "Coptic":
            language_enum = Language.COPTIC
        elif current_language_str == "Arabic":
            language_enum = Language.ARABIC

        all_methods_for_combo: List[MethodType] = []

        if language_enum:
            # Add standard calculation types for the language
            standard_types = CalculationType.get_types_for_language(language_enum)
            all_methods_for_combo.extend(standard_types)

            # Add custom ciphers for the language
            try:
                lang_type_for_custom = LanguageType(
                    language_enum.value.lower()
                )
                custom_ciphers = self._custom_cipher_service.get_ciphers(
                    lang_type_for_custom
                )
                all_methods_for_combo.extend(cast(List[MethodType], custom_ciphers))
            except ValueError as e:
                logger.error(f"Could not get LanguageType for {language_enum}: {e}")

        # Add "All Methods" option first (if there are methods available)
        if all_methods_for_combo:
            self._method_combo.addItem("🌟 All Methods", "ALL_METHODS")
            
        # Populate the combo box with individual methods
        for method_item in all_methods_for_combo:
            if isinstance(method_item, CalculationType):
                self._method_combo.addItem(
                    method_item.display_name, method_item
                )
            elif isinstance(method_item, CustomCipherConfig):
                self._method_combo.addItem(method_item.name, method_item)

        if all_methods_for_combo:
            self._method_combo.setCurrentIndex(1)  # Select first actual method by default
        else:
            self._method_combo.addItem("No methods available", None)

    def _on_language_changed(self, language_index_or_text: Union[int, str]) -> None:
        """Handle language selection change."""
        if isinstance(language_index_or_text, int):
            language = self._language_combo.itemText(language_index_or_text)
        else:
            language = language_index_or_text

        self._input_field.setPlaceholderText(f"Enter {language} text...")
        self._input_field.clear()
        self._populate_methods_for_selected_language()
        self._update_virtual_keyboard_button()

    def _on_method_changed(self, index: int) -> None:
        """Handle calculation method changes."""
        pass

    def _calculate(self) -> None:
        """Handle calculate button click with beautiful animations."""
        logger.debug("WordAbacusWidget._calculate called")

        input_text = self._input_field.text().strip()
        if not input_text:
            return

        # Get selected calculation type
        selected_index = self._method_combo.currentIndex()
        calc_type = self._method_combo.itemData(selected_index)
        transliterate = self._transliterate_chk.isChecked()

        # Check if "All Methods" is selected
        if calc_type == "ALL_METHODS":
            # Get current language
            current_language_str = self._language_combo.currentText()
            language_enum = None
            
            if current_language_str == "Hebrew":
                language_enum = Language.HEBREW
            elif current_language_str == "Greek":
                language_enum = Language.GREEK
            elif current_language_str == "English":
                language_enum = Language.ENGLISH
            elif current_language_str == "Coptic":
                language_enum = Language.COPTIC
            elif current_language_str == "Arabic":
                language_enum = Language.ARABIC
            
            if language_enum:
                # Open the All Methods Results dialog
                dialog = AllMethodsResultsDialog(
                    input_text=input_text,
                    language=language_enum,
                    transliterate=transliterate,
                    parent=self
                )
                dialog.exec()
            return

        # Regular single method calculation
        result_value = self._calculation_service.calculate(
            input_text, calc_type, transliterate_input=transliterate
        )

        # Update result display with animation
        self._result_display.set_result(result_value, input_text)

        # Enable buttons that require a calculation result
        self._save_calc_button.setEnabled(True)

        # Create calculation result and add to history
        method_name = self._method_combo.currentText()
        if isinstance(calc_type, CustomCipherConfig):
            # Handle the case where method_name might be a tuple
            if isinstance(method_name, tuple) and len(method_name) > 0:
                clean_name = method_name[0]  # Extract the first element with clean display name
            else:
                clean_name = method_name
                
            result = CalculationResult(
                input_text=input_text,
                calculation_type="CUSTOM_CIPHER",
                result_value=result_value,
                custom_method_name=f"Custom: {clean_name}",
            )
        else:
            result = CalculationResult(
                input_text=input_text,
                calculation_type=calc_type,
                result_value=result_value,
            )

        # Add to history
        self._history_service.add_calculation(result)
        self._update_history_table()

        # Emit signal
        self.calculation_performed.emit(result)

    def _update_history_table(self) -> None:
        """Update the history table with the latest calculations."""
        history = self._history_service.get_history()
        self._history_table.setRowCount(0)

        for i, calc in enumerate(history):
            self._history_table.insertRow(i)
            display_dict = calc.to_display_dict()

            for j, column in enumerate(["Input", "Method", "Result", "Time", "Notes"]):
                item = QTableWidgetItem(display_dict[column])
                self._history_table.setItem(i, j, item)

        self._history_table.resizeColumnsToContents()

    def clear_history(self) -> None:
        """Clear the calculation history."""
        self._history_service.clear_history()
        self._update_history_table()

    def refresh_methods(self) -> None:
        """Refresh the available calculation methods (useful when custom ciphers are updated)."""
        current_selection = self._method_combo.currentIndex()
        self._populate_methods_for_selected_language()
        
        # Try to maintain the current selection if possible
        if current_selection < self._method_combo.count():
            self._method_combo.setCurrentIndex(current_selection)
        else:
            self._method_combo.setCurrentIndex(0)

    def reset_calculator(self) -> None:
        """Reset the calculator to its initial state."""
        self._input_field.clear()
        self._result_display.set_result(0)
        self._transliterate_chk.setChecked(False)
        self._populate_methods_for_selected_language()
        self._method_combo.setCurrentIndex(0)
        self._calc_button.setEnabled(False)
        self._save_calc_button.setEnabled(False)

    def _send_to_polygon_calculator(self, value: int = None, input_text: str = "") -> None:
        """Send the current calculation result to the Regular Polygon Calculator."""
        try:
            if value is not None:
                result_value = float(value)
            else:
                result_value = float(self._result_display.result_value.text())
        except ValueError:
            return

        from gematria.ui.dialogs.send_to_polygon_dialog import SendToPolygonDialog
        dialog = SendToPolygonDialog(result_value, self)
        dialog.exec()

    def _send_to_quadset_analysis(self, value: int = None, input_text: str = "") -> None:
        """Send the current calculation result to Quadset Analysis."""
        if not TQ_AVAILABLE:
            QMessageBox.warning(
                self,
                "Feature Unavailable",
                "The TQ module is not available in this installation.",
            )
            return

        try:
            if value is not None:
                result_value = int(value)
            else:
                result_value = int(self._result_display.result_value.text())
            
            # Open the TQ Grid with this number
            analysis_service = tq_analysis_service.get_instance()
            panel = analysis_service.open_quadset_analysis(result_value)
            
            # Find the window containing this panel and ensure it's on top
            parent = panel.window()
            if parent and hasattr(parent, "ensure_on_top"):
                parent.ensure_on_top()
                
            logger.info(f"Sent value {result_value} to Quadset Analysis")
            
        except ValueError:
            QMessageBox.warning(
                self,
                "Invalid Value",
                "Only integer values can be sent to Quadset Analysis.",
            )
            logger.warning("No valid calculation result to send to Quadset Analysis")
        except Exception as e:
            QMessageBox.critical(
                self,
                "Error",
                f"An error occurred while opening Quadset Analysis: {str(e)}",
            )
            logger.error(f"Error opening Quadset Analysis: {e}")

    def _save_calculation(self) -> None:
        """Request save of the current calculation result."""
        # Emit signal to request save dialog from parent panel
        self.save_requested.emit()

    def _import_word_phrase_list(self) -> None:
        """Request import of a list of words or phrases for batch calculation."""
        # Emit signal to request import dialog from parent panel
        self.import_requested.emit()

    def _show_help(self) -> None:
        """Show help and documentation."""
        help_text = """
        <h3>✨ Word Abacus Help</h3>
        
        <p><b>Getting Started:</b></p>
        <ul>
        <li>Select your desired language from the dropdown</li>
        <li>Enable transliteration if you want to input Latin characters</li>
        <li>Enter your text in the input field</li>
        <li>Choose a calculation method</li>
        <li>Click Calculate to see the result</li>
        </ul>
        
        <p><b>Features:</b></p>
        <ul>
        <li><b>💾 Save Calculation:</b> Save your calculation results</li>
        <li><b>📋 Import List:</b> Batch process multiple words/phrases</li>
        </ul>
        
        <p><b>Context Menus:</b></p>
        <ul>
        <li><b>Result Display:</b> Right-click on the calculation result to send to Quadset Analysis or PolyCalc</li>
        <li><b>History Table:</b> Right-click on any cell to copy, or on result values for send options</li>
        </ul>
        
        <p><b>Custom Ciphers:</b></p>
        <p>To create and manage custom calculation methods, use the Custom Ciphers button 
        on the main Gematria tab. Custom ciphers will automatically appear in the method dropdown.</p>
        
        <p><b>Transliteration:</b></p>
        <p>When enabled, Latin characters are converted to the selected script before calculation. 
        This follows the official transliteration rules documented in the Gematria Methods guide.</p>
        
        <p><b>Virtual Keyboard:</b></p>
        <p>Click the ⌨️ button to open a virtual keyboard for Hebrew, Greek, Coptic, or Arabic input.</p>
        """
        
        QMessageBox.information(self, "Word Abacus Help", help_text)

    def _connect_signals(self) -> None:
        """Connect UI signals to handlers."""
        self._language_combo.currentTextChanged.connect(self._on_language_changed)
        self._method_combo.currentTextChanged.connect(self._on_method_changed)
        self._input_field.textChanged.connect(self._on_input_changed)
        self._calc_button.clicked.connect(self._calculate)
        self._save_calc_button.clicked.connect(self._save_calculation)
        self._vk_button.clicked.connect(self._show_virtual_keyboard)
        
        # Toolbar button connections
        self._import_list_button.clicked.connect(self._import_word_phrase_list)
        self._help_button.clicked.connect(self._show_help)
        
        # Result display context menu connections
        self._result_display.send_to_quadset_requested.connect(self._send_to_quadset_analysis)
        self._result_display.send_to_polycalc_requested.connect(self._send_to_polygon_calculator)

    def _update_ui(self) -> None:
        """Update UI state based on current values."""
        self._populate_methods_for_selected_language()
        self._on_language_changed(self._language_combo.currentText())
        self._on_input_changed(self._input_field.text())

    def _update_virtual_keyboard_button(self) -> None:
        """Show or hide the virtual keyboard button based on selected language."""
        selected_language_text = self._language_combo.currentText()
        if selected_language_text in [
            Language.HEBREW.value,
            Language.GREEK.value,
            Language.COPTIC.value,
            Language.ARABIC.value,
        ]:
            self._vk_button.show()
        else:
            self._vk_button.hide()
            if self._virtual_keyboard and self._virtual_keyboard.isVisible():
                self._virtual_keyboard.hide()

    def _show_virtual_keyboard(self) -> None:
        """Show the virtual keyboard for the selected language."""
        current_language = self._language_combo.currentText()
        if current_language in [
            Language.HEBREW.value,
            Language.GREEK.value,
            Language.COPTIC.value,
            Language.ARABIC.value,
        ]:
            if (
                self._virtual_keyboard is None
                or self._virtual_keyboard.language != current_language
            ):
                self._virtual_keyboard = VirtualKeyboardWidget(
                    language=current_language, parent=self
                )
                self._virtual_keyboard.key_pressed.connect(self._handle_virtual_key)

            button_pos = self._vk_button.mapToGlobal(
                self._vk_button.rect().bottomLeft()
            )
            self._virtual_keyboard.move(button_pos.x(), button_pos.y())
            self._virtual_keyboard.show()
        else:
            if self._virtual_keyboard is not None:
                self._virtual_keyboard.hide()

    def _handle_virtual_key(self, key: str) -> None:
        """Handle key press from the virtual keyboard."""
        if key == "<BACKSPACE>":
            cursor_pos = self._input_field.cursorPosition()
            text = self._input_field.text()
            if cursor_pos > 0:
                text = text[: cursor_pos - 1] + text[cursor_pos:]
                self._input_field.setText(text)
                self._input_field.setCursorPosition(cursor_pos - 1)
        elif key == "<CLEAR>":
            self._input_field.clear()
        else:
            cursor_pos = self._input_field.cursorPosition()
            text = self._input_field.text()
            text = text[:cursor_pos] + key + text[cursor_pos:]
            self._input_field.setText(text)
            self._input_field.setCursorPosition(cursor_pos + len(key))

    def _show_transliteration_help(self) -> None:
        """Show the transliteration help dialog with mapping tables."""
        dialog = TransliterationHelpDialog(self)
        dialog.show()

    def _on_input_changed(self, text: str) -> None:
        """Handle input text changes."""
        self._calc_button.setEnabled(bool(text))

    def _show_history_context_menu(self, position):
        """Show context menu for history table actions."""
        if self._history_table.selectedIndexes():
            selected_index = self._history_table.selectedIndexes()[0]
            row = selected_index.row()
            column = selected_index.column()
            
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background-color: #ffffff;
                    border: 1px solid #e1e8ed;
                    border-radius: 8px;
                    padding: 4px;
                }
                QMenu::item {
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                QMenu::item:selected {
                    background-color: #e8f4fd;
                    color: #2c3e50;
                }
            """)
            
            # Copy action
            copy_action = menu.addAction("📋 Copy")
            copy_action.triggered.connect(
                lambda: self._copy_to_clipboard(row, column)
            )
            
            # If clicking on the Result column (column 2), add send options
            if column == 2:  # Result column
                menu.addSeparator()
                
                # Get the result value and input text
                result_item = self._history_table.item(row, 2)  # Result column
                input_item = self._history_table.item(row, 0)   # Input column
                
                if result_item and input_item:
                    try:
                        result_value = int(result_item.text())
                        input_text = input_item.text()
                        
                        # Send to Quadset Analysis action
                        quadset_action = menu.addAction("📊 Send to Quadset Analysis")
                        quadset_action.triggered.connect(
                            lambda: self._send_to_quadset_analysis(result_value, input_text)
                        )
                        
                        # Send to PolyCalc action
                        polycalc_action = menu.addAction("📐 Send to PolyCalc")
                        polycalc_action.triggered.connect(
                            lambda: self._send_to_polygon_calculator(result_value, input_text)
                        )
                    except ValueError:
                        pass  # Invalid result value, skip send options
            
            # Show the menu
            global_pos = self._history_table.mapToGlobal(position)
            menu.exec(global_pos)

    def _copy_to_clipboard(self, row: int, column: int) -> None:
        """Copy the selected history item to the clipboard."""
        if row >= 0 and column >= 0:
            selected_item = self._history_table.item(row, column)
            if selected_item:
                text = selected_item.text()
                clipboard = QApplication.clipboard()
                clipboard.setText(text)