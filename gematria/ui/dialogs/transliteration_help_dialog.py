"""Transliteration Help Dialog.

This module provides a dialog showing transliteration mapping tables for different languages.
"""

from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import (
    QDialog,
    QFrame,
    QHBoxLayout,
    QLabel,
    QPushButton,
    QScrollArea,
    QTabWidget,
    QTableWidget,
    QTableWidgetItem,
    QVBoxLayout,
    QWidget,
    QHeaderView,
)
from PyQt6.QtGui import QFont


class TransliterationHelpDialog(QDialog):
    """Dialog showing transliteration mapping tables for different languages."""

    def __init__(self, parent=None):
        """Initialize the dialog.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        self.setWindowTitle("Transliteration Mapping Reference")
        self.resize(700, 500)
        self.setModal(False)
        
        self._init_ui()

    def _init_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Transliteration Mapping Tables")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # Description
        desc_label = QLabel(
            "These tables show how Latin characters are mapped to different scripts "
            "when transliteration is enabled. Type the Latin character to get the "
            "corresponding character in the target script."
        )
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin: 10px 0;")
        layout.addWidget(desc_label)
        
        # Create tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # Create tabs for each language
        tab_widget.addTab(self._create_hebrew_tab(), "Hebrew")
        tab_widget.addTab(self._create_greek_tab(), "Greek") 
        tab_widget.addTab(self._create_coptic_tab(), "Coptic")
        tab_widget.addTab(self._create_arabic_tab(), "Arabic")
        
        # Close button
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        button_layout.addWidget(close_button)
        layout.addLayout(button_layout)

    def _create_mapping_table(self, mappings, title):
        """Create a mapping table widget.
        
        Args:
            mappings: Dictionary of latin -> target script mappings
            title: Title for the table
            
        Returns:
            QWidget containing the table
        """
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # Table title
        title_label = QLabel(title)
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Create table
        table = QTableWidget()
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["Latin Input", "Target Script"])
        
        # Set up table data
        sorted_mappings = sorted(mappings.items(), key=lambda x: x[0].lower())
        table.setRowCount(len(sorted_mappings))
        
        for row, (latin, target) in enumerate(sorted_mappings):
            # Latin character
            latin_item = QTableWidgetItem(latin)
            latin_item.setFont(QFont("Courier", 11))
            table.setItem(row, 0, latin_item)
            
            # Target script character
            target_item = QTableWidgetItem(target)
            target_font = QFont()
            target_font.setPointSize(14)
            target_item.setFont(target_font)
            table.setItem(row, 1, target_item)
        
        # Configure table appearance
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # Set vertical header visibility
        vertical_header = table.verticalHeader()
        if vertical_header:
            vertical_header.setVisible(False)
        
        # Resize columns
        header = table.horizontalHeader()
        if header:
            header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
            header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        
        # Style the table
        table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 8px;
                border: 1px solid #ddd;
                font-weight: bold;
            }
        """)
        
        layout.addWidget(table)
        return widget

    def _create_hebrew_tab(self):
        """Create the Hebrew transliteration tab."""
        hebrew_mappings = {
            "a": "א", "b": "ב", "g": "ג", "d": "ד", "h": "ה", "v": "ו",
            "z": "ז", "x": "ח", "j": "ט", "y": "י", "k": "כ", "K": "ך",
            "l": "ל", "m": "מ", "M": "ם", "n": "נ", "N": "ן", "s": "ס",
            "o": "ע", "p": "פ", "P": "ף", "c": "צ", "C": "ץ", "q": "ק",
            "r": "ר", "$": "ש", "t": "ת"
        }
        return self._create_mapping_table(hebrew_mappings, "Hebrew Script Mapping")

    def _create_greek_tab(self):
        """Create the Greek transliteration tab."""
        greek_mappings = {
            "a": "α", "b": "β", "g": "γ", "d": "δ", "e": "ε", "z": "ζ",
            "h": "η", "q": "θ", "i": "ι", "k": "κ", "l": "λ", "m": "μ",
            "n": "ν", "c": "ξ", "o": "ο", "p": "π", "r": "ρ", "s": "σ",
            "t": "τ", "u": "υ", "f": "φ", "x": "χ", "y": "ψ", "w": "ω"
        }
        return self._create_mapping_table(greek_mappings, "Greek Script Mapping")

    def _create_coptic_tab(self):
        """Create the Coptic transliteration tab."""
        coptic_mappings = {
            "a": "ⲁ", "b": "ⲃ", "g": "ⲅ", "d": "ⲇ", "e": "ⲉ", "so": "ⲋ",
            "z": "ⲍ", "h": "ⲏ", "th": "ⲑ", "i": "ⲓ", "k": "ⲕ", "l": "ⲗ",
            "m": "ⲙ", "n": "ⲛ", "ks": "ⲝ", "o": "ⲟ", "p": "ⲡ", "r": "ⲣ",
            "s": "ⲥ", "t": "ⲧ", "u": "ⲩ", "ph": "ⲫ", "kh": "ⲭ", "ps": "ⲯ",
            "w": "ⲱ", "sh": "ϣ", "f": "ϥ", "j": "ϫ", "ch": "ϭ", "ti": "ϯ"
        }
        return self._create_mapping_table(coptic_mappings, "Coptic Script Mapping")

    def _create_arabic_tab(self):
        """Create the Arabic transliteration tab."""
        arabic_mappings = {
            "a": "ا", "b": "ب", "j": "ج", "d": "د", "h": "ه", "w": "و",
            "z": "ز", "H": "ح", "T": "ط", "y": "ي", "k": "ك", "l": "ل",
            "m": "م", "n": "ن", "s": "س", "'": "ع", "f": "ف", "S": "ص",
            "q": "ق", "r": "ر", "sh": "ش", "t": "ت", "th": "ث", "kh": "خ",
            "dh": "ذ", "D": "ض", "Z": "ظ", "gh": "غ"
        }
        return self._create_mapping_table(arabic_mappings, "Arabic Script Mapping")
