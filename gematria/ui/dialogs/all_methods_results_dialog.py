"""
Dialog for displaying results from all calculation methods.
"""

import logging
from typing import List, Dict, Any, Optional
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLabel, QHeaderView, QGroupBox, QTextEdit, QCheckBox,
    QMessageBox, QProgressBar, QWidget, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
from PyQt6.QtGui import QFont

from gematria.models.calculation_result import CalculationResult
from gematria.models.calculation_type import CalculationType, Language
from gematria.models.custom_cipher_config import CustomCipherConfig, LanguageType
from gematria.services.gematria_service import GematriaService
from gematria.services.custom_cipher_service import CustomCipherService
from gematria.services.calculation_database_service import CalculationDatabaseService
from gematria.ui.dialogs.save_calculation_dialog import SaveCalculationDialog
from shared.services.service_locator import ServiceLocator

logger = logging.getLogger(__name__)


class AllMethodsCalculationThread(QThread):
    """Background thread for calculating all methods."""
    
    progress_updated = pyqtSignal(int, str)  # Progress percentage and current method
    calculation_completed = pyqtSignal(object, int)  # Result and value
    all_completed = pyqtSignal()
    
    def __init__(self, input_text: str, language: Language, transliterate: bool):
        super().__init__()
        self.input_text = input_text
        self.language = language
        self.transliterate = transliterate
        self.results = []
        
        # Get services
        self.calculation_service = ServiceLocator.get(GematriaService)
        self.custom_cipher_service = ServiceLocator.get(CustomCipherService)
        
    def run(self):
        """Run calculations for all methods."""
        try:
            # Get all standard methods for the language
            standard_methods = CalculationType.get_types_for_language(self.language)
            
            # Get custom ciphers for the language
            custom_ciphers = []
            try:
                lang_type = LanguageType(self.language.value.lower())
                custom_ciphers = self.custom_cipher_service.get_ciphers(lang_type)
            except (ValueError, Exception) as e:
                logger.warning(f"Could not get custom ciphers for {self.language}: {e}")
            
            all_methods = list(standard_methods) + list(custom_ciphers)
            total_methods = len(all_methods)
            
            if total_methods == 0:
                self.all_completed.emit()
                return
            
            # Calculate each method
            for i, method in enumerate(all_methods):
                if isinstance(method, CalculationType):
                    method_name = method.display_name
                    try:
                        result_value = self.calculation_service.calculate(
                            self.input_text, method, transliterate_input=self.transliterate
                        )
                        
                        # Create calculation result
                        calc_result = CalculationResult(
                            input_text=self.input_text,
                            calculation_type=method,
                            result_value=result_value
                        )
                        
                        # Emit signals
                        self.calculation_completed.emit(calc_result, result_value)
                        self.results.append((calc_result, result_value))
                        
                    except Exception as e:
                        logger.error(f"Error calculating {method_name}: {e}")
                        
                elif isinstance(method, CustomCipherConfig):
                    # Handle the case where method.name might be a tuple
                    if isinstance(method.name, tuple) and len(method.name) > 0:
                        clean_name = method.name[0]  # Extract the first element with clean display name
                    else:
                        clean_name = method.name
                    
                    method_name = f"Custom: {clean_name}"
                    try:
                        result_value = self.calculation_service.calculate(
                            self.input_text, method, transliterate_input=self.transliterate
                        )
                        
                        # Create calculation result
                        calc_result = CalculationResult(
                            input_text=self.input_text,
                            calculation_type="CUSTOM_CIPHER",
                            result_value=result_value,
                            custom_method_name=method_name
                        )
                        
                        # Emit signals
                        self.calculation_completed.emit(calc_result, result_value)
                        self.results.append((calc_result, result_value))
                        
                    except Exception as e:
                        logger.error(f"Error calculating {method_name}: {e}")
                
                # Update progress
                progress = int(((i + 1) / total_methods) * 100)
                self.progress_updated.emit(progress, method_name)
                
            self.all_completed.emit()
            
        except Exception as e:
            logger.error(f"Error in calculation thread: {e}")
            self.all_completed.emit()


class AllMethodsResultsDialog(QDialog):
    """Dialog for displaying and managing results from all calculation methods."""
    
    def __init__(self, input_text: str, language: Language, transliterate: bool, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"All Methods Results - '{input_text}'")
        self.setModal(True)
        self.resize(900, 700)
        
        self.input_text = input_text
        self.language = language
        self.transliterate = transliterate
        self.results = []  # List of (CalculationResult, result_value) tuples
        
        # Services
        self.db_service = ServiceLocator.get(CalculationDatabaseService)
        
        # UI setup
        self._init_ui()
        
        # Start calculations
        self._start_calculations()
        
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # Header section
        header_group = QGroupBox("Calculation Summary")
        header_layout = QVBoxLayout(header_group)
        
        # Input info
        input_label = QLabel(f"<b>Input:</b> {self.input_text}")
        input_label.setWordWrap(True)
        header_layout.addWidget(input_label)
        
        language_label = QLabel(f"<b>Language:</b> {self.language.value.title()}")
        header_layout.addWidget(language_label)
        
        transliterate_label = QLabel(f"<b>Transliteration:</b> {'Enabled' if self.transliterate else 'Disabled'}")
        header_layout.addWidget(transliterate_label)
        
        layout.addWidget(header_group)
        
        # Progress section
        self.progress_group = QGroupBox("Calculation Progress")
        progress_layout = QVBoxLayout(self.progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setMinimum(0)
        self.progress_bar.setMaximum(100)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("Preparing calculations...")
        progress_layout.addWidget(self.progress_label)
        
        layout.addWidget(self.progress_group)
        
        # Main content using splitter
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Results table
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels([
            "Method", "Value", "Type", "Select"
        ])
        
        # Configure table
        header = self.results_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        results_layout.addWidget(self.results_table)
        
        content_splitter.addWidget(results_group)
        
        # Batch save section
        save_group = QGroupBox("Batch Save Options")
        save_layout = QVBoxLayout(save_group)
        
        # Instructions
        instructions = QLabel("Select results above and configure shared settings:")
        instructions.setWordWrap(True)
        save_layout.addWidget(instructions)
        
        # Shared notes
        notes_label = QLabel("Shared Notes:")
        notes_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        save_layout.addWidget(notes_label)
        
        self.shared_notes = QTextEdit()
        self.shared_notes.setPlaceholderText("Enter notes to be applied to all selected calculations...")
        self.shared_notes.setMaximumHeight(100)
        save_layout.addWidget(self.shared_notes)
        
        # Favorite checkbox
        self.shared_favorite = QCheckBox("Mark all selected as favorites")
        save_layout.addWidget(self.shared_favorite)
        
        # Selection info
        self.selection_info = QLabel("Selected: 0 results")
        self.selection_info.setStyleSheet("color: #666; font-style: italic;")
        save_layout.addWidget(self.selection_info)
        
        content_splitter.addWidget(save_group)
        
        # Set splitter proportions
        content_splitter.setSizes([600, 300])
        layout.addWidget(content_splitter)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self._select_all)
        self.select_all_btn.setEnabled(False)
        button_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self._select_none)
        self.select_none_btn.setEnabled(False)
        button_layout.addWidget(self.select_none_btn)
        
        button_layout.addStretch()
        
        self.save_selected_btn = QPushButton("Save Selected Results")
        self.save_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #2ecc71;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #27ae60;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        self.save_selected_btn.clicked.connect(self._save_selected_results)
        self.save_selected_btn.setEnabled(False)
        button_layout.addWidget(self.save_selected_btn)
        
        close_btn = QPushButton("Close")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #34495e;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
        """)
        close_btn.clicked.connect(self.accept)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
    def _start_calculations(self):
        """Start the background calculation thread."""
        self.calculation_thread = AllMethodsCalculationThread(
            self.input_text, self.language, self.transliterate
        )
        
        # Connect signals
        self.calculation_thread.progress_updated.connect(self._on_progress_updated)
        self.calculation_thread.calculation_completed.connect(self._on_calculation_completed)
        self.calculation_thread.all_completed.connect(self._on_all_completed)
        
        # Start the thread
        self.calculation_thread.start()
        
    def _on_progress_updated(self, percentage: int, method_name: str):
        """Handle progress updates from the calculation thread."""
        self.progress_bar.setValue(percentage)
        self.progress_label.setText(f"Calculating: {method_name}")
        
    def _on_calculation_completed(self, calc_result: CalculationResult, result_value: int):
        """Handle completion of a single calculation."""
        # Add to results list
        self.results.append((calc_result, result_value))
        
        # Add to table
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)
        
        # Method name
        if hasattr(calc_result, 'custom_method_name') and calc_result.custom_method_name:
            custom_method = calc_result.custom_method_name
            # Handle the case when custom_method_name is a tuple
            if isinstance(custom_method, tuple) and len(custom_method) > 0:
                method_name = custom_method[0]  # Use the first element with the clean name
            else:
                method_name = custom_method
        else:
            method_name = calc_result.calculation_type.display_name
            
        self.results_table.setItem(row, 0, QTableWidgetItem(method_name))
        
        # Value
        value_item = QTableWidgetItem(str(result_value))
        value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 1, value_item)
        
        # Type
        if hasattr(calc_result, 'custom_method_name') and calc_result.custom_method_name:
            type_text = "Custom"
        else:
            type_text = "Standard"
        type_item = QTableWidgetItem(type_text)
        type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.results_table.setItem(row, 2, type_item)
        
        # Checkbox
        checkbox = QCheckBox()
        checkbox.setChecked(True)  # Selected by default
        checkbox.stateChanged.connect(self._update_selection_info)
        
        checkbox_widget = QWidget()
        checkbox_layout = QHBoxLayout(checkbox_widget)
        checkbox_layout.addWidget(checkbox)
        checkbox_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        checkbox_layout.setContentsMargins(0, 0, 0, 0)
        
        self.results_table.setCellWidget(row, 3, checkbox_widget)
        
        # Update selection info
        self._update_selection_info()
        
    def _on_all_completed(self):
        """Handle completion of all calculations."""
        self.progress_group.setTitle("Calculation Complete")
        self.progress_label.setText(f"Completed {len(self.results)} calculations")
        self.progress_bar.setValue(100)
        
        # Enable controls
        self.select_all_btn.setEnabled(True)
        self.select_none_btn.setEnabled(True)
        self.save_selected_btn.setEnabled(True)
        
        self._update_selection_info()
        
    def _update_selection_info(self):
        """Update the selection information label."""
        selected_count = 0
        for row in range(self.results_table.rowCount()):
            checkbox_widget = self.results_table.cellWidget(row, 3)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_count += 1
                    
        total_count = len(self.results)
        self.selection_info.setText(f"Selected: {selected_count} of {total_count} results")
        
        # Enable/disable save button based on selection
        self.save_selected_btn.setEnabled(selected_count > 0)
        
    def _select_all(self):
        """Select all results."""
        for row in range(self.results_table.rowCount()):
            checkbox_widget = self.results_table.cellWidget(row, 3)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(True)
                    
    def _select_none(self):
        """Deselect all results."""
        for row in range(self.results_table.rowCount()):
            checkbox_widget = self.results_table.cellWidget(row, 3)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(False)
                    
    def _save_selected_results(self):
        """Save all selected results with shared settings."""
        # Get selected results
        selected_results = []
        for row in range(self.results_table.rowCount()):
            checkbox_widget = self.results_table.cellWidget(row, 3)
            if checkbox_widget:
                checkbox = checkbox_widget.findChild(QCheckBox)
                if checkbox and checkbox.isChecked():
                    selected_results.append(self.results[row])
        
        if not selected_results:
            QMessageBox.warning(self, "No Selection", "Please select at least one result to save.")
            return
            
        # Get shared settings
        shared_notes = self.shared_notes.toPlainText().strip()
        is_favorite = self.shared_favorite.isChecked()
        
        # Show confirmation
        count = len(selected_results)
        reply = QMessageBox.question(
            self,
            "Confirm Batch Save",
            f"Save {count} calculation{'s' if count != 1 else ''} with shared settings?\n\n"
            f"Shared notes: {'Yes' if shared_notes else 'None'}\n"
            f"Mark as favorites: {'Yes' if is_favorite else 'No'}",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.Yes
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
            
        # Save each selected result
        saved_count = 0
        for calc_result, _ in selected_results:
            # Apply shared settings
            if shared_notes:
                calc_result.notes = shared_notes
            calc_result.favorite = is_favorite
            
            # Save to database
            success = self.db_service.save_calculation(calc_result)
            if success:
                saved_count += 1
            else:
                logger.error(f"Failed to save calculation: {calc_result}")
                
        # Show result
        if saved_count == count:
            QMessageBox.information(
                self,
                "Success",
                f"Successfully saved {saved_count} calculation{'s' if saved_count != 1 else ''}!"
            )
        else:
            QMessageBox.warning(
                self,
                "Partial Success",
                f"Saved {saved_count} of {count} calculations. Check logs for details."
            )
