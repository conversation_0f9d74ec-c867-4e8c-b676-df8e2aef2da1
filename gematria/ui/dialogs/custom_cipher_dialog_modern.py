"""Modern Custom Cipher Dialog.

This module provides a modernized dialog for creating and managing custom gematria ciphers
with improved UI/UX, tabbed interface, and enhanced functionality.
"""

from typing import Dict, List, Optional, Union

from loguru import logger
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QPropertyAnimation, QEasingCurve
from PyQt6.QtGui import QFont, QIcon, QPalette, QPixmap, QPainter, QColor
from PyQt6.QtWidgets import (
    QWidget,  # Changed from QDialog to QWidget for window management integration
    QVBoxLayout,
    QHBoxLayout,
    QTabWidget,
    QLabel,
    QLineEdit,
    QTextEdit,
    QComboBox,
    QCheckBox,
    QPushButton,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QGroupBox,
    QGridLayout,
    QFormLayout,
    QSpinBox,
    QScrollArea,
    QFrame,
    QSplitter,
    QMessageBox,
    QFile<PERSON>ialog,
    Q<PERSON>rogressBar,
    QApplication,
)

from gematria.models.custom_cipher_config import CustomCipherConfig
from gematria.services.custom_cipher_service import CustomCipherService


class ModernCustomCipherWidget(QWidget):
    """Modern custom cipher widget with tabbed interface.
    
    This widget is designed to be used within the window management system
    as content for an auxiliary window rather than as a standalone dialog.
    """

    # Signals
    cipher_updated = pyqtSignal(object)  # Emitted when a cipher is updated
    cipher_created = pyqtSignal(object)  # Emitted when a new cipher is created
    cipher_deleted = pyqtSignal(str)     # Emitted when a cipher is deleted
    close_requested = pyqtSignal()       # Emitted when user wants to close

    def __init__(self, parent: Optional[QWidget] = None) -> None:
        """Initialize the modern custom cipher widget.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)
        
        # Initialize services
        self.custom_cipher_service = CustomCipherService()
        
        # Current cipher being edited
        self.current_cipher: Optional[CustomCipherConfig] = None
        
        # UI state
        self.is_editing = False
        self.unsaved_changes = False
        
        # Initialize UI
        self._init_ui()
        self._apply_theme()
        self._load_ciphers()
        
        # Connect signals
        self._connect_signals()
        
        logger.debug("Modern Custom Cipher Widget initialized")

    def _language_display_to_enum(self, display_name: str) -> str:
        """Convert display language name to LanguageType enum value.
        
        Args:
            display_name: Display name like "Hebrew", "Greek", etc.
            
        Returns:
            Lowercase enum value like "hebrew", "greek", etc.
        """
        language_map = {
            "Hebrew": "hebrew",
            "Greek": "greek", 
            "English": "english",
            "Coptic": "coptic",
            "Arabic": "arabic"
        }
        return language_map.get(display_name, display_name.lower())

    def _language_enum_to_display(self, enum_value: str) -> str:
        """Convert LanguageType enum value to display name.
        
        Args:
            enum_value: Lowercase enum value like "hebrew", "greek", etc.
            
        Returns:
            Display name like "Hebrew", "Greek", etc.
        """
        display_map = {
            "hebrew": "Hebrew",
            "greek": "Greek",
            "english": "English", 
            "coptic": "Coptic",
            "arabic": "Arabic"
        }
        return display_map.get(enum_value, enum_value.capitalize())

    def _init_ui(self) -> None:
        """Initialize the user interface."""
        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(8)
        
        # Header section
        header_layout = self._create_header()
        layout.addLayout(header_layout)
        
        # Main content area with tabs
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        layout.addWidget(self.tab_widget)
        
        # Create tabs
        self._create_browse_tab()
        self._create_edit_tab()
        self._create_import_export_tab()
        
        # Footer with action buttons
        footer_layout = self._create_footer()
        layout.addLayout(footer_layout)
        
        # Set initial tab
        self.tab_widget.setCurrentIndex(0)

    def _create_header(self) -> QHBoxLayout:
        """Create the header section with title and quick actions.
        
        Returns:
            Header layout
        """
        layout = QHBoxLayout()
        
        # Title
        title_label = QLabel("Custom Cipher Manager")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        layout.addStretch()
        
        # Quick action buttons
        self.new_cipher_btn = QPushButton("New Cipher")
        self.new_cipher_btn.setIcon(QIcon(":/icons/add.png"))
        self.new_cipher_btn.clicked.connect(self._create_new_cipher)
        layout.addWidget(self.new_cipher_btn)
        
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.setIcon(QIcon(":/icons/refresh.png"))
        self.refresh_btn.clicked.connect(self._load_ciphers)
        layout.addWidget(self.refresh_btn)
        
        return layout

    def _create_browse_tab(self) -> None:
        """Create the browse/manage ciphers tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Search and filter section
        search_layout = QHBoxLayout()
        
        # Search box
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("Search ciphers...")
        self.search_edit.textChanged.connect(self._filter_ciphers)
        search_layout.addWidget(QLabel("Search:"))
        search_layout.addWidget(self.search_edit)
        
        # Language filter
        self.language_filter = QComboBox()
        self.language_filter.addItems(["All Languages", "Hebrew", "Greek", "English", "Coptic", "Arabic"])
        self.language_filter.currentTextChanged.connect(self._filter_ciphers)
        search_layout.addWidget(QLabel("Language:"))
        search_layout.addWidget(self.language_filter)
        
        search_layout.addStretch()
        layout.addLayout(search_layout)
        
        # Cipher list
        self.cipher_table = QTableWidget()
        self.cipher_table.setColumnCount(4)
        self.cipher_table.setHorizontalHeaderLabels(["Name", "Language", "Description", "Actions"])
        
        # Configure table
        header = self.cipher_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        self.cipher_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.cipher_table.setAlternatingRowColors(True)
        self.cipher_table.itemSelectionChanged.connect(self._on_cipher_selected)
        
        layout.addWidget(self.cipher_table)
        
        self.tab_widget.addTab(tab, "Browse & Manage")

    def _create_edit_tab(self) -> None:
        """Create the edit cipher tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Cipher info section
        info_group = QGroupBox("Cipher Information")
        info_layout = QFormLayout(info_group)
        
        self.name_edit = QLineEdit()
        self.name_edit.textChanged.connect(self._mark_unsaved_changes)
        info_layout.addRow("Name:", self.name_edit)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["Hebrew", "Greek", "English", "Coptic", "Arabic"])
        self.language_combo.currentTextChanged.connect(self._mark_unsaved_changes)
        self.language_combo.currentTextChanged.connect(self._on_language_changed)
        info_layout.addRow("Language:", self.language_combo)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.textChanged.connect(self._mark_unsaved_changes)
        info_layout.addRow("Description:", self.description_edit)
        
        layout.addWidget(info_group)
        
        # Cipher options section
        options_group = QGroupBox("Cipher Options")
        options_layout = QFormLayout(options_group)
        
        # Case sensitivity option
        self.case_sensitive_checkbox = QCheckBox("Case Sensitive")
        self.case_sensitive_checkbox.setToolTip(
            "When enabled, uppercase and lowercase letters can have different values.\n"
            "When disabled, the same value is used for both cases."
        )
        self.case_sensitive_checkbox.stateChanged.connect(self._on_case_sensitivity_changed)
        options_layout.addRow("", self.case_sensitive_checkbox)
        
        # Hebrew final forms option
        self.use_final_forms_checkbox = QCheckBox("Include Hebrew Final Forms")
        self.use_final_forms_checkbox.setToolTip(
            "Include Hebrew final letter forms (ך, ם, ן, ף, ץ) with separate values.\n"
            "Only available for Hebrew language."
        )
        self.use_final_forms_checkbox.stateChanged.connect(self._on_final_forms_changed)
        options_layout.addRow("", self.use_final_forms_checkbox)
        
        layout.addWidget(options_group)
        
        # Letter values section
        values_group = QGroupBox("Letter Values")
        values_layout = QVBoxLayout(values_group)
        
        # Quick preset buttons
        preset_layout = QHBoxLayout()
        
        self.standard_btn = QPushButton("Standard Values")
        self.standard_btn.clicked.connect(self._apply_standard_values)
        preset_layout.addWidget(self.standard_btn)
        
        self.ordinal_btn = QPushButton("Ordinal Values")
        self.ordinal_btn.clicked.connect(self._apply_ordinal_values)
        preset_layout.addWidget(self.ordinal_btn)
        
        self.clear_btn = QPushButton("Clear All")
        self.clear_btn.clicked.connect(self._clear_all_values)
        preset_layout.addWidget(self.clear_btn)
        
        preset_layout.addStretch()
        values_layout.addLayout(preset_layout)
        
        # Case sensitivity info label
        self.case_info_label = QLabel()
        self.case_info_label.setWordWrap(True)
        self.case_info_label.setStyleSheet("color: #666; font-style: italic; padding: 5px;")
        values_layout.addWidget(self.case_info_label)
        
        # Letter grid in scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(300)
        
        self.letter_grid_widget = QWidget()
        self.letter_grid_layout = QGridLayout(self.letter_grid_widget)
        self.letter_grid_layout.setSpacing(4)
        
        scroll_area.setWidget(self.letter_grid_widget)
        values_layout.addWidget(scroll_area)
        
        layout.addWidget(values_group)
        
        # Edit actions
        edit_actions_layout = QHBoxLayout()
        
        self.save_cipher_btn = QPushButton("Save Cipher")
        self.save_cipher_btn.setIcon(QIcon(":/icons/save.png"))
        self.save_cipher_btn.clicked.connect(self._save_current_cipher)
        self.save_cipher_btn.setEnabled(False)
        edit_actions_layout.addWidget(self.save_cipher_btn)
        
        self.clone_cipher_btn = QPushButton("Clone as New")
        self.clone_cipher_btn.setIcon(QIcon(":/icons/copy.png"))
        self.clone_cipher_btn.clicked.connect(self._clone_current_cipher)
        self.clone_cipher_btn.setEnabled(False)
        edit_actions_layout.addWidget(self.clone_cipher_btn)
        
        self.delete_cipher_btn = QPushButton("Delete Cipher")
        self.delete_cipher_btn.setIcon(QIcon(":/icons/delete.png"))
        self.delete_cipher_btn.clicked.connect(self._delete_current_cipher)
        self.delete_cipher_btn.setEnabled(False)
        edit_actions_layout.addWidget(self.delete_cipher_btn)
        
        edit_actions_layout.addStretch()
        layout.addLayout(edit_actions_layout)
        
        self.tab_widget.addTab(tab, "Edit Cipher")

    def _create_import_export_tab(self) -> None:
        """Create the import/export tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Import section
        import_group = QGroupBox("Import Ciphers")
        import_layout = QVBoxLayout(import_group)
        
        import_info = QLabel(
            "Import custom ciphers from JSON files. You can import individual cipher files "
            "or batch import multiple ciphers from a directory."
        )
        import_info.setWordWrap(True)
        import_layout.addWidget(import_info)
        
        import_buttons_layout = QHBoxLayout()
        
        self.import_file_btn = QPushButton("Import from File")
        self.import_file_btn.setIcon(QIcon(":/icons/import.png"))
        self.import_file_btn.clicked.connect(self._import_from_file)
        import_buttons_layout.addWidget(self.import_file_btn)
        
        self.import_dir_btn = QPushButton("Import from Directory")
        self.import_dir_btn.setIcon(QIcon(":/icons/folder.png"))
        self.import_dir_btn.clicked.connect(self._import_from_directory)
        import_buttons_layout.addWidget(self.import_dir_btn)
        
        import_buttons_layout.addStretch()
        import_layout.addLayout(import_buttons_layout)
        
        layout.addWidget(import_group)
        
        # Export section
        export_group = QGroupBox("Export Ciphers")
        export_layout = QVBoxLayout(export_group)
        
        export_info = QLabel(
            "Export your custom ciphers to JSON files for backup or sharing. "
            "You can export individual ciphers or all ciphers at once."
        )
        export_info.setWordWrap(True)
        export_layout.addWidget(export_info)
        
        export_buttons_layout = QHBoxLayout()
        
        self.export_selected_btn = QPushButton("Export Selected")
        self.export_selected_btn.setIcon(QIcon(":/icons/export.png"))
        self.export_selected_btn.clicked.connect(self._export_selected_cipher)
        self.export_selected_btn.setEnabled(False)
        export_buttons_layout.addWidget(self.export_selected_btn)
        
        self.export_all_btn = QPushButton("Export All")
        self.export_all_btn.setIcon(QIcon(":/icons/export.png"))
        self.export_all_btn.clicked.connect(self._export_all_ciphers)
        export_buttons_layout.addWidget(self.export_all_btn)
        
        export_buttons_layout.addStretch()
        export_layout.addLayout(export_buttons_layout)
        
        layout.addWidget(export_group)
        
        layout.addStretch()
        
        self.tab_widget.addTab(tab, "Import/Export")

    def _create_footer(self) -> QHBoxLayout:
        """Create the footer with main action buttons.
        
        Returns:
            Footer layout
        """
        layout = QHBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # Main action buttons
        self.help_btn = QPushButton("Help")
        self.help_btn.setIcon(QIcon(":/icons/help.png"))
        self.help_btn.clicked.connect(self._show_help)
        layout.addWidget(self.help_btn)
        
        self.close_btn = QPushButton("Close")
        self.close_btn.setIcon(QIcon(":/icons/close.png"))
        self.close_btn.clicked.connect(self._close_widget)
        layout.addWidget(self.close_btn)
        
        return layout

    def _apply_theme(self) -> None:
        """Apply theme styling to the widget."""
        # Modern card-style appearance
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 6px 12px;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #e0e0e0;
                border-color: #999999;
            }
            
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
            
            QPushButton:disabled {
                background-color: #f8f8f8;
                color: #999999;
                border-color: #e0e0e0;
            }
            
            QTabWidget::pane {
                border: 1px solid #cccccc;
                border-radius: 4px;
                margin-top: -1px;
            }
            
            QTabBar::tab {
                background-color: #f8f8f8;
                border: 1px solid #cccccc;
                border-bottom: none;
                border-radius: 4px 4px 0 0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            
            QTabBar::tab:hover {
                background-color: #f0f0f0;
            }
            
            QTableWidget {
                gridline-color: #e0e0e0;
                selection-background-color: #e3f2fd;
            }
            
            QLineEdit, QTextEdit, QComboBox {
                border: 1px solid #cccccc;
                border-radius: 4px;
                padding: 4px;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus {
                border-color: #2196f3;
            }
        """)

    def _connect_signals(self) -> None:
        """Connect internal signals."""
        # Tab change handling
        self.tab_widget.currentChanged.connect(self._on_tab_changed)

    def _load_ciphers(self) -> None:
        """Load and display available custom ciphers."""
        try:
            ciphers = self.custom_cipher_service.get_ciphers()
            self._populate_cipher_table(ciphers)
            self.status_label.setText(f"Loaded {len(ciphers)} custom ciphers")
            logger.debug(f"Loaded {len(ciphers)} custom ciphers")
        except Exception as e:
            logger.error(f"Error loading ciphers: {e}")
            self.status_label.setText("Error loading ciphers")
            QMessageBox.warning(self, "Error", f"Failed to load ciphers: {e}")

    def _populate_cipher_table(self, ciphers: List[CustomCipherConfig]) -> None:
        """Populate the cipher table with cipher data.
        
        Args:
            ciphers: List of cipher configurations
        """
        self.cipher_table.setRowCount(len(ciphers))
        
        for row, cipher in enumerate(ciphers):
            # Name
            name_item = QTableWidgetItem(cipher.name)
            name_item.setData(Qt.ItemDataRole.UserRole, cipher)
            self.cipher_table.setItem(row, 0, name_item)
            
            # Language
            language_item = QTableWidgetItem(cipher.language)
            self.cipher_table.setItem(row, 1, language_item)
            
            # Description
            description = cipher.description or "No description"
            if len(description) > 50:
                description = description[:47] + "..."
            desc_item = QTableWidgetItem(description)
            desc_item.setToolTip(cipher.description or "No description")
            self.cipher_table.setItem(row, 2, desc_item)
            
            # Actions
            actions_widget = self._create_action_buttons(cipher)
            self.cipher_table.setCellWidget(row, 3, actions_widget)

    def _create_action_buttons(self, cipher: CustomCipherConfig) -> QWidget:
        """Create action buttons for a cipher row.
        
        Args:
            cipher: Cipher configuration
            
        Returns:
            Widget containing action buttons
        """
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(4, 2, 4, 2)
        layout.setSpacing(2)
        
        # Edit button
        edit_btn = QPushButton("Edit")
        edit_btn.setMaximumWidth(50)
        edit_btn.clicked.connect(lambda: self._edit_cipher(cipher))
        layout.addWidget(edit_btn)
        
        # Clone button
        clone_btn = QPushButton("Clone")
        clone_btn.setMaximumWidth(50)
        clone_btn.clicked.connect(lambda: self._clone_cipher(cipher))
        layout.addWidget(clone_btn)
        
        # Delete button
        delete_btn = QPushButton("Del")
        delete_btn.setMaximumWidth(40)
        delete_btn.clicked.connect(lambda: self._delete_cipher(cipher))
        layout.addWidget(delete_btn)
        
        return widget

    def _filter_ciphers(self) -> None:
        """Filter ciphers based on search text and language."""
        search_text = self.search_edit.text().lower()
        language_filter = self.language_filter.currentText()
        
        for row in range(self.cipher_table.rowCount()):
            name_item = self.cipher_table.item(row, 0)
            language_item = self.cipher_table.item(row, 1)
            desc_item = self.cipher_table.item(row, 2)
            
            if name_item and language_item and desc_item:
                # Check search text
                text_match = (
                    search_text in name_item.text().lower() or
                    search_text in desc_item.text().lower()
                )
                
                # Check language filter
                language_match = (
                    language_filter == "All Languages" or
                    language_filter == language_item.text()
                )
                
                # Show/hide row
                show_row = text_match and language_match
                self.cipher_table.setRowHidden(row, not show_row)

    def _on_cipher_selected(self) -> None:
        """Handle cipher selection in the table."""
        selected_rows = self.cipher_table.selectionModel().selectedRows()
        
        if selected_rows:
            row = selected_rows[0].row()
            name_item = self.cipher_table.item(row, 0)
            if name_item:
                cipher = name_item.data(Qt.ItemDataRole.UserRole)
                self.export_selected_btn.setEnabled(True)
                logger.debug(f"Selected cipher: {cipher.name}")
        else:
            self.export_selected_btn.setEnabled(False)

    def _on_tab_changed(self, index: int) -> None:
        """Handle tab change events.
        
        Args:
            index: New tab index
        """
        if index == 1:  # Edit tab
            self._update_edit_tab_state()

    def _update_edit_tab_state(self) -> None:
        """Update the edit tab based on current cipher selection."""
        if self.current_cipher:
            self._populate_edit_form(self.current_cipher)
            self.save_cipher_btn.setEnabled(True)
            self.clone_cipher_btn.setEnabled(True)
            self.delete_cipher_btn.setEnabled(True)
        else:
            self._clear_edit_form()
            self.save_cipher_btn.setEnabled(False)
            self.clone_cipher_btn.setEnabled(False)
            self.delete_cipher_btn.setEnabled(False)

    def _populate_edit_form(self, cipher: CustomCipherConfig) -> None:
        """Populate the edit form with cipher data.
        
        Args:
            cipher: Cipher configuration to populate from
        """
        self.name_edit.setText(cipher.name)
        self.language_combo.setCurrentText(self._language_enum_to_display(cipher.language))
        self.description_edit.setPlainText(cipher.description)
        
        # Set checkbox states
        self.case_sensitive_checkbox.setChecked(cipher.case_sensitive)
        self.use_final_forms_checkbox.setChecked(cipher.use_final_forms)
        
        # Enable/disable Hebrew final forms based on language
        is_hebrew = cipher.language == "hebrew"
        self.use_final_forms_checkbox.setEnabled(is_hebrew)
        
        # Update case info label
        self._update_case_info_label()
        
        # Create letter grid
        self._create_letter_grid(cipher)
        
        self.unsaved_changes = False

    def _clear_edit_form(self) -> None:
        """Clear the edit form."""
        self.name_edit.clear()
        self.language_combo.setCurrentIndex(0)
        self.description_edit.clear()
        
        # Reset checkbox states
        self.case_sensitive_checkbox.setChecked(False)
        self.use_final_forms_checkbox.setChecked(False)
        self.use_final_forms_checkbox.setEnabled(True)  # Will be updated based on language
        
        # Clear case info label
        self.case_info_label.clear()
        
        # Clear letter grid
        self._clear_letter_grid()
        
        self.unsaved_changes = False

    def _create_letter_grid(self, cipher: CustomCipherConfig) -> None:
        """Create the letter value grid for editing.
        
        Args:
            cipher: Cipher configuration
        """
        # Clear existing grid
        self._clear_letter_grid()
        
        # Get letters for the language
        letters = self._get_letters_for_language(cipher.language, cipher.case_sensitive, cipher.use_final_forms)
        
        # Organize letters for better display
        if cipher.language == "english" and cipher.case_sensitive:
            # Separate uppercase and lowercase for English
            uppercase = [l for l in letters if l.isupper()]
            lowercase = [l for l in letters if l.islower()]
            organized_letters = [("Uppercase", uppercase), ("Lowercase", lowercase)]
        elif cipher.language == "hebrew" and cipher.use_final_forms:
            # Separate regular and final forms for Hebrew
            regular = [l for l in letters if l not in ["ך", "ם", "ן", "ף", "ץ"]]
            finals = [l for l in letters if l in ["ך", "ם", "ן", "ף", "ץ"]]
            organized_letters = [("Regular Forms", regular), ("Final Forms", finals)]
        elif cipher.language == "greek" and cipher.case_sensitive:
            # Separate uppercase and lowercase for Greek
            uppercase = [l for l in letters if l.isupper()]
            lowercase = [l for l in letters if l.islower()]
            organized_letters = [("Uppercase", uppercase), ("Lowercase", lowercase)]
        else:
            # Single group for other cases
            organized_letters = [("Letters", letters)]
        
        self.letter_inputs = {}
        current_row = 0
        
        for group_name, group_letters in organized_letters:
            if len(organized_letters) > 1:
                # Add group header
                group_label = QLabel(group_name)
                group_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
                self.letter_grid_layout.addWidget(group_label, current_row, 0, 1, 4)  # Span 4 columns
                current_row += 1
            
            # Create grid for this group (4 columns)
            cols = 4
            group_rows = (len(group_letters) + cols - 1) // cols
            
            for i, letter in enumerate(group_letters):
                row = current_row + (i // cols)
                col = i % cols
                
                # Letter label
                letter_label = QLabel(letter)
                letter_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                letter_label.setMinimumWidth(35)
                letter_label.setStyleSheet("font-size: 14px; font-weight: bold; padding: 2px;")
                self.letter_grid_layout.addWidget(letter_label, row * 2, col)
                
                # Value input
                value_input = QSpinBox()
                value_input.setRange(0, 9999)
                value_input.setValue(cipher.letter_values.get(letter, 0))
                value_input.valueChanged.connect(self._mark_unsaved_changes)
                value_input.setMinimumWidth(60)
                self.letter_inputs[letter] = value_input
                self.letter_grid_layout.addWidget(value_input, row * 2 + 1, col)
            
            current_row += group_rows * 2 + 1  # Add space between groups

    def _clear_letter_grid(self) -> None:
        """Clear the letter value grid."""
        # Remove all widgets from the grid
        while self.letter_grid_layout.count():
            child = self.letter_grid_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        self.letter_inputs = {}

    def _get_letters_for_language(self, language: str, case_sensitive: bool = False, use_final_forms: bool = False) -> List[str]:
        """Get the letters for a specific language.
        
        Args:
            language: Language name (display or enum value)
            case_sensitive: Whether to include both uppercase and lowercase letters
            use_final_forms: Whether to include Hebrew final forms
            
        Returns:
            List of letters for the language
        """
        # Convert to display name if it's an enum value
        if language.islower():
            language = self._language_enum_to_display(language)
            
        # Base language letters
        language_letters = {
            "Hebrew": ["א", "ב", "ג", "ד", "ה", "ו", "ז", "ח", "ט", "י", "כ", "ל", "מ", "נ", "ס", "ע", "פ", "צ", "ק", "ר", "ש", "ת"],
            "Greek": ["Α", "Β", "Γ", "Δ", "Ε", "Ζ", "Η", "Θ", "Ι", "Κ", "Λ", "Μ", "Ν", "Ξ", "Ο", "Π", "Ρ", "Σ", "Τ", "Υ", "Φ", "Χ", "Ψ", "Ω"],
            "English": [chr(i) for i in range(ord('A'), ord('Z') + 1)],
            "Coptic": ["Ⲁ", "Ⲃ", "Ⲅ", "Ⲇ", "Ⲉ", "Ⲋ", "Ⲍ", "Ⲏ", "Ⲑ", "Ⲓ", "Ⲕ", "Ⲗ", "Ⲙ", "Ⲛ", "Ⲝ", "Ⲟ", "Ⲡ", "Ⲣ", "Ⲥ", "Ⲧ", "Ⲩ", "Ⲫ", "Ⲭ", "Ⲯ", "Ⲱ"],
            "Arabic": ["ا", "ب", "ج", "د", "ه", "و", "ز", "ح", "ط", "ي", "ك", "ل", "م", "ن", "س", "ع", "ف", "ص", "ق", "ر", "ش", "ت", "ث", "خ", "ذ", "ض", "ظ", "غ"]
        }
        
        letters = language_letters.get(language, []).copy()
        
        # Handle Hebrew final forms
        if language == "Hebrew" and use_final_forms:
            hebrew_finals = ["ך", "ם", "ן", "ף", "ץ"]
            letters.extend(hebrew_finals)
        
        # Handle case sensitivity for applicable languages
        if case_sensitive and language in ["English", "Greek"]:
            if language == "English":
                # Add lowercase letters
                lowercase = [chr(i) for i in range(ord('a'), ord('z') + 1)]
                letters.extend(lowercase)
            elif language == "Greek":
                # Add lowercase Greek letters
                lowercase_greek = ["α", "β", "γ", "δ", "ε", "ζ", "η", "θ", "ι", "κ", "λ", "μ", "ν", "ξ", "ο", "π", "ρ", "σ", "τ", "υ", "φ", "χ", "ψ", "ω"]
                letters.extend(lowercase_greek)
        
        return letters

    def _mark_unsaved_changes(self) -> None:
        """Mark that there are unsaved changes."""
        self.unsaved_changes = True
        self.save_cipher_btn.setEnabled(True)

    def _create_new_cipher(self) -> None:
        """Create a new cipher."""
        # Switch to edit tab
        self.tab_widget.setCurrentIndex(1)
        
        # Clear form and set up for new cipher
        self.current_cipher = None
        self._clear_edit_form()
        
        # Set default values
        self.name_edit.setText("New Cipher")
        self.language_combo.setCurrentIndex(0)
        
        # Initialize checkboxes for new cipher
        self.case_sensitive_checkbox.setChecked(False)
        self.use_final_forms_checkbox.setChecked(False)
        
        # Update Hebrew final forms availability based on default language
        language = self.language_combo.currentText()
        is_hebrew = language == "Hebrew"
        self.use_final_forms_checkbox.setEnabled(is_hebrew)
        
        # Update case info label
        self._update_case_info_label()
        
        # Create empty letter grid
        language_enum = self._language_display_to_enum(language)
        letters = self._get_letters_for_language(
            language, 
            self.case_sensitive_checkbox.isChecked(),
            self.use_final_forms_checkbox.isChecked()
        )
        
        # Create a temporary cipher for the grid
        temp_cipher = CustomCipherConfig(
            name="New Cipher",
            language=language_enum
        )
        temp_cipher.case_sensitive = self.case_sensitive_checkbox.isChecked()
        temp_cipher.use_final_forms = self.use_final_forms_checkbox.isChecked()
        temp_cipher.letter_values = {letter: 0 for letter in letters}
        self._create_letter_grid(temp_cipher)
        
        self.is_editing = True
        self.status_label.setText("Creating new cipher")

    def _edit_cipher(self, cipher: CustomCipherConfig) -> None:
        """Edit an existing cipher.
        
        Args:
            cipher: Cipher to edit
        """
        self.current_cipher = cipher
        self.is_editing = True
        
        # Switch to edit tab
        self.tab_widget.setCurrentIndex(1)
        
        # Populate form
        self._populate_edit_form(cipher)
        
        self.status_label.setText(f"Editing cipher: {cipher.name}")

    def _clone_cipher(self, cipher: CustomCipherConfig) -> None:
        """Clone an existing cipher.
        
        Args:
            cipher: Cipher to clone
        """
        # Create a copy with modified name
        cloned_cipher = CustomCipherConfig(
            name=f"{cipher.name} (Copy)",
            language=cipher.language,
            description=cipher.description
        )
        cloned_cipher.case_sensitive = cipher.case_sensitive
        cloned_cipher.use_final_forms = cipher.use_final_forms
        cloned_cipher.letter_values = cipher.letter_values.copy()
        
        self.current_cipher = None  # This is a new cipher
        self.is_editing = True
        
        # Switch to edit tab
        self.tab_widget.setCurrentIndex(1)
        
        # Populate form with cloned data
        self._populate_edit_form(cloned_cipher)
        
        self.status_label.setText(f"Cloning cipher: {cipher.name}")

    def _clone_current_cipher(self) -> None:
        """Clone the currently selected cipher."""
        if self.current_cipher:
            self._clone_cipher(self.current_cipher)

    def _delete_cipher(self, cipher: CustomCipherConfig) -> None:
        """Delete a cipher.
        
        Args:
            cipher: Cipher to delete
        """
        reply = QMessageBox.question(
            self,
            "Delete Cipher",
            f"Are you sure you want to delete the cipher '{cipher.name}'?\n\nThis action cannot be undone.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.custom_cipher_service.delete_cipher(cipher.id)
                self.cipher_deleted.emit(cipher.name)
                self._load_ciphers()
                self.status_label.setText(f"Deleted cipher: {cipher.name}")
                logger.info(f"Deleted cipher: {cipher.name}")
                
                # Clear edit form if this was the current cipher
                if self.current_cipher and self.current_cipher.name == cipher.name:
                    self.current_cipher = None
                    self._clear_edit_form()
                    self._update_edit_tab_state()
                    
            except Exception as e:
                logger.error(f"Error deleting cipher: {e}")
                QMessageBox.critical(self, "Error", f"Failed to delete cipher: {e}")

    def _delete_current_cipher(self) -> None:
        """Delete the currently selected cipher."""
        if self.current_cipher:
            self._delete_cipher(self.current_cipher)

    def _save_current_cipher(self) -> None:
        """Save the current cipher being edited."""
        try:
            # Validate input
            name = self.name_edit.text().strip()
            if not name:
                QMessageBox.warning(self, "Validation Error", "Cipher name cannot be empty.")
                return
            
            language = self.language_combo.currentText()
            language_enum = self._language_display_to_enum(language)
            description = self.description_edit.toPlainText()
            
            # Get checkbox values
            case_sensitive = self.case_sensitive_checkbox.isChecked()
            use_final_forms = self.use_final_forms_checkbox.isChecked()
            
            # Collect letter values from the grid
            letter_values = {}
            for letter, input_widget in self.letter_inputs.items():
                value = input_widget.value()
                if value > 0:  # Only save non-zero values
                    letter_values[letter] = value
            
            if not letter_values:
                QMessageBox.warning(self, "Validation Error", "At least one letter must have a non-zero value.")
                return
            
            # Create or update cipher
            if self.current_cipher:
                # Update existing cipher
                cipher = self.current_cipher
                cipher.name = name
                cipher.language = language_enum
                cipher.description = description
                cipher.case_sensitive = case_sensitive
                cipher.use_final_forms = use_final_forms
                cipher.letter_values = letter_values
                
                # Update in service
                self.custom_cipher_service.update_cipher(cipher)
                self.cipher_updated.emit(cipher)
                action = "updated"
            else:
                # Create new cipher
                cipher = CustomCipherConfig(
                    name=name,
                    language=language_enum,
                    description=description
                )
                cipher.case_sensitive = case_sensitive
                cipher.use_final_forms = use_final_forms
                cipher.letter_values = letter_values
                
                # Save to service
                self.custom_cipher_service.save_cipher(cipher)
                self.cipher_created.emit(cipher)
                self.current_cipher = cipher
                action = "created"
            
            # Update UI state
            self.unsaved_changes = False
            self.save_cipher_btn.setEnabled(False)
            self._update_edit_tab_state()
            
            # Refresh cipher list
            self._load_ciphers()
            
            self.status_label.setText(f"Cipher {action}: {name}")
            logger.info(f"Cipher {action}: {name}")
            
        except Exception as e:
            logger.error(f"Error saving cipher: {e}")
            QMessageBox.critical(self, "Error", f"Failed to save cipher: {e}")

    def _apply_standard_values(self) -> None:
        """Apply standard gematria values to the current cipher."""
        language = self.language_combo.currentText()
        
        # Standard values for different languages
        if language == "Hebrew":
            standard_values = {
                "א": 1, "ב": 2, "ג": 3, "ד": 4, "ה": 5, "ו": 6, "ז": 7, "ח": 8, "ט": 9, "י": 10,
                "כ": 20, "ל": 30, "מ": 40, "נ": 50, "ס": 60, "ע": 70, "פ": 80, "צ": 90, "ק": 100,
                "ר": 200, "ש": 300, "ת": 400
            }
            
            # Add final forms if enabled
            if self.use_final_forms_checkbox.isChecked():
                standard_values.update({
                    "ך": 500, "ם": 600, "ן": 700, "ף": 800, "ץ": 900
                })
                
        elif language == "Greek":
            # Base Greek values
            standard_values = {
                "Α": 1, "Β": 2, "Γ": 3, "Δ": 4, "Ε": 5, "Ζ": 7, "Η": 8, "Θ": 9, "Ι": 10,
                "Κ": 20, "Λ": 30, "Μ": 40, "Ν": 50, "Ξ": 60, "Ο": 70, "Π": 80, "Ρ": 100,
                "Σ": 200, "Τ": 300, "Υ": 400, "Φ": 500, "Χ": 600, "Ψ": 700, "Ω": 800
            }
            
            # Add lowercase if case sensitive
            if self.case_sensitive_checkbox.isChecked():
                lowercase_values = {
                    "α": 1, "β": 2, "γ": 3, "δ": 4, "ε": 5, "ζ": 7, "η": 8, "θ": 9, "ι": 10,
                    "κ": 20, "λ": 30, "μ": 40, "ν": 50, "ξ": 60, "ο": 70, "π": 80, "ρ": 100,
                    "σ": 200, "τ": 300, "υ": 400, "φ": 500, "χ": 600, "ψ": 700, "ω": 800
                }
                standard_values.update(lowercase_values)
                
        elif language == "English":
            # Base English values (A=1, B=2, etc.)
            standard_values = {chr(i + ord('A')): i + 1 for i in range(26)}
            
            # Add lowercase if case sensitive
            if self.case_sensitive_checkbox.isChecked():
                lowercase_values = {chr(i + ord('a')): i + 1 for i in range(26)}
                standard_values.update(lowercase_values)
        else:
            # For other languages, use ordinal values
            self._apply_ordinal_values()
            return
        
        # Apply the values to the input widgets
        for letter, value in standard_values.items():
            if letter in self.letter_inputs:
                self.letter_inputs[letter].setValue(value)
        
        self._mark_unsaved_changes()

    def _apply_ordinal_values(self) -> None:
        """Apply ordinal values (A=1, B=2, etc.) to letters."""
        for i, (letter, input_widget) in enumerate(self.letter_inputs.items()):
            # Apply sequential values starting from 1
            input_widget.setValue(i + 1)
        
        self._mark_unsaved_changes()

    def _clear_all_values(self) -> None:
        """Clear all letter values."""
        for input_widget in self.letter_inputs.values():
            input_widget.setValue(0)
        
        self._mark_unsaved_changes()

    def _import_from_file(self) -> None:
        """Import cipher from a JSON file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Import Cipher",
            "",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                cipher = self.custom_cipher_service.import_cipher_from_file(file_path)
                self.cipher_created.emit(cipher)
                self._load_ciphers()
                self.status_label.setText(f"Imported cipher: {cipher.name}")
                logger.info(f"Imported cipher from: {file_path}")
            except Exception as e:
                logger.error(f"Error importing cipher: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import cipher: {e}")

    def _import_from_directory(self) -> None:
        """Import multiple ciphers from a directory."""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Directory")
        
        if dir_path:
            try:
                imported_count = self.custom_cipher_service.import_ciphers_from_directory(dir_path)
                self._load_ciphers()
                self.status_label.setText(f"Imported {imported_count} ciphers")
                logger.info(f"Imported {imported_count} ciphers from: {dir_path}")
            except Exception as e:
                logger.error(f"Error importing ciphers: {e}")
                QMessageBox.critical(self, "Import Error", f"Failed to import ciphers: {e}")

    def _export_selected_cipher(self) -> None:
        """Export the selected cipher."""
        selected_rows = self.cipher_table.selectionModel().selectedRows()
        
        if not selected_rows:
            QMessageBox.information(self, "No Selection", "Please select a cipher to export.")
            return
        
        row = selected_rows[0].row()
        name_item = self.cipher_table.item(row, 0)
        cipher = name_item.data(Qt.ItemDataRole.UserRole)
        
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Export Cipher",
            f"{cipher.name}.json",
            "JSON Files (*.json);;All Files (*)"
        )
        
        if file_path:
            try:
                self.custom_cipher_service.export_cipher_to_file(cipher, file_path)
                self.status_label.setText(f"Exported cipher: {cipher.name}")
                logger.info(f"Exported cipher to: {file_path}")
            except Exception as e:
                logger.error(f"Error exporting cipher: {e}")
                QMessageBox.critical(self, "Export Error", f"Failed to export cipher: {e}")

    def _export_all_ciphers(self) -> None:
        """Export all ciphers to a directory."""
        dir_path = QFileDialog.getExistingDirectory(self, "Select Export Directory")
        
        if dir_path:
            try:
                exported_count = self.custom_cipher_service.export_all_ciphers_to_directory(dir_path)
                self.status_label.setText(f"Exported {exported_count} ciphers")
                logger.info(f"Exported {exported_count} ciphers to: {dir_path}")
            except Exception as e:
                logger.error(f"Error exporting ciphers: {e}")
                QMessageBox.critical(self, "Export Error", f"Failed to export ciphers: {e}")

    def _show_help(self) -> None:
        """Show comprehensive help menu."""
        self._show_help_menu()

    def _show_help_menu(self) -> None:
        """Show the main help menu with options."""
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel
        
        help_dialog = QDialog(self)
        help_dialog.setWindowTitle("Custom Cipher Manager - Help")
        help_dialog.setModal(True)
        help_dialog.resize(500, 400)
        
        layout = QVBoxLayout(help_dialog)
        
        # Title
        title = QLabel("📚 Custom Cipher Manager Help")
        title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin: 10px;")
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # Description
        desc = QLabel(
            "Welcome to the enhanced Custom Cipher Manager! 🚀\n\n"
            "This tool allows you to create, edit, and manage custom gematria ciphers "
            "with advanced features like case sensitivity and Hebrew final forms support.\n\n"
            "Choose a help topic below:"
        )
        desc.setWordWrap(True)
        desc.setStyleSheet("margin: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;")
        layout.addWidget(desc)
        
        # Help topic buttons
        buttons_layout = QVBoxLayout()
        
        help_topics = [
            ("🎯 Getting Started Guide", self._show_getting_started_help),
            ("📝 Creating & Editing Ciphers", self._show_editing_help),
            ("🔤 Enhanced Features (Case & Finals)", self._show_enhanced_features_help),
            ("📊 Browse & Manage Ciphers", self._show_browse_help),
            ("📁 Import & Export", self._show_import_export_help),
            ("💡 Tips & Best Practices", self._show_tips_help),
            ("🔧 Troubleshooting", self._show_troubleshooting_help)
        ]
        
        for topic_name, callback in help_topics:
            btn = QPushButton(topic_name)
            btn.setMinimumHeight(35)
            btn.clicked.connect(lambda checked, cb=callback: cb())
            btn.setStyleSheet("""
                QPushButton {
                    text-align: left;
                    padding: 8px 15px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background-color: #ffffff;
                }
                QPushButton:hover {
                    background-color: #f0f8ff;
                    border-color: #2196f3;
                }
            """)
            buttons_layout.addWidget(btn)
        
        layout.addLayout(buttons_layout)
        
        # Close button
        close_layout = QHBoxLayout()
        close_layout.addStretch()
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(help_dialog.close)
        close_layout.addWidget(close_btn)
        layout.addLayout(close_layout)
        
        help_dialog.exec()

    def _show_getting_started_help(self) -> None:
        """Show getting started help."""
        help_text = """
        <h2>🎯 Getting Started with Custom Cipher Manager</h2>
        
        <h3>What is a Custom Cipher?</h3>
        <p>A custom cipher is a user-defined system for assigning numerical values to letters. 
        This allows you to create your own gematria systems beyond the traditional Hebrew, Greek, 
        and English systems.</p>
        
        <h3>Quick Start Steps:</h3>
        <ol>
        <li><b>Create a New Cipher:</b> Click "New Cipher" or go to the Edit tab</li>
        <li><b>Set Basic Info:</b> Enter name, select language, add description</li>
        <li><b>Choose Options:</b> Enable case sensitivity or Hebrew final forms if needed</li>
        <li><b>Assign Values:</b> Use presets or manually set letter values</li>
        <li><b>Save:</b> Click "Save Cipher" to store your creation</li>
        </ol>
        
        <h3>Interface Overview:</h3>
        <ul>
        <li><b>Browse & Manage Tab:</b> View, search, and manage existing ciphers</li>
        <li><b>Edit Cipher Tab:</b> Create new ciphers or modify existing ones</li>
        <li><b>Import/Export Tab:</b> Backup and share your ciphers</li>
        </ul>
        
        <h3>Your First Cipher:</h3>
        <p>Try creating a simple English cipher:</p>
        <ol>
        <li>Click "New Cipher"</li>
        <li>Name it "My First Cipher"</li>
        <li>Select "English" language</li>
        <li>Click "Standard Values" to apply A=1, B=2, etc.</li>
        <li>Click "Save Cipher"</li>
        </ol>
        
        <p><b>🎉 Congratulations!</b> Your cipher is now available in all Word Abacus widgets!</p>
        """
        
        self._show_help_dialog("Getting Started Guide", help_text)

    def _show_editing_help(self) -> None:
        """Show cipher editing help."""
        help_text = """
        <h2>📝 Creating & Editing Ciphers</h2>
        
        <h3>Cipher Information Section:</h3>
        <ul>
        <li><b>Name:</b> Give your cipher a descriptive, unique name</li>
        <li><b>Language:</b> Choose from Hebrew, Greek, English, Coptic, or Arabic</li>
        <li><b>Description:</b> Add notes about the cipher's purpose or source</li>
        </ul>
        
        <h3>Letter Values Section:</h3>
        <p>This is where you assign numerical values to each letter:</p>
        <ul>
        <li><b>Manual Entry:</b> Click on any number field and type a value (0-9999)</li>
        <li><b>Quick Presets:</b> Use the preset buttons for common patterns</li>
        <li><b>Visual Organization:</b> Letters are grouped by type (uppercase/lowercase, regular/final)</li>
        </ul>
        
        <h3>Preset Buttons:</h3>
        <ul>
        <li><b>Standard Values:</b> Applies traditional gematria values for the language</li>
        <li><b>Ordinal Values:</b> Applies sequential numbering (A=1, B=2, C=3, etc.)</li>
        <li><b>Clear All:</b> Sets all values to 0</li>
        </ul>
        
        <h3>Editing Tips:</h3>
        <ul>
        <li>Values of 0 are ignored in calculations</li>
        <li>At least one letter must have a non-zero value to save</li>
        <li>Changes are marked with an asterisk until saved</li>
        <li>Use Tab key to quickly move between value fields</li>
        </ul>
        
        <h3>Cloning Ciphers:</h3>
        <p>To create variations of existing ciphers:</p>
        <ol>
        <li>Select a cipher in the Browse tab</li>
        <li>Click "Clone" to create a copy</li>
        <li>Modify the copy as needed</li>
        <li>Save with a new name</li>
        </ol>
        """
        
        self._show_help_dialog("Creating & Editing Ciphers", help_text)

    def _show_enhanced_features_help(self) -> None:
        """Show enhanced features help."""
        help_text = """
        <h2>🔤 Enhanced Features: Case Sensitivity & Hebrew Final Forms</h2>
        
        <h3>🔠 Case Sensitivity (English & Greek)</h3>
        <p>When enabled, uppercase and lowercase letters can have different values:</p>
        
        <h4>How it Works:</h4>
        <ul>
        <li><b>Enabled:</b> 'A' and 'a' can have different values (e.g., A=10, a=1)</li>
        <li><b>Disabled:</b> 'A' and 'a' use the same value (traditional behavior)</li>
        <li><b>Visual:</b> Letters are separated into "Uppercase" and "Lowercase" sections</li>
        </ul>
        
        <h4>Use Cases:</h4>
        <ul>
        <li>Modern cipher systems that distinguish case</li>
        <li>Emphasis-based calculations (capitals = higher values)</li>
        <li>Mixed-case text analysis</li>
        </ul>
        
        <h3>📜 Hebrew Final Forms</h3>
        <p>Hebrew has 5 letters with special final forms used at word endings:</p>
        
        <h4>The Final Forms:</h4>
        <ul>
        <li><b>ך</b> (Final Kaf) - Traditional value: 500</li>
        <li><b>ם</b> (Final Mem) - Traditional value: 600</li>
        <li><b>ן</b> (Final Nun) - Traditional value: 700</li>
        <li><b>ף</b> (Final Peh) - Traditional value: 800</li>
        <li><b>ץ</b> (Final Tzadi) - Traditional value: 900</li>
        </ul>
        
        <h4>When to Use:</h4>
        <ul>
        <li><b>Enabled:</b> For traditional Hebrew gematria accuracy</li>
        <li><b>Disabled:</b> For simplified Hebrew calculations</li>
        <li><b>Visual:</b> Letters are separated into "Regular Forms" and "Final Forms"</li>
        </ul>
        
        <h3>🎯 Smart Presets:</h3>
        <p>The preset buttons automatically adapt to your settings:</p>
        <ul>
        <li><b>Case Sensitive:</b> Applies different values to upper/lowercase</li>
        <li><b>Final Forms:</b> Includes traditional final form values</li>
        <li><b>Combined:</b> Both features work together seamlessly</li>
        </ul>
        
        <h3>💡 Pro Tips:</h3>
        <ul>
        <li>Enable case sensitivity for modern language analysis</li>
        <li>Use Hebrew final forms for scholarly accuracy</li>
        <li>The info label shows current settings and behavior</li>
        <li>Grid organization adapts automatically to your choices</li>
        </ul>
        """
        
        self._show_help_dialog("Enhanced Features", help_text)

    def _show_browse_help(self) -> None:
        """Show browse and manage help."""
        help_text = """
        <h2>📊 Browse & Manage Ciphers</h2>
        
        <h3>Cipher Table:</h3>
        <p>The main table shows all your custom ciphers with:</p>
        <ul>
        <li><b>Name:</b> The cipher's display name</li>
        <li><b>Language:</b> Which language system it uses</li>
        <li><b>Description:</b> Brief description (hover for full text)</li>
        <li><b>Actions:</b> Quick action buttons for each cipher</li>
        </ul>
        
        <h3>Search & Filter:</h3>
        <ul>
        <li><b>Search Box:</b> Type to filter by name or description</li>
        <li><b>Language Filter:</b> Show only ciphers for specific languages</li>
        <li><b>Real-time:</b> Results update as you type</li>
        </ul>
        
        <h3>Action Buttons:</h3>
        <ul>
        <li><b>Edit:</b> Open the cipher in the Edit tab for modification</li>
        <li><b>Clone:</b> Create a copy with "(Copy)" added to the name</li>
        <li><b>Del:</b> Delete the cipher (with confirmation prompt)</li>
        </ul>
        
        <h3>Selection Features:</h3>
        <ul>
        <li><b>Click a row:</b> Select a cipher for export</li>
        <li><b>Double-click:</b> Quickly open for editing</li>
        <li><b>Keyboard navigation:</b> Use arrow keys to navigate</li>
        </ul>
        
        <h3>Management Tips:</h3>
        <ul>
        <li>Use descriptive names for easy identification</li>
        <li>Add meaningful descriptions to remember cipher purposes</li>
        <li>Regular cleanup: delete unused or test ciphers</li>
        <li>Use the search to quickly find specific ciphers</li>
        </ul>
        
        <h3>Sorting & Organization:</h3>
        <ul>
        <li>Table automatically sorts alphabetically by name</li>
        <li>Use consistent naming conventions (e.g., "Hebrew - Traditional")</li>
        <li>Group related ciphers with similar prefixes</li>
        </ul>
        """
        
        self._show_help_dialog("Browse & Manage", help_text)

    def _show_import_export_help(self) -> None:
        """Show import/export help."""
        help_text = """
        <h2>📁 Import & Export</h2>
        
        <h3>Why Import/Export?</h3>
        <ul>
        <li><b>Backup:</b> Protect your custom ciphers from data loss</li>
        <li><b>Sharing:</b> Share ciphers with colleagues or students</li>
        <li><b>Migration:</b> Move ciphers between different installations</li>
        <li><b>Collaboration:</b> Work with cipher sets from others</li>
        </ul>
        
        <h3>📥 Import Options:</h3>
        
        <h4>Import from File:</h4>
        <ul>
        <li>Import a single cipher from a JSON file</li>
        <li>Supports .json files created by the export function</li>
        <li>Automatically validates cipher format</li>
        <li>Handles name conflicts by prompting for new names</li>
        </ul>
        
        <h4>Import from Directory:</h4>
        <ul>
        <li>Batch import multiple cipher files from a folder</li>
        <li>Processes all .json files in the selected directory</li>
        <li>Shows progress and summary of imported ciphers</li>
        <li>Skips invalid or duplicate files</li>
        </ul>
        
        <h3>📤 Export Options:</h3>
        
        <h4>Export Selected:</h4>
        <ul>
        <li>Export a single cipher to a JSON file</li>
        <li>Select a cipher in the Browse tab first</li>
        <li>Choose filename and location</li>
        <li>Creates a portable, shareable file</li>
        </ul>
        
        <h4>Export All:</h4>
        <ul>
        <li>Export all ciphers to separate files in a directory</li>
        <li>Each cipher becomes its own .json file</li>
        <li>Filenames based on cipher names</li>
        <li>Perfect for complete backups</li>
        </ul>
        
        <h3>📋 File Format:</h3>
        <p>Exported files are in JSON format containing:</p>
        <ul>
        <li>Cipher name and description</li>
        <li>Language and options (case sensitivity, final forms)</li>
        <li>Complete letter-to-value mappings</li>
        <li>Metadata for validation</li>
        </ul>
        
        <h3>💡 Best Practices:</h3>
        <ul>
        <li><b>Regular Backups:</b> Export all ciphers monthly</li>
        <li><b>Organized Storage:</b> Keep exports in dated folders</li>
        <li><b>Version Control:</b> Export before major changes</li>
        <li><b>Documentation:</b> Include readme files with cipher collections</li>
        </ul>
        
        <h3>🔧 Troubleshooting:</h3>
        <ul>
        <li><b>Import Fails:</b> Check file format and permissions</li>
        <li><b>Name Conflicts:</b> Rename conflicting ciphers</li>
        <li><b>Missing Files:</b> Verify file paths and extensions</li>
        <li><b>Corrupted Data:</b> Use backup files</li>
        </ul>
        """
        
        self._show_help_dialog("Import & Export", help_text)

    def _show_tips_help(self) -> None:
        """Show tips and best practices help."""
        help_text = """
        <h2>💡 Tips & Best Practices</h2>
        
        <h3>🎯 Cipher Design Tips:</h3>
        <ul>
        <li><b>Meaningful Names:</b> Use descriptive names like "Hebrew Traditional" or "English Ordinal"</li>
        <li><b>Clear Descriptions:</b> Document the cipher's purpose, source, or special features</li>
        <li><b>Consistent Values:</b> Follow logical patterns for easier memorization</li>
        <li><b>Test First:</b> Create test ciphers before finalizing important ones</li>
        </ul>
        
        <h3>📚 Historical Accuracy:</h3>
        <ul>
        <li><b>Research Sources:</b> Verify traditional values from reliable sources</li>
        <li><b>Hebrew Finals:</b> Enable final forms for authentic Hebrew gematria</li>
        <li><b>Greek Variants:</b> Consider different Greek numbering systems</li>
        <li><b>Document Sources:</b> Note where values came from in descriptions</li>
        </ul>
        
        <h3>🔧 Workflow Optimization:</h3>
        <ul>
        <li><b>Clone & Modify:</b> Start with existing ciphers for variations</li>
        <li><b>Preset Usage:</b> Use presets as starting points, then customize</li>
        <li><b>Batch Operations:</b> Create multiple related ciphers in one session</li>
        <li><b>Regular Cleanup:</b> Remove unused test ciphers periodically</li>
        </ul>
        
        <h3>💾 Data Management:</h3>
        <ul>
        <li><b>Regular Backups:</b> Export your ciphers monthly</li>
        <li><b>Version Control:</b> Keep dated backups of important cipher sets</li>
        <li><b>Organization:</b> Use consistent naming conventions</li>
        <li><b>Documentation:</b> Maintain notes about cipher purposes</li>
        </ul>
        
        <h3>🎨 UI Efficiency:</h3>
        <ul>
        <li><b>Keyboard Navigation:</b> Use Tab to move between value fields</li>
        <li><b>Search Usage:</b> Use search to quickly find specific ciphers</li>
        <li><b>Filter Application:</b> Use language filters to reduce clutter</li>
        <li><b>Window Management:</b> Keep the manager open while working</li>
        </ul>
        
        <h3>🔍 Quality Assurance:</h3>
        <ul>
        <li><b>Value Verification:</b> Double-check important letter values</li>
        <li><b>Test Calculations:</b> Try your cipher with known words</li>
        <li><b>Peer Review:</b> Have others check important ciphers</li>
        <li><b>Documentation:</b> Record any special rules or exceptions</li>
        </ul>
        
        <h3>🌟 Advanced Techniques:</h3>
        <ul>
        <li><b>Case Sensitivity:</b> Use for modern text analysis</li>
        <li><b>Value Patterns:</b> Create mathematical progressions</li>
        <li><b>Multi-Language:</b> Create cipher families across languages</li>
        <li><b>Special Purpose:</b> Design ciphers for specific research needs</li>
        </ul>
        
        <h3>🤝 Collaboration:</h3>
        <ul>
        <li><b>Sharing Standards:</b> Agree on naming conventions with team</li>
        <li><b>Export Packages:</b> Create cipher collections for distribution</li>
        <li><b>Version Tracking:</b> Document cipher evolution over time</li>
        <li><b>Peer Validation:</b> Cross-check important cipher values</li>
        </ul>
        """
        
        self._show_help_dialog("Tips & Best Practices", help_text)

    def _show_troubleshooting_help(self) -> None:
        """Show troubleshooting help."""
        help_text = """
        <h2>🔧 Troubleshooting</h2>
        
        <h3>🚨 Common Issues & Solutions:</h3>
        
        <h4>Cannot Save Cipher:</h4>
        <ul>
        <li><b>Empty Name:</b> Cipher name cannot be blank</li>
        <li><b>No Values:</b> At least one letter must have a non-zero value</li>
        <li><b>Duplicate Name:</b> Choose a unique name for new ciphers</li>
        <li><b>Invalid Characters:</b> Avoid special characters in names</li>
        </ul>
        
        <h4>Missing Letters in Grid:</h4>
        <ul>
        <li><b>Language Selection:</b> Verify correct language is selected</li>
        <li><b>Options Settings:</b> Check case sensitivity and final forms settings</li>
        <li><b>Grid Refresh:</b> Try changing language and changing back</li>
        <li><b>Restart Manager:</b> Close and reopen the manager</li>
        </ul>
        
        <h4>Import/Export Problems:</h4>
        <ul>
        <li><b>File Permissions:</b> Ensure read/write access to files</li>
        <li><b>File Format:</b> Only JSON files are supported</li>
        <li><b>Corrupted Files:</b> Try with a different file</li>
        <li><b>Path Issues:</b> Avoid special characters in file paths</li>
        </ul>
        
        <h4>Performance Issues:</h4>
        <ul>
        <li><b>Too Many Ciphers:</b> Consider archiving unused ciphers</li>
        <li><b>Large Descriptions:</b> Keep descriptions reasonably short</li>
        <li><b>Memory Usage:</b> Restart the application if sluggish</li>
        <li><b>Database Issues:</b> Check available disk space</li>
        </ul>
        
        <h3>🔍 Diagnostic Steps:</h3>
        
        <h4>When Something Goes Wrong:</h4>
        <ol>
        <li><b>Check Status Bar:</b> Look for error messages at the bottom</li>
        <li><b>Verify Settings:</b> Ensure all options are set correctly</li>
        <li><b>Try Simple Case:</b> Test with a basic cipher first</li>
        <li><b>Restart Manager:</b> Close and reopen the cipher manager</li>
        <li><b>Check Logs:</b> Look for error messages in application logs</li>
        </ol>
        
        <h3>💾 Data Recovery:</h3>
        
        <h4>If Ciphers Are Lost:</h4>
        <ul>
        <li><b>Check Backups:</b> Look for exported cipher files</li>
        <li><b>Database Recovery:</b> Check if database files exist</li>
        <li><b>Recent Exports:</b> Import from recent export files</li>
        <li><b>Recreate:</b> Rebuild important ciphers from documentation</li>
        </ul>
        
        <h3>🆘 Getting Help:</h3>
        
        <h4>When You Need Support:</h4>
        <ul>
        <li><b>Error Messages:</b> Note exact error text</li>
        <li><b>Steps to Reproduce:</b> Document what you were doing</li>
        <li><b>System Info:</b> Note your operating system and version</li>
        <li><b>Screenshots:</b> Capture relevant screen images</li>
        </ul>
        
        <h3>🔄 Reset Options:</h3>
        
        <h4>Nuclear Options (Use Carefully):</h4>
        <ul>
        <li><b>Clear All Ciphers:</b> Delete all custom ciphers (export first!)</li>
        <li><b>Reset Settings:</b> Return to default configuration</li>
        <li><b>Reinstall:</b> Complete application reinstallation</li>
        <li><b>Fresh Start:</b> Create new user profile</li>
        </ul>
        
        <h3>🛡️ Prevention:</h3>
        <ul>
        <li><b>Regular Backups:</b> Export ciphers frequently</li>
        <li><b>Test Changes:</b> Try modifications on copies first</li>
        <li><b>Document Work:</b> Keep notes about important ciphers</li>
        <li><b>Update Software:</b> Keep the application current</li>
        </ul>
        
        <p><b>💡 Remember:</b> Most issues can be resolved by restarting the manager or checking your settings. When in doubt, export your ciphers first!</p>
        """
        
        self._show_help_dialog("Troubleshooting", help_text)

    def _show_help_dialog(self, title: str, content: str) -> None:
        """Show a help dialog with the given title and content.
        
        Args:
            title: Dialog title
            content: HTML content to display
        """
        from PyQt6.QtWidgets import QDialog, QVBoxLayout, QTextBrowser, QPushButton, QHBoxLayout
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"Help - {title}")
        dialog.setModal(True)
        dialog.resize(700, 600)
        
        layout = QVBoxLayout(dialog)
        
        # Content area
        content_browser = QTextBrowser()
        content_browser.setHtml(content)
        content_browser.setOpenExternalLinks(True)
        layout.addWidget(content_browser)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        back_btn = QPushButton("← Back to Help Menu")
        back_btn.clicked.connect(lambda: (dialog.close(), self._show_help_menu()))
        button_layout.addWidget(back_btn)
        
        close_btn = QPushButton("Close")
        close_btn.clicked.connect(dialog.close)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        dialog.exec()

    def _close_widget(self) -> None:
        """Handle close request."""
        if self.unsaved_changes:
            reply = QMessageBox.question(
                self,
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before closing?",
                QMessageBox.StandardButton.Save | QMessageBox.StandardButton.Discard | QMessageBox.StandardButton.Cancel,
                QMessageBox.StandardButton.Save
            )
            
            if reply == QMessageBox.StandardButton.Save:
                self._save_current_cipher()
            elif reply == QMessageBox.StandardButton.Cancel:
                return
        
        self.close_requested.emit()

    def _on_language_changed(self) -> None:
        """Handle language selection change."""
        # Enable/disable Hebrew final forms checkbox based on language
        is_hebrew = self.language_combo.currentText() == "Hebrew"
        self.use_final_forms_checkbox.setEnabled(is_hebrew)
        if not is_hebrew:
            self.use_final_forms_checkbox.setChecked(False)
        
        # Regenerate letter grid for new language
        self._regenerate_letter_grid()
        self._mark_unsaved_changes()

    def _on_case_sensitivity_changed(self) -> None:
        """Handle case sensitivity option change."""
        # Update info label
        self._update_case_info_label()
        
        # Regenerate letter grid to show/hide uppercase letters
        self._regenerate_letter_grid()
        self._mark_unsaved_changes()

    def _on_final_forms_changed(self) -> None:
        """Handle Hebrew final forms option change."""
        # Regenerate letter grid to show/hide final forms
        self._regenerate_letter_grid()
        self._mark_unsaved_changes()

    def _update_case_info_label(self) -> None:
        """Update the case sensitivity information label."""
        if self.case_sensitive_checkbox.isChecked():
            self.case_info_label.setText(
                "💡 Case Sensitive: Uppercase and lowercase letters can have different values. "
                "Both will be shown in the grid below."
            )
        else:
            self.case_info_label.setText(
                "💡 Case Insensitive: The same value will be used for both uppercase and lowercase letters. "
                "Only one case is shown in the grid below."
            )

    def _regenerate_letter_grid(self) -> None:
        """Regenerate the letter grid based on current settings."""
        if hasattr(self, 'current_cipher') and self.current_cipher:
            # Update cipher settings based on UI
            language_enum = self._language_display_to_enum(self.language_combo.currentText())
            
            # Create updated cipher config with current settings
            updated_cipher = CustomCipherConfig(
                name=self.current_cipher.name,
                language=language_enum,
                description=self.current_cipher.description
            )
            updated_cipher.case_sensitive = self.case_sensitive_checkbox.isChecked()
            updated_cipher.use_final_forms = self.use_final_forms_checkbox.isChecked()
            
            # Preserve existing letter values where possible
            updated_cipher.letter_values = self.current_cipher.letter_values.copy()
            
            # Recreate the grid
            self._create_letter_grid(updated_cipher)
        elif hasattr(self, 'letter_grid_widget'):
            # For new ciphers, create temporary cipher with current settings
            language_enum = self._language_display_to_enum(self.language_combo.currentText())
            temp_cipher = CustomCipherConfig(
                name="Temporary",
                language=language_enum
            )
            temp_cipher.case_sensitive = self.case_sensitive_checkbox.isChecked()
            temp_cipher.use_final_forms = self.use_final_forms_checkbox.isChecked()
            temp_cipher.letter_values = temp_cipher.get_empty_template()
            
            self._create_letter_grid(temp_cipher)


# Backward compatibility alias
ModernCustomCipherDialog = ModernCustomCipherWidget 