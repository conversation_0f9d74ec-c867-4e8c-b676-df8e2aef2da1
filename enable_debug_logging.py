#!/usr/bin/env python3
"""
Enable debug logging for the application to troubleshoot method name display issues.
Run this before running the main application to see detailed debug logs.
"""

import os
import sys
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Configure logging to show debug messages
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_method_name.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)
logger.info("Debug logging enabled for method name display")
logger.info("Check debug_method_name.log for detailed logs")

print("Debug logging enabled!")
print("Check 'debug_method_name.log' for detailed logs when running the application")
print("You should now run the main application and check the calculation details dialog")
