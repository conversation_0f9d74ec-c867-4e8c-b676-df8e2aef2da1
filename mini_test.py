#!/usr/bin/python3
"""
Basic test of our tuple extraction logic
"""

print("Testing tuple extraction")
print("=" * 30)

# Test the problematic case
custom_method = "Custom: ('Method Name in Tuple', 'Extra Data')"

print(f"Input: {custom_method}")

# Extract using our algorithm
if custom_method.startswith("Custom: (") and "," in custom_method:
    start = custom_method.find("'", 8)  # Start after "Custom: ("
    if start != -1:
        end = custom_method.find("'", start + 1)
        if end != -1:
            result = custom_method[start + 1:end]
            print(f"Result: {result}")

# Check if successful
print("Test complete")
