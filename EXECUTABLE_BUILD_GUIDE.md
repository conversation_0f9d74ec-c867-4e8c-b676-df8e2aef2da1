# IsopGem Linux Executable Build Guide

## 🎉 Build Completed Successfully!

Your IsopGem application has been successfully compiled into a standalone Linux executable! This guide covers everything you need to know about using and distributing your executable.

## 🔨 Building the Executable

### Prerequisites
- Python 3.11+ with virtual environment
- All IsopGem dependencies installed
- PyInstaller package
- Sufficient disk space (build can require 1GB+ temporarily)

### Build Process
```bash
# Automated build (recommended)
./build_executable.sh

# Manual build
source venv_py311/bin/activate
pyinstaller main.spec --clean --noconfirm
```

### Build Configuration
The build uses `main.spec` with:
- **Mode**: Directory-based distribution (COLLECT)
- **Console**: Disabled (GUI application)
- **Optimization**: UPX compression enabled
- **Assets**: All necessary data files included
- **Exclusions**: PyQt5 and unnecessary modules excluded

## 📁 Build Results

### Executable Details
- **Location**: `./dist/IsopGem/IsopGem` (directory-based distribution)
- **Size**: ~30MB executable + ~200MB dependencies
- **Type**: ELF 64-bit LSB executable for Linux x86-64
- **Dependencies**: Self-contained (no external Python installation required)
- **Distribution Type**: Directory-based (includes _internal folder with dependencies)

### What's Included
The executable bundles:
- Complete Python runtime
- All PyQt6 libraries and dependencies
- All astronomical calculation libraries (Swiss Ephemeris, etc.)
- All mathematical and visualization libraries
- All application assets and data files
- OpenGL and 3D visualization support

## 🚀 Running the Application

### Direct Execution
```bash
# Using the launcher script (recommended)
./launch_isopgem.sh

# Direct execution from dist directory
./dist/IsopGem/IsopGem

# Or with full path
/path/to/IsopGem/dist/IsopGem/IsopGem
```

### Desktop Integration
Create a desktop entry for easy access:
```bash
./create_desktop_entry.sh
```

This creates:
- **Location**: `~/.local/share/applications/isopgem.desktop`
- **Access**: Search for "IsopGem" in your application menu
- **Categories**: Education → Science → Math

## 📦 Distribution Options

### Option 1: Portable Distribution
Simply copy the entire `dist/IsopGem` directory to any Linux system:
```bash
# Copy to another system
scp -r dist/IsopGem user@remote-system:/path/to/destination/
```

### Option 2: System-Wide Installation
Install for all users on the system:
```bash
# Requires sudo privileges
sudo ./install_system_wide.sh
```

After system-wide installation:
- Run from anywhere: `IsopGem`
- Available in system application menu
- Accessible to all users

## 🛠️ Build Scripts Reference

### Primary Build Script
```bash
./build_executable.sh
```
- Cleans previous builds
- Runs PyInstaller with optimized settings
- Validates the build
- Provides detailed status output

### Desktop Entry Creator
```bash
./create_desktop_entry.sh
```
- Creates user-specific desktop entry
- Updates desktop database
- Enables application menu integration

### System Installation
```bash
sudo ./install_system_wide.sh
```
- Installs to `/usr/local/bin/`
- Creates system-wide desktop entry
- Enables global access

## 🔧 Technical Details

### PyInstaller Configuration
The build uses `main.spec` with:
- **Mode**: Directory-based distribution (COLLECT)
- **Console**: Disabled (GUI application)
- **Optimization**: UPX compression enabled
- **Assets**: All necessary data files included
- **Exclusions**: PyQt5 and unnecessary modules excluded

### Dependencies Included
- **GUI Framework**: PyQt6 with all modules
- **Astronomy**: Swiss Ephemeris, Skyfield, PyEphem
- **Mathematics**: NumPy, SymPy, NetworkX
- **Visualization**: Matplotlib, PyQtGraph, VTK
- **Document Processing**: PyMuPDF, python-docx
- **Data Processing**: Pandas, Pydantic

### Asset Inclusion
All necessary assets are bundled:
- `/assets/` - Application assets
- `/resources/` - Images and resources
- `/config/` - Configuration files
- `/sweph/` - Swiss Ephemeris data
- Module-specific data directories

## 🐛 Troubleshooting

### Common Issues

#### "Permission denied" Error
```bash
chmod +x dist/IsopGem/IsopGem
# Or use the launcher script
chmod +x launch_isopgem.sh
```

#### Missing Libraries on Target System
The executable is self-contained, but some systems may need:
```bash
# Ubuntu/Debian
sudo apt install libgl1-mesa-glx libglib2.0-0

# CentOS/RHEL/Fedora
sudo yum install mesa-libGL glib2
```

#### Large Directory Size
The ~250MB total size is normal for a complex application with:
- Complete Python runtime (~30MB executable + ~200MB dependencies)
- PyQt6 with WebEngine
- Scientific computing libraries
- 3D visualization libraries
- Astronomical data files

### Performance Notes
- **First Launch**: May take 5-15 seconds (initialization)
- **Subsequent Launches**: Much faster (cached components)
- **Memory Usage**: Similar to running from source
- **CPU Usage**: No performance penalty
- **Disk Space**: Keep entire dist/IsopGem directory together

## 🔄 Rebuilding

### When to Rebuild
- Code changes
- Dependency updates
- Asset modifications
- Configuration changes

### Rebuild Process
```bash
# Clean and rebuild
./build_executable.sh

# Update desktop entry if needed
./create_desktop_entry.sh
```

### Build Environment
- **Python**: 3.11+ recommended
- **Virtual Environment**: Strongly recommended
- **PyInstaller**: 6.0+ required
- **System**: Linux x86-64

## 📋 Distribution Checklist

Before distributing your executable:

- [ ] Test on clean system without Python
- [ ] Verify all features work correctly
- [ ] Check asset loading (images, data files)
- [ ] Test astronomical calculations
- [ ] Validate 3D visualization
- [ ] Confirm document processing
- [ ] Test on different Linux distributions

## 🎯 Next Steps

### For Users
1. **Run the Application**: `./launch_isopgem.sh` or `./dist/IsopGem/IsopGem`
2. **Create Desktop Shortcut**: `./create_desktop_entry.sh`
3. **Optional System Install**: `sudo ./install_system_wide.sh`

### For Developers
1. **Version Control**: Consider `.gitignore` for `dist/` and `build/`
2. **CI/CD**: Automate builds with GitHub Actions
3. **Packaging**: Consider AppImage or Flatpak for broader distribution
4. **Testing**: Set up automated testing on different Linux distributions

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify system requirements
3. Test with a fresh build
4. Check PyInstaller logs in the build output

---

**Congratulations! Your IsopGem application is now ready for distribution as a standalone Linux executable!** 🎉 