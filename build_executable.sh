#!/bin/bash

# IsopGem Executable Build Script
# This script builds a standalone executable for Linux using PyInstaller

set -e  # Exit on any error

echo "🚀 Building IsopGem Executable for Linux..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    print_status "Virtual environment detected: $VIRTUAL_ENV"
else
    print_warning "No virtual environment detected. Consider using one for better dependency management."
fi

# Check if PyInstaller is installed
if ! command -v pyinstaller &> /dev/null; then
    print_error "PyInstaller not found. Installing..."
    pip install pyinstaller
fi

# Clean previous builds
print_status "Cleaning previous builds..."
rm -rf build/
rm -rf dist/
rm -rf __pycache__/
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# Check if main.spec exists
if [ ! -f "main.spec" ]; then
    print_error "main.spec file not found!"
    exit 1
fi

print_status "Building executable with PyInstaller..."
print_status "This may take several minutes depending on your system..."

# Build the executable
if pyinstaller main.spec --clean --noconfirm; then
    print_success "Build completed successfully!"
    
    # Check if the executable was created
    if [ -f "dist/IsopGem" ]; then
        print_success "Executable created: dist/IsopGem"
        
        # Make it executable
        chmod +x dist/IsopGem
        
        # Get file size
        SIZE=$(du -h dist/IsopGem | cut -f1)
        print_status "Executable size: $SIZE"
        
        # Test if the executable can be run
        print_status "Testing executable..."
        if ./dist/IsopGem --version 2>/dev/null || ./dist/IsopGem --help 2>/dev/null; then
            print_success "Executable test passed!"
        else
            print_warning "Executable created but may have runtime issues. Test manually."
        fi
        
        echo ""
        echo "=============================================="
        print_success "Build Complete! 🎉"
        echo ""
        echo "Your executable is located at: ./dist/IsopGem"
        echo ""
        echo "To run your application:"
        echo "  ./dist/IsopGem"
        echo ""
        echo "To install system-wide (optional):"
        echo "  sudo cp dist/IsopGem /usr/local/bin/"
        echo "  # Then run with: IsopGem"
        echo ""
        echo "To create a desktop entry:"
        echo "  ./create_desktop_entry.sh"
        echo "=============================================="
        
    else
        print_error "Executable not found in dist/ directory!"
        exit 1
    fi
else
    print_error "Build failed! Check the output above for errors."
    exit 1
fi 