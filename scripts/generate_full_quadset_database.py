#!/usr/bin/env python3
"""
@file scripts/generate_full_quadset_database.py
@description Generate complete quadset database up to 15-digit ternary numbers using existing TQ services
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies sqlite3, os, sys, pathlib
"""

import sqlite3
import os
import sys
from pathlib import Path
from typing import Dict, Any, <PERSON><PERSON>

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import the existing services
from tq.utils.ternary_transition import TernaryTransition
from tq.utils.ternary_converter import decimal_to_ternary, ternary_to_decimal


def calculate_quadset(base_number: int) -> Dict[str, Any]:
    """
    Calculate the complete quadset for a base number using existing TQ logic.
    
    Args:
        base_number: The base decimal number
        
    Returns:
        Dictionary containing all quadset data
    """
    # Convert to 6-digit ternary
    base_ternary = decimal_to_ternary(base_number, pad_length=6)
    
    # Initialize transition utility
    transition = TernaryTransition()
    
    # Calculate the four quadset members
    conrune_ternary = transition.apply_conrune(base_ternary)
    conrune = ternary_to_decimal(conrune_ternary)
    
    # Reversal (reverse the digits)
    reversal_ternary = base_ternary[::-1]
    reversal = ternary_to_decimal(reversal_ternary)
    
    # Reversal Conrune
    reversal_conrune_ternary = transition.apply_conrune(reversal_ternary)
    reversal_conrune = ternary_to_decimal(reversal_conrune_ternary)
    
    # Calculate the key differences
    base_conrune_diff = abs(base_number - conrune)
    reversal_reversal_conrune_diff = abs(reversal - reversal_conrune)
    
    # Calculate transition between the differences
    diff1_ternary = decimal_to_ternary(base_conrune_diff, pad_length=6)
    diff2_ternary = decimal_to_ternary(reversal_reversal_conrune_diff, pad_length=6)
    
    transition_result_ternary = transition.apply_transition(diff1_ternary, diff2_ternary)
    transition_result_decimal = ternary_to_decimal(transition_result_ternary)
    
    # Calculate quadset sum
    quadset_sum = base_number + conrune + reversal + reversal_conrune
    
    # Calculate ternary digit count
    ternary_digits = len(base_ternary.lstrip('0')) if base_ternary.lstrip('0') else 1
    
    return {
        'base_number': base_number,
        'base_ternary': base_ternary,
        'conrune': conrune,
        'conrune_ternary': conrune_ternary,
        'reversal': reversal,
        'reversal_ternary': reversal_ternary,
        'reversal_conrune': reversal_conrune,
        'reversal_conrune_ternary': reversal_conrune_ternary,
        'base_conrune_diff': base_conrune_diff,
        'reversal_reversal_conrune_diff': reversal_reversal_conrune_diff,
        'transition_result_decimal': transition_result_decimal,
        'transition_result_ternary': transition_result_ternary,
        'quadset_sum': quadset_sum,
        'ternary_digits': ternary_digits
    }


def create_quadset_database(db_path: str, max_number: int = 14348907, batch_size: int = 10000):
    """
    Create a complete quadset database.
    
    Args:
        db_path: Path to the database file
        max_number: Maximum number to generate (3^15 = 14,348,907)
        batch_size: Number of records to process in each batch
    """
    print(f"Creating quadset database at: {db_path}")
    print(f"Generating quadsets for numbers 1 to {max_number:,}")
    
    # Create database directory if it doesn't exist
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Drop existing table if it exists to ensure clean schema
    cursor.execute("DROP TABLE IF EXISTS quadsets")
    conn.commit()
    
    # Create the quadsets table
    cursor.execute("""
        CREATE TABLE quadsets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            base_number INTEGER UNIQUE NOT NULL,
            base_ternary TEXT NOT NULL,
            conrune INTEGER NOT NULL,
            conrune_ternary TEXT NOT NULL,
            reversal INTEGER NOT NULL,
            reversal_ternary TEXT NOT NULL,
            reversal_conrune INTEGER NOT NULL,
            reversal_conrune_ternary TEXT NOT NULL,
            base_conrune_diff INTEGER NOT NULL,
            reversal_reversal_conrune_diff INTEGER NOT NULL,
            transition_result_decimal INTEGER NOT NULL,
            transition_result_ternary TEXT NOT NULL,
            quadset_sum INTEGER NOT NULL,
            ternary_digits INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    # Commit the table creation before creating indexes
    conn.commit()
    
    # Create indexes for better performance
    cursor.execute("CREATE INDEX idx_base_number ON quadsets(base_number)")
    cursor.execute("CREATE INDEX idx_conrune ON quadsets(conrune)")
    cursor.execute("CREATE INDEX idx_reversal ON quadsets(reversal)")
    cursor.execute("CREATE INDEX idx_reversal_conrune ON quadsets(reversal_conrune)")
    cursor.execute("CREATE INDEX idx_quadset_sum ON quadsets(quadset_sum)")
    cursor.execute("CREATE INDEX idx_ternary_digits ON quadsets(ternary_digits)")
    cursor.execute("CREATE INDEX idx_transition_result ON quadsets(transition_result_decimal)")
    
    conn.commit()
    
    # Generate quadsets in batches
    batch_data = []
    processed = 0
    
    for base_number in range(1, max_number + 1):
        try:
            quadset_data = calculate_quadset(base_number)
            
            batch_data.append((
                quadset_data['base_number'],
                quadset_data['base_ternary'],
                quadset_data['conrune'],
                quadset_data['conrune_ternary'],
                quadset_data['reversal'],
                quadset_data['reversal_ternary'],
                quadset_data['reversal_conrune'],
                quadset_data['reversal_conrune_ternary'],
                quadset_data['base_conrune_diff'],
                quadset_data['reversal_reversal_conrune_diff'],
                quadset_data['transition_result_decimal'],
                quadset_data['transition_result_ternary'],
                quadset_data['quadset_sum'],
                quadset_data['ternary_digits']
            ))
            
            # Insert batch when it reaches batch_size
            if len(batch_data) >= batch_size:
                cursor.executemany("""
                    INSERT OR REPLACE INTO quadsets 
                    (base_number, base_ternary, conrune, conrune_ternary, 
                     reversal, reversal_ternary, reversal_conrune, reversal_conrune_ternary,
                     base_conrune_diff, reversal_reversal_conrune_diff,
                     transition_result_decimal, transition_result_ternary,
                     quadset_sum, ternary_digits)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, batch_data)
                
                conn.commit()
                processed += len(batch_data)
                batch_data = []
                
                # Progress update
                if processed % 100000 == 0:
                    print(f"Processed {processed:,} quadsets...")
        
        except Exception as e:
            print(f"Error processing number {base_number}: {e}")
            continue
    
    # Insert remaining batch
    if batch_data:
        cursor.executemany("""
            INSERT OR REPLACE INTO quadsets 
            (base_number, base_ternary, conrune, conrune_ternary, 
             reversal, reversal_ternary, reversal_conrune, reversal_conrune_ternary,
             base_conrune_diff, reversal_reversal_conrune_diff,
             transition_result_decimal, transition_result_ternary,
             quadset_sum, ternary_digits)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, batch_data)
        
        conn.commit()
        processed += len(batch_data)
    
    # Generate statistics
    cursor.execute("SELECT COUNT(*) FROM quadsets")
    total_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT MIN(quadset_sum), MAX(quadset_sum), AVG(quadset_sum) FROM quadsets")
    min_sum, max_sum, avg_sum = cursor.fetchone()
    
    cursor.execute("SELECT MIN(ternary_digits), MAX(ternary_digits) FROM quadsets")
    min_digits, max_digits = cursor.fetchone()
    
    cursor.execute("SELECT COUNT(DISTINCT transition_result_decimal) FROM quadsets")
    unique_transitions = cursor.fetchone()[0]
    
    print(f"\n✅ Database generation complete!")
    print(f"📊 Statistics:")
    print(f"   Total quadsets: {total_count:,}")
    print(f"   Quadset sum range: {min_sum:,} to {max_sum:,} (avg: {avg_sum:.1f})")
    print(f"   Ternary digits range: {min_digits} to {max_digits}")
    print(f"   Unique transition results: {unique_transitions:,}")
    
    conn.close()


def test_quadset_calculation():
    """Test the quadset calculation with a few examples."""
    print("🧪 Testing quadset calculation...")
    
    test_numbers = [1, 10, 100, 1000]
    
    for num in test_numbers:
        quadset = calculate_quadset(num)
        print(f"\nBase: {num}")
        print(f"  Ternary: {quadset['base_ternary']}")
        print(f"  Conrune: {quadset['conrune']} ({quadset['conrune_ternary']})")
        print(f"  Reversal: {quadset['reversal']} ({quadset['reversal_ternary']})")
        print(f"  Rev. Conrune: {quadset['reversal_conrune']} ({quadset['reversal_conrune_ternary']})")
        print(f"  Differences: {quadset['base_conrune_diff']}, {quadset['reversal_reversal_conrune_diff']}")
        print(f"  Transition: {quadset['transition_result_decimal']} ({quadset['transition_result_ternary']})")
        print(f"  Sum: {quadset['quadset_sum']}")


if __name__ == "__main__":
    # Test first
    test_quadset_calculation()
    
    # Ask user for confirmation before generating full database
    print(f"\n🚀 Ready to generate full quadset database (14,348,907 records)")
    print("This will take significant time and disk space.")
    
    response = input("Continue? (y/N): ").strip().lower()
    
    if response == 'y':
        db_path = os.path.join(project_root, "data", "quadsets.db")
        create_quadset_database(db_path)
    else:
        print("Database generation cancelled.") 