#!/usr/bin/env python3
"""
@file scripts/build_quadset_database.py
@description Build QuadScript-compatible quadset database from CSV data
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies sqlite3, csv, os, math
"""

import sqlite3
import csv
import os
import math
from pathlib import Path


def ternary_digits(number: int) -> int:
    """Calculate number of ternary digits."""
    if number <= 0:
        return 1
    count = 0
    while number > 0:
        number //= 3
        count += 1
    return count


def is_prime(number: int) -> bool:
    """Check if number is prime."""
    if number < 2:
        return False
    if number == 2:
        return True
    if number % 2 == 0:
        return False
    
    for i in range(3, int(math.sqrt(number)) + 1, 2):
        if number % i == 0:
            return False
    return True


def is_perfect_square(number: int) -> bool:
    """Check if number is a perfect square."""
    if number < 0:
        return False
    root = int(math.sqrt(number))
    return root * root == number


def create_quadset_database(db_path: str, csv_path: str):
    """Create QuadScript-compatible quadset database."""
    print(f"🗄️ Creating quadset database at {db_path}")
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # Create database connection
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Create quadsets table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS quadsets (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            member_1 INTEGER NOT NULL,
            member_2 INTEGER NOT NULL,
            member_3 INTEGER NOT NULL,
            member_4 INTEGER NOT NULL,
            sum_total INTEGER NOT NULL,
            differential_transgram INTEGER,
            zodiacal_pair_ab TEXT,
            zodiacal_pair_cd TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(member_1, member_2, member_3, member_4)
        )
    """)
    
    # Create quadset properties table
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS quadset_properties (
            quadset_id INTEGER PRIMARY KEY,
            -- Mathematical Properties
            all_prime BOOLEAN DEFAULT FALSE,
            prime_count INTEGER DEFAULT 0,
            contains_perfect_squares BOOLEAN DEFAULT FALSE,
            perfect_square_count INTEGER DEFAULT 0,
            
            -- Ternary Digit Properties
            min_ternary_digits INTEGER,
            max_ternary_digits INTEGER,
            avg_ternary_digits REAL,
            uniform_ternary_digits BOOLEAN DEFAULT FALSE,
            ternary_digit_pattern TEXT,
            
            -- Sum Properties
            sum_is_even BOOLEAN DEFAULT TRUE,
            sum_is_perfect_square BOOLEAN DEFAULT FALSE,
            sum_divisible_by_4 BOOLEAN DEFAULT FALSE,
            sum_divisible_by_8 BOOLEAN DEFAULT FALSE,
            sum_divisible_by_12 BOOLEAN DEFAULT FALSE,
            sum_ternary_digits INTEGER,
            
            -- Scale Categories
            contains_thousands BOOLEAN DEFAULT FALSE,
            contains_ten_thousands BOOLEAN DEFAULT FALSE,
            contains_hundreds_thousands BOOLEAN DEFAULT FALSE,
            largest_member_digits INTEGER,
            smallest_member_digits INTEGER,
            
            FOREIGN KEY (quadset_id) REFERENCES quadsets(id)
        )
    """)
    
    # Create indexes
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_quadsets_sum ON quadsets(sum_total)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_quadsets_members ON quadsets(member_1, member_2, member_3, member_4)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_properties_ternary ON quadset_properties(min_ternary_digits, max_ternary_digits)")
    cursor.execute("CREATE INDEX IF NOT EXISTS idx_properties_uniform ON quadset_properties(uniform_ternary_digits)")
    
    # Load and process CSV data
    print(f"📊 Loading quadset data from {csv_path}")
    
    with open(csv_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.DictReader(file)
        quadset_count = 0
        
        for row in csv_reader:
            # Skip recursive pairs and empty rows
            if row['QuadSet'].startswith('RP') or not row['A']:
                continue
                
            try:
                # Extract quadset members
                a = int(row['A'])
                b = int(row['B'])
                c = int(row['C'])
                d = int(row['D'])
                
                members = [a, b, c, d]
                quadset_sum = sum(members)
                
                # Calculate differential transgram (A-B difference)
                diff_trans = abs(a - b) if row['A-B'] else None
                
                # Insert quadset
                cursor.execute("""
                    INSERT OR REPLACE INTO quadsets 
                    (name, member_1, member_2, member_3, member_4, sum_total, 
                     differential_transgram, zodiacal_pair_ab, zodiacal_pair_cd)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    f"QuadSet_{row['QuadSet']}",
                    a, b, c, d, quadset_sum, diff_trans,
                    row.get('Zodiacal Pair AB', ''),
                    row.get('Zodiacal Pair CD', '')
                ))
                
                quadset_id = cursor.lastrowid
                
                # Calculate properties
                ternary_digits_list = [ternary_digits(m) for m in members]
                min_digits = min(ternary_digits_list)
                max_digits = max(ternary_digits_list)
                avg_digits = sum(ternary_digits_list) / len(ternary_digits_list)
                uniform_digits = len(set(ternary_digits_list)) == 1
                digit_pattern = ','.join(map(str, ternary_digits_list))
                
                # Mathematical properties
                prime_count = sum(1 for m in members if is_prime(m))
                all_prime = prime_count == 4
                perfect_square_count = sum(1 for m in members if is_perfect_square(m))
                contains_perfect_squares = perfect_square_count > 0
                
                # Sum properties
                sum_is_perfect_square = is_perfect_square(quadset_sum)
                sum_divisible_by_4 = quadset_sum % 4 == 0
                sum_divisible_by_8 = quadset_sum % 8 == 0
                sum_divisible_by_12 = quadset_sum % 12 == 0
                sum_ternary_digits = ternary_digits(quadset_sum)
                
                # Scale categories
                contains_thousands = any(1000 <= m < 10000 for m in members)
                contains_ten_thousands = any(10000 <= m < 100000 for m in members)
                contains_hundreds_thousands = any(m >= 100000 for m in members)
                
                # Insert properties
                cursor.execute("""
                    INSERT OR REPLACE INTO quadset_properties
                    (quadset_id, all_prime, prime_count, contains_perfect_squares, perfect_square_count,
                     min_ternary_digits, max_ternary_digits, avg_ternary_digits, uniform_ternary_digits,
                     ternary_digit_pattern, sum_is_perfect_square, sum_divisible_by_4, sum_divisible_by_8,
                     sum_divisible_by_12, sum_ternary_digits, contains_thousands, contains_ten_thousands,
                     contains_hundreds_thousands, largest_member_digits, smallest_member_digits)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    quadset_id, all_prime, prime_count, contains_perfect_squares, perfect_square_count,
                    min_digits, max_digits, avg_digits, uniform_digits, digit_pattern,
                    sum_is_perfect_square, sum_divisible_by_4, sum_divisible_by_8, sum_divisible_by_12,
                    sum_ternary_digits, contains_thousands, contains_ten_thousands, contains_hundreds_thousands,
                    max_digits, min_digits
                ))
                
                quadset_count += 1
                
            except (ValueError, KeyError) as e:
                print(f"⚠️ Skipping invalid row {row['QuadSet']}: {e}")
                continue
    
    conn.commit()
    conn.close()
    
    print(f"✅ Successfully created database with {quadset_count} quadsets!")
    return quadset_count


def main():
    """Build the quadset database."""
    # Get project root directory
    project_root = Path(__file__).parent.parent
    
    # Paths
    csv_path = project_root / "assets" / "cvs" / "quadsets_for_app.csv"
    db_path = project_root / "data" / "quadsets.db"
    
    print("🎯 QuadScript Database Builder")
    print("=" * 50)
    
    if not csv_path.exists():
        print(f"❌ CSV file not found: {csv_path}")
        return
    
    # Create database
    quadset_count = create_quadset_database(str(db_path), str(csv_path))
    
    # Test database
    print(f"\n🔍 Testing database connection...")
    conn = sqlite3.connect(str(db_path))
    cursor = conn.cursor()
    
    # Get some statistics
    cursor.execute("SELECT COUNT(*) FROM quadsets")
    total_quadsets = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM quadset_properties WHERE all_prime = 1")
    all_prime_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM quadset_properties WHERE uniform_ternary_digits = 1")
    uniform_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT MAX(sum_total), MIN(sum_total), AVG(sum_total) FROM quadsets")
    max_sum, min_sum, avg_sum = cursor.fetchone()
    
    cursor.execute("""
        SELECT COUNT(*) FROM quadsets q 
        JOIN quadset_properties p ON q.id = p.quadset_id 
        WHERE q.sum_total > 1000000
    """)
    large_sum_count = cursor.fetchone()[0]
    
    conn.close()
    
    print(f"\n📊 Database Statistics:")
    print(f"  • Total quadsets: {total_quadsets}")
    print(f"  • All-prime quadsets: {all_prime_count}")
    print(f"  • Uniform ternary digit quadsets: {uniform_count}")
    print(f"  • Quadsets with sum > 1M: {large_sum_count}")
    print(f"  • Sum range: {min_sum:,} to {max_sum:,}")
    print(f"  • Average sum: {avg_sum:,.0f}")
    
    print(f"\n🚀 Database ready for QuadScript at: {db_path}")
    print("✨ You can now run QuadScript queries on real data!")


if __name__ == "__main__":
    main() 