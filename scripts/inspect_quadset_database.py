#!/usr/bin/env python3
"""
@file scripts/inspect_quadset_database.py
@description Inspect and analyze the generated quadset database
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies sqlite3, os, sys, pathlib, tabulate
"""

import sqlite3
import os
import sys
from pathlib import Path
from typing import List, Dict, Any

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from tabulate import tabulate
    HAS_TABULATE = True
except ImportError:
    HAS_TABULATE = False
    def tabulate(data, headers=None, tablefmt="grid"):
        """Simple fallback table formatter."""
        if not data:
            return ""
        
        # Simple text table formatting
        if headers:
            lines = [" | ".join(str(h) for h in headers)]
            lines.append("-" * len(lines[0]))
        else:
            lines = []
        
        for row in data:
            lines.append(" | ".join(str(cell) for cell in row))
        
        return "\n".join(lines)


def inspect_quadset_database(db_path: str, limit: int = 100):
    """
    Inspect the quadset database and show detailed information.
    
    Args:
        db_path: Path to the database file
        limit: Number of records to display
    """
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    print(f"🔍 Inspecting Quadset Database")
    print(f"📁 Database: {db_path}")
    print("=" * 80)
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Get database info
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    print(f"📊 Tables: {[table[0] for table in tables]}")
    
    # Get table schema
    cursor.execute("PRAGMA table_info(quadsets)")
    schema = cursor.fetchall()
    print(f"\n📋 Table Schema:")
    schema_headers = ["Column", "Type", "NotNull", "Default", "PrimaryKey"]
    schema_data = [[col[1], col[2], bool(col[3]), col[4], bool(col[5])] for col in schema]
    print(tabulate(schema_data, headers=schema_headers, tablefmt="grid"))
    
    # Get database statistics
    print(f"\n📈 Database Statistics:")
    
    cursor.execute("SELECT COUNT(*) FROM quadsets")
    total_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT MIN(base_number), MAX(base_number) FROM quadsets")
    min_base, max_base = cursor.fetchone()
    
    cursor.execute("SELECT MIN(quadset_sum), MAX(quadset_sum), AVG(quadset_sum) FROM quadsets")
    min_sum, max_sum, avg_sum = cursor.fetchone()
    
    cursor.execute("SELECT MIN(ternary_digits), MAX(ternary_digits) FROM quadsets")
    min_digits, max_digits = cursor.fetchone()
    
    cursor.execute("SELECT COUNT(DISTINCT transition_result_decimal) FROM quadsets")
    unique_transitions = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(DISTINCT quadset_sum) FROM quadsets")
    unique_sums = cursor.fetchone()[0]
    
    stats_data = [
        ["Total Records", f"{total_count:,}"],
        ["Base Number Range", f"{min_base:,} to {max_base:,}"],
        ["Quadset Sum Range", f"{min_sum:,} to {max_sum:,}"],
        ["Average Quadset Sum", f"{avg_sum:,.1f}"],
        ["Ternary Digits Range", f"{min_digits} to {max_digits}"],
        ["Unique Transitions", f"{unique_transitions:,}"],
        ["Unique Sums", f"{unique_sums:,}"]
    ]
    print(tabulate(stats_data, headers=["Statistic", "Value"], tablefmt="grid"))
    
    # Show first N records
    print(f"\n📝 First {limit} Quadset Records:")
    cursor.execute(f"""
        SELECT base_number, base_ternary, conrune, reversal, reversal_conrune,
               base_conrune_diff, reversal_reversal_conrune_diff, 
               transition_result_decimal, quadset_sum, ternary_digits
        FROM quadsets 
        ORDER BY base_number 
        LIMIT {limit}
    """)
    
    records = cursor.fetchall()
    headers = [
        "Base", "Ternary", "Conrune", "Reversal", "Rev.Con", 
        "Diff1", "Diff2", "Transition", "Sum", "Digits"
    ]
    
    print(tabulate(records, headers=headers, tablefmt="grid"))
    
    # Show some interesting patterns
    print(f"\n🎯 Interesting Patterns:")
    
    # Largest quadset sums
    cursor.execute("""
        SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
        FROM quadsets 
        ORDER BY quadset_sum DESC 
        LIMIT 10
    """)
    largest_sums = cursor.fetchall()
    print(f"\n🔝 Top 10 Largest Quadset Sums:")
    sum_headers = ["Base", "Conrune", "Reversal", "Rev.Conrune", "Sum"]
    print(tabulate(largest_sums, headers=sum_headers, tablefmt="grid"))
    
    # Most common transition results
    cursor.execute("""
        SELECT transition_result_decimal, COUNT(*) as frequency
        FROM quadsets 
        GROUP BY transition_result_decimal 
        ORDER BY frequency DESC 
        LIMIT 10
    """)
    common_transitions = cursor.fetchall()
    print(f"\n🔄 Most Common Transition Results:")
    trans_headers = ["Transition Result", "Frequency"]
    print(tabulate(common_transitions, headers=trans_headers, tablefmt="grid"))
    
    # Palindromic base numbers (where base = reversal)
    cursor.execute("""
        SELECT base_number, base_ternary, reversal, reversal_ternary
        FROM quadsets 
        WHERE base_number = reversal
        LIMIT 10
    """)
    palindromes = cursor.fetchall()
    if palindromes:
        print(f"\n🪞 Palindromic Numbers (Base = Reversal):")
        pal_headers = ["Base", "Base Ternary", "Reversal", "Reversal Ternary"]
        print(tabulate(palindromes, headers=pal_headers, tablefmt="grid"))
    
    # Numbers where base = conrune
    cursor.execute("""
        SELECT base_number, base_ternary, conrune, conrune_ternary
        FROM quadsets 
        WHERE base_number = conrune
        LIMIT 10
    """)
    self_conrunes = cursor.fetchall()
    if self_conrunes:
        print(f"\n🔄 Self-Conrune Numbers (Base = Conrune):")
        self_headers = ["Base", "Base Ternary", "Conrune", "Conrune Ternary"]
        print(tabulate(self_conrunes, headers=self_headers, tablefmt="grid"))
    
    conn.close()
    print(f"\n✅ Database inspection complete!")


def search_quadset_by_base(db_path: str, base_number: int):
    """
    Search for a specific quadset by base number.
    
    Args:
        db_path: Path to the database file
        base_number: The base number to search for
    """
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT * FROM quadsets WHERE base_number = ?
    """, (base_number,))
    
    result = cursor.fetchone()
    
    if result:
        print(f"\n🔍 Quadset for Base Number {base_number}:")
        print(f"  Base: {result[1]} ({result[2]})")
        print(f"  Conrune: {result[3]} ({result[4]})")
        print(f"  Reversal: {result[5]} ({result[6]})")
        print(f"  Reversal Conrune: {result[7]} ({result[8]})")
        print(f"  Differences: {result[9]}, {result[10]}")
        print(f"  Transition: {result[11]} ({result[12]})")
        print(f"  Quadset Sum: {result[13]}")
        print(f"  Ternary Digits: {result[14]}")
    else:
        print(f"❌ No quadset found for base number {base_number}")
    
    conn.close()


def search_quadsets_by_sum(db_path: str, target_sum: int, limit: int = 10):
    """
    Search for quadsets with a specific sum.
    
    Args:
        db_path: Path to the database file
        target_sum: The quadset sum to search for
        limit: Maximum number of results to return
    """
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
        FROM quadsets 
        WHERE quadset_sum = ?
        ORDER BY base_number
        LIMIT ?
    """, (target_sum, limit))
    
    results = cursor.fetchall()
    
    if results:
        print(f"\n🎯 Quadsets with Sum = {target_sum}:")
        headers = ["Base", "Conrune", "Reversal", "Rev.Conrune", "Sum"]
        print(tabulate(results, headers=headers, tablefmt="grid"))
    else:
        print(f"❌ No quadsets found with sum {target_sum}")
    
    conn.close()


def main():
    """Main function to run database inspection."""
    db_path = os.path.join(project_root, "data", "quadsets.db")
    
    print("🔍 Quadset Database Inspector")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "inspect":
            limit = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            inspect_quadset_database(db_path, limit)
        
        elif command == "search":
            if len(sys.argv) < 3:
                print("Usage: python inspect_quadset_database.py search <base_number>")
                return
            base_number = int(sys.argv[2])
            search_quadset_by_base(db_path, base_number)
        
        elif command == "sum":
            if len(sys.argv) < 3:
                print("Usage: python inspect_quadset_database.py sum <target_sum> [limit]")
                return
            target_sum = int(sys.argv[2])
            limit = int(sys.argv[3]) if len(sys.argv) > 3 else 10
            search_quadsets_by_sum(db_path, target_sum, limit)
        
        else:
            print("Unknown command. Available commands:")
            print("  inspect [limit] - Inspect database with first N records")
            print("  search <base_number> - Search for specific quadset")
            print("  sum <target_sum> [limit] - Find quadsets with specific sum")
    
    else:
        # Default: inspect with first 100 records
        inspect_quadset_database(db_path, 100)


if __name__ == "__main__":
    main() 