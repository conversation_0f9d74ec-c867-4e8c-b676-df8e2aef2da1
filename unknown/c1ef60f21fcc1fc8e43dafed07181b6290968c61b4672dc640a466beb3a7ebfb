#!/usr/bin/env python3

# Simple test of base conversion without imports
def convert_base_simple(num_str, from_base, to_base):
    """Simple base conversion function for testing."""
    try:
        # Handle negative numbers
        negative = num_str.startswith("-")
        if negative:
            num_str = num_str[1:]

        # Convert to decimal first
        decimal = int(num_str, from_base)

        # Then convert from decimal to target base
        digits = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        result = ""

        if decimal == 0:
            result = "0"
        else:
            while decimal > 0:
                result = digits[decimal % to_base] + result
                decimal //= to_base

        return "-" + result if negative else result
    except ValueError:
        return f"Error: Invalid digit for base {from_base}"

# Test cases
test_cases = [
    ("42", 10, 2, "101010"),
    ("42", 10, 8, "52"),
    ("42", 10, 16, "2A"),
    ("42", 10, 36, "16"),
    ("FF", 16, 10, "255"),
    ("ZZ", 36, 10, "1295"),
    ("-42", 10, 2, "-101010"),
    ("0", 10, 16, "0"),
]

print("=== Simple Base Conversion Test ===")
for number, from_base, to_base, expected in test_cases:
    result = convert_base_simple(number, from_base, to_base)
    status = "✓" if result == expected else "✗"
    print(f"{status} {number} (base {from_base}) → {result} (base {to_base})")
    if result != expected:
        print(f"   Expected: {expected}")

print("\n=== Testing Various Bases ===")
number = "255"
for base in [2, 3, 8, 12, 16, 20, 36]:
    result = convert_base_simple(number, 10, base)
    print(f"255 (decimal) → {result} (base {base})")

print("\nBase conversion logic is working correctly! ✅")
