#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to modify the calculation_details_dialog.py file directly.
This will make the most straightforward fix possible to handle the specific case
of a Custom: prefix with a tuple.
"""

import re
import sys
import os

# Path to the file
filepath = "/home/<USER>/Desktop/IsopGem/gematria/ui/dialogs/calculation_details_dialog.py"

def fix_method_name_display():
    """Apply the fix to the _get_method_name method."""
    
    # Read the file
    with open(filepath, 'r') as file:
        content = file.read()
    
    # Find the _get_method_name method
    method_start = content.find("def _get_method_name(self)")
    if method_start == -1:
        print("Error: Could not find _get_method_name method")
        return False
    
    # Find the place to insert our special case - right after checking custom_method is a string
    insert_point = content.find("# First check for", method_start)
    if insert_point == -1:
        print("Error: Could not find insertion point")
        return False
    
    # Our fix - a direct, simple approach to extract the first element from any tuple
    fix_code = """                # Direct fix for the problematic format
                if "Custom: (" in custom_method or "(" in custom_method:
                    try:
                        # Find first quoted text after any opening parenthesis
                        start_idx = custom_method.find("(")
                        if start_idx != -1:
                            # Find the first quote after the opening parenthesis
                            quote_idx = max(custom_method.find("'", start_idx), 
                                           custom_method.find('"', start_idx))
                            if quote_idx != -1:
                                # Find matching closing quote
                                quote_char = custom_method[quote_idx]
                                end_idx = custom_method.find(quote_char, quote_idx + 1)
                                if end_idx != -1:
                                    # Extract just the text between quotes
                                    return custom_method[quote_idx + 1:end_idx]
                    except Exception:
                        pass  # Fall through to other methods
                    
"""
    
    # Insert our fix
    new_content = content[:insert_point] + fix_code + content[insert_point:]
    
    # Write the modified file
    with open(filepath, 'w') as file:
        file.write(new_content)
    
    print("Fix applied successfully")
    return True

if __name__ == "__main__":
    if fix_method_name_display():
        print("Done! The method name display should now work correctly.")
    else:
        print("Failed to apply the fix.")
