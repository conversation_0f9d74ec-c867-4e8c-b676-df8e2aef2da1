# Gematria Methods Reference

This document summarizes the core gematria methods for Hebrew, Greek, English (TQ), and Coptic systems. These methods form the base set for the initial release of the Advanced Gematria App.

---

## Hebrew Gematria Methods

### Hebrew Alphabet Values
| Letter | Name         | Value   |
|--------|--------------|---------|
| א      | Aleph        | 1       |
| ב      | Bet          | 2       |
| ג      | Gimel        | 3       |
| ד      | Dalet        | 4       |
| ה      | He           | 5       |
| ו      | Vav          | 6       |
| ז      | <PERSON>ay<PERSON>        | 7       |
| ח      | Chet         | 8       |
| ט      | Tet          | 9       |
| י      | Yod          | 10      |
| כ/ך    | Kaf/Final Kaf| 20/500  |
| ל      | Lamed        | 30      |
| מ/ם    | Mem/Final Mem| 40/600  |
| נ/ן    | Nun/Final Nun| 50/700  |
| ס      | Samekh       | 60      |
| ע      | Ayin         | 70      |
| פ/ף    | Pe/Final Pe  | 80/800  |
| צ/ץ    | Tsadi/Final Tsadi| 90/900|
| ק      | Qof          | 100     |
| ר      | Resh         | 200     |
| ש      | Shin         | 300     |
| ת      | Tav          | 400     |

*Final letter values are used in some specific methods only*

### Method Categories
#### 1. Basic Value Methods
- **Standard Value (מספר הכרחי, Mispar Hechrachi):** Why: This is the most common and straightforward method. It represents the inherent numerical essence of each letter as established in the Hebrew numeral system. It's considered the "necessary" or "absolute" value.
  - When: Used universally in almost all forms of gematria analysis, from basic word comparisons to complex textual interpretations in Kabbalistic texts like the Zohar, Talmud, and Midrash. It's a foundational method for finding equivalencies between words and phrases.
  - Example: יהוה = י(10) + ה(5) + ו(6) + ה(5) = 26

- **Ordinal Value (מספר סידורי, Mispar Siduri):** Why: This method considers the sequential position of each letter in the alphabet, highlighting the developmental or progressive aspect of concepts. It can reveal relationships based on the "order" of creation or emanation.
  - When: Often used to find simpler numerical relationships or to explore themes of sequence, hierarchy, or stages. It's sometimes used in conjunction with Standard Value to provide another layer of meaning.
  - Example: יהוה = י(10th letter = 10) + ה(5th letter = 5) + ו(6th letter = 6) + ה(5th letter = 5) = 26

- **Final Letter Values (מספר סופית, Mispar Sofit):** Why: The five Hebrew letters that have final forms (Kaf, Mem, Nun, Pe, Tsadi) are given higher values when they appear at the end of a word. This is thought to represent a state of fulfillment, expansion, or a connection to a higher or more revealed plane of existence. The final forms often symbolize the unfolding of potential into actuality.
  - When: Used specifically when a word contains one of these final letters and the interpreter wishes to explore the "fulfilled" or "expanded" meaning of the word. This method is not always applied; its use depends on the specific interpretative context and the desired depth of analysis. It's particularly relevant in Kabbalistic interpretations where the form of the letters carries significant meaning.
  - Example: מלך = מ(40) + ל(30) + ך(final kaf = 500) = 570
  - Compared to standard: מלך = מ(40) + ל(30) + כ(20) = 90

#### 2. Reduction Methods
- **Small/Reduced Value (מספר קטן, Mispar Katan):** Why: This method reduces letter values to a single digit (1-9), reflecting the idea of returning to fundamental essences or "seeds." It's based on the principle that all numbers ultimately derive from the first nine digits, representing the Sefirot in their most basic form. This can reveal underlying connections obscured by larger numbers.
  - When: Used to simplify complex values and find core numerical similarities between words. It's common in meditative practices and when seeking the most essential root of a concept. Often employed when comparing words that have vastly different standard values.
  - Example: אדם = א(1) + ד(4) + ם(40→4) = 9
  - Note: Values above 9 are reduced to a single digit (e.g., 40→4, 400→4 by summing digits, e.g. 4+0=4).

- **Integral Reduced Value (מספר מספרי, Mispar Mispari or מספר קטן מספרי, Mispar Katan Mispari):** Why: This method involves summing the digits of each letter's standard value before summing them for the word. It's a deeper form of reduction, aiming to distill the essence of each letter's contribution to the total. It can reveal a more nuanced "small" value.
  - When: Used when a more profound level of reduction than Mispar Katan is desired, focusing on the internal numerical structure of each letter's value. It's less common than Mispar Katan but offers another layer for analysis.
  - Example: שלום = ש(300→3+0+0=3) + ל(30→3+0=3) + ו(6) + ם(final mem, 600→6+0+0=6 or standard mem 40→4+0=4). Original example used standard mem: ם(40→4+0=4) = 16.

- **Integral Reduced Ordinal Value (מספר קטן סידורי, Mispar Katan Siduri):** Why: Similar to Mispar Katan, but applied to the ordinal values of the letters. This reduces the sequential position to its single-digit essence, focusing on the fundamental stage or aspect represented by that position.
  - When: Used when exploring the core meaning of a word based on the simplified positions of its letters in the alphabet. It's a way to combine the principles of ordinal position and numerical reduction.
  - Example: אבג = א(1st letter = 1) + ב(2nd letter = 2) + ג(3rd letter = 3) = 6

#### 3. Mathematical Operations
- **Square Value (מספר בונה, Mispar Bone'eh - "Building Number" or מספר מרובע, Mispar Meruba - "Squared Number"):** Why: Squaring each letter's value amplifies its power and significance. This method is often associated with concepts of structure, foundation, and manifestation ("building"). The square represents a stable, complete form.
  - When: Used to explore the inherent potential or magnified force of a word. It can highlight the underlying structure or the "building blocks" of a concept. Sometimes used in architectural or cosmological symbolism.
  - Example: אב = א(1²=1) + ב(2²=4) = 5

- **Cubed Value (מספר משולש, Mispar Meshulash - often means Triangular, but here seems to mean Cubed, or מספר מעוקב, Mispar Me'ukav - "Cubed Number"):** Why: Cubing values suggests a three-dimensional or fully realized manifestation, representing volume, depth, and complete development. It signifies an even greater amplification of the letter's essence than squaring.
  - When: Used less commonly than squaring, but applied when seeking to understand the fullest possible expression or impact of a word or name, often in contexts of divine power or cosmic significance.
  - Example: אב = א(1³=1) + ב(2³=8) = 9

- **Triangular Value (מספר קדמי, Mispar Kidmi or מספר משולש, Mispar Meshulash):** Why: This method sums all integers from 1 up to the value of the letter (n(n+1)/2). Triangular numbers are associated with sequences, unfolding processes, and the sum of emanations. It can represent the cumulative force or development of a letter's energy.
  - When: Used to explore the progressive unfolding or the total generative power inherent in a letter's value. It's found in more esoteric Kabbalistic calculations.
  - Example: אב = א(triangular of 1 = 1) + ב(triangular of 2 = 3) = 4
  - Note: Triangular number of n = n(n+1)/2

- **Full Square Value (מספר המרובע הכללי, Mispar HaMerubah HaKlali):** Why: This is the same as Square Value (Mispar Bone'eh/Meruba) if it refers to summing the squares of each letter's value. It emphasizes the combined foundational strength of all letters in the word.
  - When: Same as Square Value. Used to assess the magnified structural power or foundational energy of a word.
  - Example: אבג = א(1²) + ב(2²) + ג(3²) = 1 + 4 + 9 = 14

- **Ordinal Building Value (מספר בונה סידורי, Mispar Boneah Siduri):** Why: Squares the ordinal (positional) values of the letters. This method amplifies the significance of each letter's place in the sequence, suggesting a "built-up" importance based on order.
  - When: Used when the order of letters is particularly significant, and one wishes to explore the magnified impact of this sequence. It combines the concepts of ordinal position with the amplifying effect of squaring.
  - Example: אבג = א(1st letter, 1²=1) + ב(2nd letter, 2²=4) + ג(3rd letter, 3²=9) = 14

#### 4. Full Spelling Methods
- **Full Value (מספר שמי, Mispar Shemi - "Name Value" or מילוי, Milui - "Filling"):** Why: Spelling out the name of each letter and summing their standard values reveals a "hidden" dimension of the word. Each letter is a gateway to a concept, and its name further elaborates that concept. This method taps into the creative power inherent in the names of the letters.
  - When: Used in advanced Kabbalistic analysis to uncover deeper meanings of divine names, scriptural verses, or significant words. It's believed to reveal the inner essence or potential of the word.
  - Example: א = אלף = א(1) + ל(30) + פ(80) = 111
  - Example word: אב = אלף(111) + בית(412) = 523

- **Full Value with Finals (מספר שמי סופית, Mispar Shemi Sofit):** Why: Similar to Full Value, but uses the higher values for final letters when they appear within the spelled-out names of the letters. This adds another layer of expansion or fulfillment to the hidden meaning.
  - When: Applied when an even more expanded or "fulfilled" understanding of the letter names is sought, particularly if the final letters are seen as significant in the structure of those names.
  - Example: א = אלף (if Pe were final, though it isn't here) = א(1) + ל(30) + ף(final pe = 800) = 831. Note: In אלף, the פ is not final. This example illustrates the principle if a final letter occurred in a name.
  - Example word: אב = אלף(111 or 831 if final letters were used in its spelling) + בית(412) = 523 or 1243 (depending on interpretation of "where applicable"). The example given in the original doc (אב = אלף(831) + בית(412) = 1243) implies applying final values within the letter names themselves, e.g., for the 'ף' in 'אלף'.

- **Name Value (מספר שמי, Mispar Shemi - also used for Full Value, sometimes distinguished):** The original document uses this for multiplication of spelled-out letter name values. This is a very uncommon method. Summation is the standard for Milui. If multiplication is intended:
  - Why: Multiplying the values would create exponentially larger numbers, perhaps signifying a vast interconnectedness or a multiplicative creative force. However, this is not a standard recognized Hebrew gematria method.
  - When: Its use would be highly speculative and likely modern or idiosyncratic, as traditional methods focus on summation.
  - Example (if multiplication): אב = אלף(111) × בית(412) = 45,732

- **Name Value with Finals (מספר שמי סופית, Mispar Shemi Sofit - for multiplication):** Why: Similar to the above, but using final letter values in the spelled-out names before multiplication.
  - When: Same as above; highly speculative.
  - Example (if multiplication): אב = אלף(831) × בית(412) = 342,372

- **Hidden Value (מספר נעלם, Mispar Ne'elam):** Why: This subtracts the simple value of the letter from its full spelled-out value (Milui). It represents the "hidden" part of the letter's essence, the energy that remains once the manifest part is removed, pointing to its inner core or unrevealed potential.
  - When: Used in Kabbalistic exegesis to explore the concealed aspects of divine names or important terms. It seeks to understand what is not immediately apparent.
  - Example: א = אלף - א = (111) - 1 = 110
  - Example word: אב = (אלף-א) + (בית-ב) = 110 + (412-2=410) = 520

- **Hidden Value with Finals (מספר נעלם סופית, Mispar Ne'elam Sofit):** Why: The same principle as Hidden Value, but the Milui calculation incorporates final letter values where applicable before subtracting the simple letter value. This explores the hidden essence in its "fulfilled" or "expanded" state.
  - When: Applied when analyzing the concealed aspects of words where the expanded nature of final letters is considered significant for the Milui calculation.
  - Example: א = אלף (using final Pe in its spelling) - א = (831) - 1 = 830
  - Example word: אך = (אלף-א using final Pe = 830) + (כף-כ using final Pe in its spelling, כף = כ(20)+ף(800)=820; 820-20=800) = 830 + 800 = 1630

- **Face Value (מספר הפנים, Mispar HaPanim - "Number of the Face"):** Why: This method highlights the first letter of a word by taking its full spelled-out value (Milui), while the remaining letters are taken at their standard value. It emphasizes the "face" or primary aspect of the word, suggesting that the initial letter sets the tone or core essence.
  - When: Used when the initial letter of a word is considered to hold particular importance or to be the "key" to its meaning. It can show how the rest of the word unfolds from this primary essence.
  - Example word: אב = אלף(111) + ב(2) = 113
  - Example word: אבאה = אלף(111) + ב(2) + א(1) + ה(5) = 119
  - Example with letter name: בית (the name of letter Bet) = (Milui of ב = בית = 412) + י(10) + ת(400) = 822

- **Face Value with Finals (מספר הפנים סופית, Mispar HaPanim Sofit):** Why: Similar to Face Value, but the Milui of the first letter is calculated using final letter values where applicable. This gives an expanded or fulfilled sense to the "face" of the word.
  - When: Used when the initial letter's expanded potential (via final letters in its Milui) is being emphasized as the primary aspect of the word.
  - Example word: אב = אלף(831, using final Pe) + ב(2) = 833
  - Example word: כב = כף(820, כף spelled with final Pe) + ב(2) = 822

- **Back Value (מספר האחור, Mispar HaAchor - "Number of the Back"):** Why: This method emphasizes the final letter of a word by taking its full spelled-out value (Milui), while the preceding letters are taken at their standard value. It focuses on the outcome, conclusion, or "rear guard" aspect of the word.
  - When: Used when the final letter is seen as the culmination or ultimate expression of the word's meaning. It can reveal the result or final impact.
  - Example word: אב = א(1) + בית(412, Milui of ב) = 413
  - Example word: אבאה = א(1) + ב(2) + א(1) + הא(Milui of ה = הא = 5+1=6) = 10
  - Example with letter name: בית (the name of letter Bet) = ב(2) + י(10) + תו(Milui of ת = תו = 400+6=406) = 418

- **Back Value with Finals (מספר האחור סופית, Mispar HaAchor Sofit):** Why: Similar to Back Value, but the Milui of the last letter is calculated using final letter values where applicable. This gives an expanded or fulfilled sense to the "conclusion" or "back" of the word.
  - When: Used when the final letter's expanded potential (via final letters in its Milui) is being emphasized as the culminating aspect of the word.
  - Example word: אף = א(1) + פא(Milui of ף, assuming פא spelled with final Aleph or standard Pe and Aleph, e.g., פ(80)+א(1)=81 or ף(800)+א(1)=801. The example 81 implies standard Pe. If final Pe was intended for the letter itself, its Milui would be different, e.g., פה with final He). The example given: אף = א(1) + פא(81) = 82. This uses the Milui of פ (Peh Aleph = 80+1=81).
  - Example word: אך = א(1) + כף(Milui of ך, spelled כף with final Pe = כ(20)+ף(800)=820) = 821

#### 5. Collective Methods
- **Collective Value (מספר כולל, Mispar Kolel):** Why: Adding the number of letters in the word (or sometimes +1, see below) to its standard value. The "+ number of letters" approach suggests that the word as a whole entity, composed of its individual parts, has a collective synergy. Each letter contributes not just its value but its presence.
  - When: Used to account for the word as a complete unit, beyond just the sum of its parts. It emphasizes the holistic nature of the word or phrase.
  - Example: תורה = ת(400) + ו(6) + ר(200) + ה(5) = 611 + 4 letters = 615

- **Name Collective Value (מספר שמי כולל, Mispar Shemi Kolel):** Why: Adds the number of letters to the Full Value (Milui) of the word. This combines the depth of the spelled-out letter names with the holistic sense of the word as a collective.
  - When: Used in advanced interpretations where both the inner essence (from Milui) and the collective unity of the word are being considered.
  - Example: אב = אלף(111) + בית(412) = 523 + 2 letters = 525

- **Name Collective Value with Finals (מספר שמי כולל סופית, Mispar Shemi Kolel Sofit):** Why: Adds the number of letters to the Full Value (Milui) calculated with final letter values. This emphasizes the collective unity of the word in its most expanded or fulfilled inner state.
  - When: Used when the most expanded form of the word's inner essence, along with its collective nature, is under examination.
  - Example: אב = אלף(831) + בית(412) = 1243 + 2 letters = 1245
  - Example: אך = אלף(831) + כף(820) = 1651 + 2 letters = 1653

- **Regular plus Collective (רגיל פלוס כולל, Ragil plus Kolel - often simply "Kolel" meaning +1):** Why: Adding 1 to the standard value. The "+1" (Kolel) is often interpreted as representing the word itself as a single, unified entity, or sometimes as representing the divine unity or a connection to a higher source. It acknowledges the whole in addition to the sum of its parts.
  - When: Very commonly used in many gematria calculations to find equivalencies. If two words are off by one, the Kolel is often invoked to show their connection. It's a flexible tool for bridging small numerical gaps.
  - Example: תורה = ת(400) + ו(6) + ר(200) + ה(5) = 611 + 1 = 612

#### 6. Substitution Methods
- **AtBash (את בש):** Why: This cipher pairs the first letter of the alphabet with the last, the second with the second-to-last, and so on. It's believed to reveal the inverse or hidden aspect of a word, or to show a connection between seemingly opposite concepts. It reflects a principle of "as above, so below" or inner/outer correspondence.
  - When: Used to uncover secret meanings, often in prophetic or esoteric texts. It can link words that appear unrelated on the surface. Jeremiah 25:26 and 51:41 contain famous examples ("Sheshach" for Babel).
  - Example: אבג → תשר = ת(400) + ש(300) + ר(200) = 900
  - Note: א(Aleph)↔ת(Tav), ב(Bet)↔ש(Shin), ג(Gimel)↔ר(Resh), etc.

- **Albam (אלבם):** Why: This cipher divides the alphabet into two halves, and letters are exchanged between the halves (e.g., the 1st letter with the 12th, the 2nd with the 13th, etc.). It's another way to systematically transform words to find hidden layers of meaning or connections based on a structured permutation.
  - When: Similar to AtBash, used for uncovering concealed meanings in scripture or other sacred texts. It's one of several traditional substitution ciphers (Temurah).
  - Example: אבג → כלמ = כ(20) + ל(30) + מ(40) = 90
  - Note: א(Aleph)↔כ(Kaf), ב(Bet)↔ל(Lamed), ג(Gimel)↔מ(Mem), etc. (The example given seems to use א↔כ, ב↔ל, ג↔מ which is one specific scheme of Albam where the first half of 11 letters is swapped with the second half of 11 letters).




---

## Greek Isopsephy Methods

### Greek Alphabet Values
| Letter | Name     | Value |
|--------|----------|-------|
| Α α    | Alpha    | 1     |
| Β β    | Beta     | 2     |
| Γ γ    | Gamma    | 3     |
| Δ δ    | Delta    | 4     |
| Ε ε    | Epsilon  | 5     |
| Ϝ ϝ    | Digamma  | 6     |
| Ζ ζ    | Zeta     | 7     |
| Η η    | Eta      | 8     |
| Θ θ    | Theta    | 9     |
| Ι ι    | Iota     | 10    |
| Κ κ    | Kappa    | 20    |
| Λ λ    | Lambda   | 30    |
| Μ μ    | Mu       | 40    |
| Ν ν    | Nu       | 50    |
| Ξ ξ    | Xi       | 60    |
| Ο ο    | Omicron  | 70    |
| Π π    | Pi       | 80    |
| Ϙ ϙ    | Koppa    | 90    |
| Ρ ρ    | Rho      | 100   |
| Σ σ/ς  | Sigma    | 200   |
| Τ τ    | Tau      | 300   |
| Υ υ    | Upsilon  | 400   |
| Φ φ    | Phi      | 500   |
| Χ χ    | Chi      | 600   |
| Ψ ψ    | Psi      | 700   |
| Ω ω    | Omega    | 800   |
| ϡ      | Sampi    | 900   |

*Digamma, Koppa, and Sampi are archaic letters used for numbers only*

### Method Categories
#### 1. Basic Value Methods
Standard Value (Αριθμός Κανονικός, Arithmos Kanonikos):
Why: The fundamental method, assigning the established numerical value to each Greek letter. This is seen as the letter's inherent numerical power or identity.
When: Universally used for finding equivalencies between words, names, and phrases in Greek texts, from classical literature (e.g., Homeric analysis by some) to Gnostic writings, magical papyri, and Christian scriptures (e.g., the number of the Beast in Revelation).
Example: Λόγος = Λ(30) + ό(70) + γ(3) + ο(70) + ς(200) = 373
Ordinal Value (Αριθμός Τακτικός, Arithmos Taktikos):
Why: Assigns value based on the letter's position in the 24-letter classical Greek alphabet. It emphasizes the sequential or developmental aspect of concepts.
When: Used to find simpler numerical patterns or to explore meanings related to order and progression. Can provide an alternative layer of meaning to the standard value.
Example: Λόγος = Λ(11th letter = 11) + ό(15th letter = 15) + γ(3rd letter = 3) + ο(15th letter = 15) + ς(18th letter = 18) = 62 (assuming a 24-letter alphabet without archaic forms for ordinal count).
#### 2. Reduction Methods
Small Value (Αριθμός Μικρός, Arithmos Mikros):
Why: Reduces each letter's standard value to a single digit (by summing its digits, e.g., 30→3, 200→2) and then sums these, or sums the total standard value and then reduces it to a single digit (the example suggests the latter). This seeks the fundamental essence or root of the word's numerical value.
When: Used to simplify values and find core numerical links, similar to Hebrew Mispar Katan. Particularly useful when comparing words with large standard values or in Neopythagorean contexts focusing on the meanings of numbers 1-9 (the Ennead).
Example: Λόγος = Λ(30→3) + ό(70→7) + γ(3) + ο(70→7) + ς(200→2) = 22→4. (This example reduces each letter first). Alternatively, Λόγος = 373 → 3+7+3 = 13 → 1+3 = 4. (This reduces the total).
Digital Value (Αριθμός Ψηφιακός, Arithmos Psephiakos):
Why: Sums the digits of each letter's standard value (e.g., Λ(30) becomes 3+0=3). This method focuses on the constituent numerical elements within each letter's value before combining them.
When: A more nuanced form of reduction, used to analyze the internal numerical structure of a word's isopsephy value.
Example: Λόγος = Λ(30→3+0=3) + ό(70→7+0=7) + γ(3) + ο(70→7+0=7) + ς(200→2+0+0=2) = 22
Digital Ordinal Value (Αριθμός Τακτικός Ψηφιακός, Arithmos Taktikos Psephiakos):
Why: Reduces the ordinal value of each letter to a single digit by summing its digits (e.g., 11th letter → 1+1=2). It seeks the fundamental essence of the letter's sequential position.
When: Used when both the order of letters and a reduced numerical essence are considered important.
Example: Λόγος = Λ(11→1+1=2) + ό(15→1+5=6) + γ(3) + ο(15→1+5=6) + ς(18→1+8=9) = 26
#### 3. Mathematical Operations
Square Value (Αριθμός Τετράγωνος, Arithmos Tetragonos):
Why: Squaring each letter's standard value amplifies its significance, suggesting foundational strength or manifested power, similar to Hebrew square value.
When: Used to explore the magnified force or structural importance of words or names, particularly in contexts influenced by Pythagorean thought where numbers and shapes had deep meaning.
Example: Θεός = Θ(9²=81) + ε(5²=25) + ό(70²=4900) + ς(200²=40000) = 45006
Cubic Value (Αριθμός Κύβος, Arithmos Kyvos):
Why: Cubing values suggests a complete, three-dimensional realization or a profound amplification of the letter's essence.
When: Used less frequently, but applied to understand the fullest possible expression or cosmic significance of a term.
Example: Θεός = Θ(9³=729) + ε(5³=125) + ό(70³=343000) + ς(200³=8000000) = 8343854
Triangular Value (Αριθμός Τριγωνικός, Arithmos Trigonikos):
Why: Uses triangular numbers (sum of integers up to n) for each letter's standard value. This implies an unfolding, generative, or cumulative power.
When: Applied in more esoteric or Pythagorean-influenced analyses to explore the developmental or summative energy of a word.
Example: Θεός = Θ(triangular of 9 = 45) + ε(triangular of 5 = 15) + ό(triangular of 70 = 2485) + ς(triangular of 200 = 20100) = 22645
Note: Triangular number of n = n(n+1)/2
Ordinal Square Value (Αριθμός Τακτικός Τετράγωνος, Arithmos Taktikos Tetragonos):
Why: Squares the ordinal (positional) values of the letters, amplifying the significance of their sequence.
When: Used when the order of letters is paramount and one wishes to explore the magnified impact of this sequential arrangement.
Example: Θεός = Θ(9th letter, 9²=81) + ε(5th letter, 5²=25) + ό(15th letter, 15²=225) + ς(18th letter, 18²=324) = 655
#### 4. Full Spelling Methods (Πλήρωμα - Pleroma, "Fullness")
Similar to Hebrew Milui, these methods use the numerical values of the names of the Greek letters.
Full Value (Αριθμός Πλήρης, Arithmos Pleres):
Why: Summing the standard isopsephy values of the spelled-out names of each letter (e.g., Alpha = A+L+P+H+A). This is believed to access a deeper, more complete layer of meaning, reflecting the "fullness" (Pleroma) of the letter's concept.
When: Used in more profound Gnostic or Neopythagorean interpretations to uncover hidden dimensions of sacred names or key terms.
Example: α = ἄλφα = α(1) + λ(30) + φ(500) + α(1) = 532
Example word: αβ = ἄλφα(532) + βῆτα(β(2)+η(8)+τ(300)+α(1)=311) = 843
Name Value (Αριθμός Ονοματικός, Arithmos Onomatikos): The original document uses this for multiplication of spelled-out letter name values. As with Hebrew, this is highly uncommon for traditional isopsephy.
Why: If multiplication is intended, it would signify a vast interconnectedness or a multiplicative creative force. However, this is not a standard recognized Greek isopsephy method.
When: Its use would be highly speculative and likely modern or idiosyncratic.
Example (if multiplication): αβ = ἄλφα(532) × βῆτα(311) = 165,452
Individual Value (Αριθμός Εξατομικευμένος, Arithmos Exatomikeumenos): This appears to be identical to "Full Value" (Arithmos Pleres) as described.
Why: Same as Full Value: to access a deeper layer of meaning through the letter names.
When: Same as Full Value.
Example: α = ἄλφα = α(1) + λ(30) + φ(500) + α(1) = 532
Example word: αβ = α(532) + β(311) = 843
Hidden Value (Αριθμός Κρυμμένος, Arithmos Krymmenos):
Why: The value of the spelled-out letter name minus the simple value of the letter itself. This points to the concealed or inner aspect of the letter's power.
When: Used to explore the unmanifest or underlying essence of letters within a word, similar to Hebrew Mispar Ne'elam.
Example: α = ἄλφα - α = (532) - 1 = 531
Example word: αβ = (ἄλφα-α) + (βῆτα-β) = 531 + (311-2=309) = 840
Face Value (Αριθμός Προσωπείο, Arithmos Prosopeio):
Why: The full spelled-out value of the first letter plus the standard values of the remaining letters. Emphasizes the primary or initiating aspect of the word.
When: Used when the initial letter is considered key to the word's meaning, setting the tone for the rest.
Example word: αβ = ἄλφα(532) + β(2) = 534
Example word: αβγα = ἄλφα(532) + β(2) + γ(3) + α(1) = 538
Example with letter name: βῆτα (the name of letter Beta) = (Isopsephy of βῆτα = 311) + η(8) + τ(300) + α(1) = 620
Back Value (Αριθμός Οπίσθιος, Arithmos Opisthios):
Why: Standard values of all letters except the last, plus the full spelled-out value of the last letter. Emphasizes the concluding or resulting aspect.
When: Used when the final letter is seen as the culmination or ultimate expression of the word's meaning.
Example word: αβ = α(1) + βῆτα(311) = 312
Example word: αβγα = α(1) + β(2) + γ(3) + ἄλφα(532) = 538
Example with letter name: βῆτα (the name of letter Beta) = β(2) + η(8) + τ(300) + ἄλφα(532) = 842
#### 5. Collective Methods
Collective Value (Αριθμός Συλλογικός, Arithmos Syllogikos):
Why: Standard value plus the number of letters in the word. This acknowledges the word as a complete entity, with each letter contributing its presence.
When: Used to consider the holistic meaning of a word, beyond the simple sum of letter values.
Example: Ἰησοῦς = Ἰ(10) + η(8) + σ(200) + ο(70) + ῦ(400) + ς(200) = 888 + 6 letters = 894
Name Collective Value (Αριθμός Ονοματικός Συλλογικός, Arithmos Onomatikos Syllogikos):
Why: The "Full Value" (sum of spelled-out letter names) plus the number of letters. Combines the depth of letter names with the holistic sense of the word.
When: For advanced interpretations considering both inner essence and collective unity.
Example: αβ = ἄλφα(532) + βῆτα(311) = 843 + 2 letters = 845
Regular plus Collective (Κανονικός Συν Συλλογικός, Kanonikos Syn Syllogikos - often simply "Kolel" +1):
Why: Standard value plus 1. The "+1" can represent the word as a unified whole or a higher unity.
When: Commonly used to bridge small numerical differences and establish connections, similar to Hebrew Kolel.
Example: Ἰησοῦς = 888 + 1 = 889
#### 6. Substitution Methods
Reverse Substitution (Αντίστροφη Αντικατάσταση, Antistrōphē Antikatastasē - "Temurah"):
Why: Reverses the alphabet (Α↔Ω, Β↔Ψ, etc.). This is the Greek equivalent of AtBash, used to find inverse meanings or hidden connections.
When: Used in esoteric interpretations to reveal concealed aspects of words or to link seemingly opposite concepts.
Example: αβγ → ωψχ = ω(800) + ψ(700) + χ(600) = 2100
Note: α is replaced with ω, β with ψ, γ with χ, etc.
Pair Matching (Αντιστοίχιση Ζεύγους, Antistoichisi Zeugous - "Temurah"):
Why: Divides the alphabet into two halves and pairs letters (e.g., 1st with 13th, 2nd with 14th). This is the Greek equivalent of Albam, creating systematic transformations.
When: Used to uncover alternative meanings or correspondences by structured letter permutation.
Example: αβγ → νξο (if α↔ν, β↔ξ, γ↔ο based on a 12/12 split) or the example given: αβγ → λκι = λ(30) + κ(20) + ι(10) = 60. This example (α↔λ, β↔κ, γ↔ι) is unusual as it pairs early letters with middle letters that are not their direct counterparts in a simple split. It might refer to a specific, non-standard pairing system. Standard Albam-like pairing would be α (1st) with μ (13th), β (2nd) with ν (14th), etc.
Next Letter Value (Αριθμός Επόμενος, Arithmos Epomenos):
Why: Each letter is replaced by the value of the following letter in the alphabet. This suggests a progression or looking towards what comes next.
When: A less common method, possibly used to explore themes of sequence, development, or cause-and-effect.
Example: αβγ = (value of β=2) + (value of γ=3) + (value of δ=4) = 9
Note: Each letter is replaced with the value of the next letter in the alphabet. Ω would typically cycle to Α or have no value.
Cyclical Permutation (Κυκλική Μετάθεση, Kyklikē Metathesē):
Why: Shifts all letters one position (or more) to the left (or right), with the first letter moving to the end (or vice versa). This explores anagrammatic relationships and the idea that meaning can be found by rearranging components.
When: Used to generate related terms or to see how a slight shift in structure alters the numerical value and potential meaning.
Example: αβγδ → βγδα = β(2) + γ(3) + δ(4) + α(1) = 10
Note: Shifts all letters one position to the left.

---

## English Gematria (Trigrammaton Qabbalah, TQ)

### TQ Value Table
| Letter | TQ Value | Letter | TQ Value |
|--------|----------|--------|----------|
| i, I   | 0        | o, O   | 10       |
| l, L   | 1        | g, G   | 11       |
| c, C   | 2        | f, F   | 12       |
| h, H   | 3        | e, E   | 13       |
| p, P   | 4        | r, R   | 14       |
| a, A   | 5        | s, S   | 15       |
| x, X   | 6        | q, Q   | 16       |
| j, J   | 7        | k, K   | 17       |
| w, W   | 8        | y, Y   | 18       |
| t, T   | 9        | z, Z   | 19       |
| b, B   | 20       | m, M   | 21       |
| v, V   | 22       | d, D   | 23       |
| n, N   | 24       | u, U   | 25       |

### Method Categories
- **Standard TQ Value:**
  Why: This is the basic application of this specific English gematria cipher. The rationale for the specific letter assignments in TQ would be tied to the unique teachings or symbolism of the system's originators. It aims to find numerical significance in English words based on this particular key.
  When: Used by practitioners of the TQ system to analyze English texts, names, or concepts, seeking correspondences and hidden meanings according to its specific numerical framework.
  Example: LIGHT = L(1) + I(0) + G(11) + H(3) + T(9) = 24
- **TQ Reduction:**
  Why: Reduces the final TQ sum to a single digit (0-9). This seeks a more fundamental or essential numerical value within the TQ system, similar to reduction in Hebrew or Greek methods.
  When: Used to simplify TQ values for comparison or to find a core "digit-essence" of a word according to this cipher.
  Example: LIGHT = 24 → 2+4 = 6
  Note: Continue adding digits until a single digit is reached.
- **TQ Square Value:**
  Why: Squares each letter's TQ value, then sums them. This amplifies the significance of each letter's assigned TQ value, suggesting a magnified power or foundational aspect within this system.
  When: Used to explore a more potent or structurally emphasized numerical value of English words according to the TQ cipher.
  Example: LIGHT = L(1²=1) + I(0²=0) + G(11²=121) + H(3²=9) + T(9²=81) = 212
- **TQ Triangular Value:**
  Why: Applies the triangular number formula to each letter's TQ value. This would imply an unfolding or cumulative energy associated with each letter's TQ assignment.
  When: A more esoteric application within the TQ system, used to explore the generative or developing potential of words based on their TQ values.
  Example: LIGHT = L(triangular of 1 = 1) + I(triangular of 0 = 0) + G(triangular of 11 = 66) + H(triangular of 3 = 6) + T(triangular of 9 = 45) = 118
  Note: Triangular number of n = n(n+1)/2. For n=0, it's 0.
- **TQ Letter Position:**
  Why: Multiplies each letter's TQ value by its position in the word. This method gives weight to where a letter appears, suggesting that its contribution is modified by its sequence.
  When: Used when the order and placement of letters within a word are considered significant in conjunction with their TQ values. It can highlight letters at key positions.
  Example: LIGHT = L(1×1=1) + I(0×2=0) + G(11×3=33) + H(3×4=12) + T(9×5=45) = 91
  Note: Position is counted from 1 (first letter) to n (last letter).

---

## Coptic Gematria

### Coptic Alphabet Values (Standard)
| Letter | Name   | Value |
|--------|--------|-------|
| Ⲁ ⲁ    | Alpha  | 1     |
| Ⲃ ⲃ    | Vita   | 2     |
| Ⲅ ⲅ    | Gamma  | 3     |
| Ⲇ ⲇ    | Delta  | 4     |
| Ⲉ ⲉ    | Ei     | 5     |
| Ⲋ ⲋ    | So     | 6     |
| Ⲍ ⲍ    | Zēta   | 7     |
| Ⲏ ⲏ    | Ēta    | 8     |
| Ⲑ ⲑ    | Thēta  | 9     |
| Ⲓ ⲓ    | Yota   | 10    |
| Ⲕ ⲕ    | Kappa  | 20    |
| Ⲗ ⲗ    | Laula  | 30    |
| Ⲙ ⲙ    | Mi     | 40    |
| Ⲛ ⲛ    | Ne     | 50    |
| Ⲝ ⲝ    | Ksi    | 60    |
| Ⲟ ⲟ    | O      | 70    |
| Ⲡ ⲡ    | Pi     | 80    |
| Ⲣ ⲣ    | Ro     | 100   |
| Ⲥ ⲥ    | Sima   | 200   |
| Ⲧ ⲧ    | Tau    | 300   |
| Ⲩ ⲩ    | Epsilon| 400   |
| Ⲫ ⲫ    | Fi     | 500   |
| Ⲭ ⲭ    | Ki     | 600   |
| Ⲯ ⲯ    | Epsi   | 700   |
| Ⲱ ⲱ    | Ō      | 800   |
| Ϣ ϣ    | Šai    | 900   |
| Ϥ ϥ    | Fai    | 90    |
| Ϧ ϧ    | Ḫai    | 900   |
| Ϩ ϩ    | Hori   | 900   |
| Ϫ ϫ    | Djandja| 90    |
| Ϭ ϭ    | Kyima  | 90    |
| Ϯ ϯ    | Ti     | 300   |

### Method Categories
- **Standard Value (Ⲁⲣⲓⲑⲙⲟⲥ Ⲕⲁⲛⲟⲛⲓⲕⲟⲥ, Arithmos Kanonikos):**
  Why: The fundamental method, assigning the established Greek-derived numerical value to each Coptic letter, including those for the Demotic additions. It represents the letter's inherent numerical power.
  When: Used for finding numerical equivalencies in Coptic texts, particularly Gnostic gospels, magical texts, and Christian liturgical or scriptural documents written in Coptic.
  Example: ⲛⲟⲩⲧⲉ (God) = ⲛ(50) + ⲟ(70) + ⲩ(400) + ⲧ(300) + ⲉ(5) = 825
- **Reduced Value (Ⲁⲣⲓⲑⲙⲟⲥ Ⲙⲓⲕⲣⲟⲥ, Arithmos Mikros):**
  Why: Reduces letter values (or the total sum) to a single digit. This seeks the most fundamental numerical essence of Coptic words, mirroring the practice in Hebrew and Greek systems.
  When: Used to simplify Coptic isopsephy values for comparison, to find core numerical links, or in contexts influenced by Pythagorean or Gnostic numerology focusing on the digits 1-9.
  Example: ⲛⲟⲩⲧⲉ = ⲛ(50→5) + ⲟ(70→7) + ⲩ(400→4) + ⲧ(300→3) + ⲉ(5) = 24→6
  Note: Values are reduced to a single digit (e.g., 50→5, 400→4 by summing digits).

---

*This document is the base reference for all gematria systems and methods included in the initial release of the Advanced Gematria App.*

---

## Transliteration Systems

For users who prefer typing in Latin characters, the Advanced Gematria App supports transliteration systems for Hebrew, Greek, and Coptic. These systems allow you to input text using standard ASCII characters, which the application will then convert to the appropriate script for gematria calculations.

### Hebrew Transliteration System

Hebrew letters can be entered using ASCII characters according to the table below. Final forms (Sophit) of the five letters Kaph, Mem, Nun, Pe, and Tsade are represented by their uppercase equivalents.

| Hebrew Letter | ASCII Code | Final Form | Final ASCII | Standard Value | Final Value | Name |
|--------------|------------|------------|-------------|----------------|-------------|------|
| א            | a          | -          | -           | 1              | -           | Alef |
| ב            | b          | -          | -           | 2              | -           | Bet  |
| ג            | g          | -          | -           | 3              | -           | Gimel|
| ד            | d          | -          | -           | 4              | -           | Dalet|
| ה            | h          | -          | -           | 5              | -           | He   |
| ו            | v          | -          | -           | 6              | -           | Vav  |
| ז            | z          | -          | -           | 7              | -           | Zayin|
| ח            | x          | -          | -           | 8              | -           | Chet |
| ט            | j          | -          | -           | 9              | -           | Tet  |
| י            | y          | -          | -           | 10             | -           | Yod  |
| כ            | k          | ך          | K           | 20             | 500         | Kaph |
| ל            | l          | -          | -           | 30             | -           | Lamed|
| מ            | m          | ם          | M           | 40             | 600         | Mem  |
| נ            | n          | ן          | N           | 50             | 700         | Nun  |
| ס            | s          | -          | -           | 60             | -           | Samekh|
| ע            | o          | -          | -           | 70             | -           | Ayin |
| פ            | p          | ף          | P           | 80             | 800         | Pe   |
| צ            | c          | ץ          | C           | 90             | 900         | Tsade|
| ק            | q          | -          | -           | 100            | -           | Qof  |
| ר            | r          | -          | -           | 200            | -           | Resh |
| ש            | $          | -          | -           | 300            | -           | Shin |
| ת            | t          | -          | -           | 400            | -           | Tav  |

#### Examples of Hebrew Transliteration

- `mlK` would be transliterated as "מלך" (melekh, "king")
- `dbryM` would be transliterated as "דברים" (devarim, "words")
- `$lM` would be transliterated as "שלם" (shalom with final mem)
- `arC` would be transliterated as "ארץ" (eretz, "land")

When using methods that incorporate Sophit values, the final letters will automatically be assigned their proper Sophit values.

### Greek Transliteration System (Simplified)

For Greek text, the application supports a simplified transliteration system that focuses on the standard 24 letters of the Greek alphabet, without diacritics or archaic letters.

#### Basic Greek Transliteration Rules

1. Each Greek letter is represented by a single Latin character
2. The system ignores diacritical marks (accents, breathing marks)
3. Final sigma (ς) is automatically handled by the application

| Greek Letter | ASCII Code | Value | Name    |
|--------------|------------|-------|---------|
| Α α          | a          | 1     | Alpha   |
| Β β          | b          | 2     | Beta    |
| Γ γ          | g          | 3     | Gamma   |
| Δ δ          | d          | 4     | Delta   |
| Ε ε          | e          | 5     | Epsilon |
| Ζ ζ          | z          | 7     | Zeta    |
| Η η          | h          | 8     | Eta     |
| Θ θ          | q          | 9     | Theta   |
| Ι ι          | i          | 10    | Iota    |
| Κ κ          | k          | 20    | Kappa   |
| Λ λ          | l          | 30    | Lambda  |
| Μ μ          | m          | 40    | Mu      |
| Ν ν          | n          | 50    | Nu      |
| Ξ ξ          | c          | 60    | Xi      |
| Ο ο          | o          | 70    | Omicron |
| Π π          | p          | 80    | Pi      |
| Ρ ρ          | r          | 100   | Rho     |
| Σ σ/ς        | s          | 200   | Sigma   |
| Τ τ          | t          | 300   | Tau     |
| Υ υ          | u          | 400   | Upsilon |
| Φ φ          | f          | 500   | Phi     |
| Χ χ          | x          | 600   | Chi     |
| Ψ ψ          | y          | 700   | Psi     |
| Ω ω          | w          | 800   | Omega   |

#### Examples of Greek Transliteration

- `logos` would be transliterated as "λογος" (logos, "word")
- `anqrwpos` would be transliterated as "ανθρωπος" (anthropos, "human")
- `qeos` would be transliterated as "θεος" (theos, "god")

The application will automatically convert the transliterated input to proper Greek characters and then apply the appropriate gematria calculations.
