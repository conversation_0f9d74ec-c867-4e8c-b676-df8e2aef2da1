"""
Gematria Dictionary Dialog.

This module provides a dialog for displaying and interacting with
gematria dictionary results from document analysis.

Author: Assistant
Created: 2024-12-28
Last Modified: 2024-12-28
Dependencies: PyQt6, typing, loguru
"""

from typing import Dict, List, Optional
from loguru import logger

from PyQt6.QtCore import Qt, QSortFilterProxyModel, pyqtSignal
from PyQt6.QtGui import QFont, QStandardItemModel, QStandardItem
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableView, QComboBox, QLineEdit, QSpinBox, QGroupBox,
    QTextEdit, QSplitter, QHeaderView, QMessageBox, QProgressBar,
    QCheckBox, QTabWidget, QWidget, QFileDialog, QRadioButton, QButtonGroup
)

from gematria.models.calculation_type import CalculationType, Language
from document_manager.models.gematria_dictionary_entry import (
    GematriaDictionaryEntry,
    GematriaDictionaryResult,
    VerseAnalysisEntry,
    GlobalAnalysisSummary
)
from document_manager.services.gematria_dictionary_service import GematriaDictionaryService
from document_manager.ui.dialogs.verse_breakdown_dialog import show_verse_breakdown


class GematriaDictionaryDialog(QDialog):
    """Dialog for displaying gematria dictionary results."""
    
    # Signal emitted when a word is selected for further analysis
    word_selected = pyqtSignal(str, dict)  # word, gematria_values
    
    def __init__(self, parent=None):
        """Initialize the dialog."""
        super().__init__(parent)
        self.setWindowTitle("Gematria Dictionary")
        self.setModal(False)
        self.resize(1200, 800)
        
        # Services
        self.dictionary_service = GematriaDictionaryService()
        
        # Data
        self.current_result: Optional[GematriaDictionaryResult] = None
        self.current_text: str = ""  # Store the current text for verse breakdown
        self.table_model = QStandardItemModel()
        self.proxy_model = QSortFilterProxyModel()
        
        # UI setup
        self._init_ui()
        self._setup_table()
        self._connect_signals()
        
        logger.debug("GematriaDictionaryDialog initialized")
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Create main splitter
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # Top section: Input and controls
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        
        # Document input section
        input_group = QGroupBox("Document Analysis")
        input_layout = QVBoxLayout(input_group)
        
        # Text input
        self.text_input = QTextEdit()
        self.text_input.setPlaceholderText(
            "Paste your document text here...\n\n"
            "For holy books, ensure verse numbers are at the beginning of each line.\n"
            "Example:\n"
            "1 In the beginning was the Word\n"
            "2 And the Word was with God\n"
            "3 And the Word was God"
        )
        self.text_input.setMaximumHeight(150)
        input_layout.addWidget(self.text_input)
        
        # Import button row
        import_layout = QHBoxLayout()
        
        self.import_btn = QPushButton("Import Text File")
        self.import_btn.setToolTip("📚 Import text from holy books collection (assets/holy_books) or browse other files (.txt, .rtf, .docx)")
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        import_layout.addWidget(self.import_btn)
        
        # Clear button
        self.clear_btn = QPushButton("Clear Text")
        self.clear_btn.setToolTip("Clear the text input area")
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        import_layout.addWidget(self.clear_btn)
        
        import_layout.addStretch()
        input_layout.addLayout(import_layout)
        
        # Controls row
        controls_layout = QHBoxLayout()
        
        # Document title
        controls_layout.addWidget(QLabel("Title:"))
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("Document title...")
        controls_layout.addWidget(self.title_input)
        
        # Language selection
        controls_layout.addWidget(QLabel("Primary Language:"))
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "Auto-detect",
            "Hebrew",
            "Greek", 
            "English (TQ)",
            "Mixed"
        ])
        controls_layout.addWidget(self.language_combo)
        
        # Analyze button
        self.analyze_btn = QPushButton("Analyze Document")
        self.analyze_btn.setStyleSheet("QPushButton { background-color: #3498db; color: white; font-weight: bold; }")
        controls_layout.addWidget(self.analyze_btn)
        
        input_layout.addLayout(controls_layout)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        input_layout.addWidget(self.progress_bar)
        
        top_layout.addWidget(input_group)
        
        # Results section with tabs
        results_group = QGroupBox("Analysis Results")
        results_layout = QVBoxLayout(results_group)
        
        # Create tab widget for different views
        self.results_tabs = QTabWidget()
        
        # Word Dictionary Tab
        word_dict_widget = QWidget()
        word_dict_layout = QVBoxLayout(word_dict_widget)
        
        # Statistics
        self.stats_label = QLabel("No analysis performed yet.")
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        word_dict_layout.addWidget(self.stats_label)
        
        # Filters
        filters_layout = QHBoxLayout()
        
        self.word_filter = QLineEdit()
        self.word_filter.setPlaceholderText("Filter by word...")
        filters_layout.addWidget(QLabel("Word:"))
        filters_layout.addWidget(self.word_filter)
        
        self.language_filter = QComboBox()
        self.language_filter.addItems(["All Languages", "English", "Hebrew", "Greek", "Unknown"])
        filters_layout.addWidget(QLabel("Language:"))
        filters_layout.addWidget(self.language_filter)
        
        self.value_filter = QSpinBox()
        self.value_filter.setRange(0, 9999)
        self.value_filter.setSpecialValueText("Any Value")
        filters_layout.addWidget(QLabel("Value:"))
        filters_layout.addWidget(self.value_filter)
        
        self.calc_type_filter = QComboBox()
        self.calc_type_filter.addItem("All Types")
        filters_layout.addWidget(QLabel("Calc Type:"))
        filters_layout.addWidget(self.calc_type_filter)
        
        self.clear_filters_btn = QPushButton("Clear Filters")
        self.clear_filters_btn.setStyleSheet("QPushButton { background-color: #95a5a6; color: white; }")
        filters_layout.addWidget(self.clear_filters_btn)
        
        filters_layout.addStretch()
        word_dict_layout.addLayout(filters_layout)
        
        # Table
        self.table_view = QTableView()
        self.table_model = QStandardItemModel()
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.table_model)
        self.table_view.setModel(self.proxy_model)
        self.table_view.setSortingEnabled(True)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        word_dict_layout.addWidget(self.table_view)
        
        self.results_tabs.addTab(word_dict_widget, "Word Dictionary")
        
        # Verse Analysis Tab
        verse_analysis_widget = QWidget()
        verse_analysis_layout = QVBoxLayout(verse_analysis_widget)
        
        # Verse analysis statistics
        self.verse_stats_label = QLabel("No verse analysis available.")
        self.verse_stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        verse_analysis_layout.addWidget(self.verse_stats_label)
        
        # Instructions for verse breakdown
        instructions_label = QLabel("💡 Tip: Double-click on any verse row to see detailed word-by-word breakdown")
        instructions_label.setStyleSheet("color: #6c757d; font-style: italic; padding: 2px;")
        verse_analysis_layout.addWidget(instructions_label)
        
        # Verse analysis table
        self.verse_table_view = QTableView()
        self.verse_table_model = QStandardItemModel()
        self.verse_table_view.setModel(self.verse_table_model)
        self.verse_table_view.setSortingEnabled(True)
        self.verse_table_view.setAlternatingRowColors(True)
        self.verse_table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        verse_analysis_layout.addWidget(self.verse_table_view)
        
        # Global analysis section
        global_analysis_group = QGroupBox("Global Analysis Summary")
        global_analysis_layout = QVBoxLayout(global_analysis_group)
        
        self.global_analysis_text = QTextEdit()
        self.global_analysis_text.setMaximumHeight(150)
        self.global_analysis_text.setReadOnly(True)
        self.global_analysis_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
            }
        """)
        global_analysis_layout.addWidget(self.global_analysis_text)
        
        verse_analysis_layout.addWidget(global_analysis_group)
        
        self.results_tabs.addTab(verse_analysis_widget, "Verse Analysis")
        
        results_layout.addWidget(self.results_tabs)
        
        # Button layout
        button_layout = QHBoxLayout()
        
        button_layout.addStretch()
        
        self.export_btn = QPushButton("Export Results")
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("QPushButton { background-color: #27ae60; color: white; }")
        button_layout.addWidget(self.export_btn)
        
        self.close_btn = QPushButton("Close")
        self.close_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; }")
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # Add widgets to splitter
        splitter.addWidget(top_widget)
        splitter.addWidget(results_group)
        splitter.setSizes([300, 500])
    
    def _setup_table(self):
        """Setup the results table."""
        # Setup proxy model for filtering and sorting
        self.proxy_model.setSourceModel(self.table_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.table_view.setModel(self.proxy_model)
        
        # Table settings
        self.table_view.setSortingEnabled(True)
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
    
    def _connect_signals(self):
        """Connect UI signals."""
        self.analyze_btn.clicked.connect(self._analyze_document)
        self.close_btn.clicked.connect(self.close)
        self.export_btn.clicked.connect(self._export_results)
        self.clear_filters_btn.clicked.connect(self._clear_filters)
        self.import_btn.clicked.connect(self._import_text_file)
        self.clear_btn.clicked.connect(self._clear_text)
        
        # Filter signals
        self.word_filter.textChanged.connect(self._apply_filters)
        self.language_filter.currentTextChanged.connect(self._apply_filters)
        self.value_filter.valueChanged.connect(self._apply_filters)
        self.calc_type_filter.currentTextChanged.connect(self._apply_filters)
        
        # Table selection
        self.table_view.doubleClicked.connect(self._on_word_double_clicked)
        self.verse_table_view.doubleClicked.connect(self._on_verse_double_clicked)
    
    def _analyze_document(self):
        """Analyze the document text."""
        text = self.text_input.toPlainText().strip()
        if not text:
            QMessageBox.warning(self, "Warning", "Please enter some text to analyze.")
            return
        
        title = self.title_input.text().strip() or "Untitled Document"
        
        # Store the current text for verse breakdown
        self.current_text = text
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
        self.analyze_btn.setEnabled(False)
        
        try:
            # Perform analysis
            self.current_result = self.dictionary_service.analyze_document(text, title)
            
            # Update UI
            self._update_results_display()
            self._populate_table()
            self._populate_verse_analysis()
            self._update_filter_options()
            
            # Update status
            self.stats_label.setText(
                f"Analysis complete: {self.current_result.unique_words} unique words "
                f"from {self.current_result.total_words} total words"
            )
            
            self.export_btn.setEnabled(True)
            
        except Exception as e:
            logger.error(f"Error analyzing document: {e}")
            QMessageBox.critical(self, "Error", f"Failed to analyze document:\n{str(e)}")
        
        finally:
            self.progress_bar.setVisible(False)
            self.analyze_btn.setEnabled(True)
    
    def _update_results_display(self):
        """Update the results display."""
        if not self.current_result:
            return
        
        # Update results label
        calc_types = ", ".join(self.current_result.calculation_types_used)
        self.stats_label.setText(
            f"Document: {self.current_result.document_title} | "
            f"{self.current_result.unique_words} unique words | "
            f"Calculation types: {calc_types}"
        )
    
    def _populate_table(self):
        """Populate the table with results."""
        if not self.current_result:
            return
        
        # Clear existing data
        self.table_model.clear()
        
        # Setup headers
        headers = ["Word", "Language", "Frequency", "Verses"]
        
        # Add gematria value columns
        for calc_type in sorted(self.current_result.calculation_types_used):
            headers.append(calc_type)
        
        self.table_model.setHorizontalHeaderLabels(headers)
        
        # Populate data
        for entry in self.current_result.entries:
            row_items = []
            
            # Basic info
            row_items.append(QStandardItem(entry.word))
            row_items.append(QStandardItem(entry.language.value))
            row_items.append(QStandardItem(str(entry.frequency)))
            
            # Verses
            verses_text = ", ".join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else "N/A"
            row_items.append(QStandardItem(verses_text))
            
            # Gematria values
            for calc_type in sorted(self.current_result.calculation_types_used):
                value = entry.gematria_values.get(calc_type, "")
                row_items.append(QStandardItem(str(value) if value else ""))
            
            self.table_model.appendRow(row_items)
        
        # Resize columns
        self.table_view.resizeColumnsToContents()
    
    def _populate_verse_analysis(self):
        """Populate the verse analysis table and global summary."""
        if not self.current_result:
            self.verse_stats_label.setText("No analysis available.")
            self.global_analysis_text.clear()
            return
        
        # Check if we have either verse analysis or line analysis
        has_verses = bool(self.current_result.verse_analysis)
        has_lines = bool(self.current_result.line_analysis)
        
        if not has_verses and not has_lines:
            self.verse_stats_label.setText("No verse or line analysis available.")
            self.global_analysis_text.clear()
            return
        
        # Update verse statistics
        verse_count = len(self.current_result.verse_analysis) if has_verses else 0
        line_count = len(self.current_result.line_analysis) if has_lines else 0
        chapter_count = len(self.current_result.chapter_summaries)
        
        # Display appropriate statistics based on text structure
        if self.current_result.text_structure:
            structure_type = self.current_result.text_structure.structure_type.value
            confidence = self.current_result.text_structure.confidence
            
            if verse_count > 0:
                if chapter_count > 0:
                    self.verse_stats_label.setText(
                        f"📖 Verse Analysis: {verse_count} verses across {chapter_count} chapters\n"
                        f"📋 Structure: {structure_type.replace('_', ' ').title()} (confidence: {confidence:.1%})"
                    )
                else:
                    self.verse_stats_label.setText(
                        f"📖 Verse Analysis: {verse_count} verses analyzed\n"
                        f"📋 Structure: {structure_type.replace('_', ' ').title()} (confidence: {confidence:.1%})"
                    )
            elif line_count > 0:
                self.verse_stats_label.setText(
                    f"📄 Line Analysis: {line_count} lines analyzed\n"
                    f"📋 Structure: {structure_type.replace('_', ' ').title()} (confidence: {confidence:.1%})"
                )
            else:
                self.verse_stats_label.setText(
                    f"📋 Structure: {structure_type.replace('_', ' ').title()} (confidence: {confidence:.1%})\n"
                    f"No verse or line analysis available."
                )
        else:
            # Fallback for older results without structure analysis
            if verse_count > 0:
                if chapter_count > 0:
                    self.verse_stats_label.setText(
                        f"Verse Analysis: {verse_count} verses across {chapter_count} chapters"
                    )
                else:
                    self.verse_stats_label.setText(
                        f"Verse Analysis: {verse_count} verses analyzed"
                    )
            elif line_count > 0:
                self.verse_stats_label.setText(
                    f"Line Analysis: {line_count} lines analyzed"
                )
        
        # Clear existing data
        self.verse_table_model.clear()
        
        # Setup headers - only include calculation types that are actually used
        calc_types = self.current_result.calculation_types_used
        
        # Determine if we have chapters and what type of analysis to display
        has_chapters = bool(self.current_result.chapter_summaries)
        
        # Build headers dynamically based on structure
        if has_verses:
            # Traditional verse analysis
            if has_chapters:
                headers = ["Chapter", "Verse #", "Words", "Numbers", "Text"]
            else:
                headers = ["Verse #", "Words", "Numbers", "Text"]
        elif has_lines:
            # Line-by-line analysis
            headers = ["Line #", "Words", "Numbers", "Text"]
            
            # Add leading number column if any lines have leading numbers
            if any(line.has_leading_number for line in self.current_result.line_analysis):
                headers.insert(1, "Leading #")
        else:
            # Fallback
            headers = ["Item #", "Words", "Numbers", "Text"]
        
        # Add gematria columns for each calculation type
        for calc_type in calc_types:
            headers.append(f"{calc_type}")
        
        # Add Numbers Total and Total columns
        headers.append("Numbers Total")
        if has_verses:
            headers.append("Verse Total")
        else:
            headers.append("Line Total")
        
        self.verse_table_model.setHorizontalHeaderLabels(headers)
        
        # Populate data based on analysis type
        if has_verses:
            self._populate_verse_data(calc_types, has_chapters)
        elif has_lines:
            self._populate_line_data(calc_types)
        
        # Update global analysis
        self._update_global_analysis()
    
    def _update_global_analysis(self):
        """Update the global analysis summary display."""
        if not self.current_result or not self.current_result.global_analysis:
            self.global_analysis_text.clear()
            return
        
        global_analysis = self.current_result.global_analysis
        
        # Build summary text
        summary_lines = [
            f"📊 GLOBAL ANALYSIS SUMMARY",
            f"{'=' * 50}",
            f"",
            f"📖 Total Verses: {global_analysis.total_verses}",
            f"🔺 Triangular Number (1+2+...+{global_analysis.total_verses}): {global_analysis.triangular_number}",
        ]
        
        # Add chapter information if available
        if self.current_result.chapter_summaries:
            summary_lines.extend([
                f"",
                f"📚 CHAPTER BREAKDOWN:",
                f"{'-' * 30}"
            ])
            
            for chapter in self.current_result.chapter_summaries:
                summary_lines.extend([
                    f"",
                    f"📖 Chapter {chapter.chapter_number}: {chapter.verse_count} verses ({chapter.verse_range})",
                    f"   Words: {chapter.total_words}, Triangular: {chapter.triangular_number}"
                ])
                
                # Show chapter gematria totals
                for calc_type, total in chapter.gematria_totals.items():
                    global_sum = chapter.global_sums.get(calc_type, 0)
                    summary_lines.append(f"   {calc_type}: {total:,} (⊕{global_sum:,})")
        
        summary_lines.extend([
            f"",
            f"📈 DOCUMENT GEMATRIA TOTALS BY CALCULATION TYPE:",
            f"{'-' * 40}"
        ])
        
        # Add calculation type totals
        for calc_type, total_sum in global_analysis.total_gematria_sums.items():
            global_sum = global_analysis.global_sums.get(calc_type, 0)
            summary_lines.extend([
                f"",
                f"🔤 {calc_type}:",
                f"   • Total Gematria: {total_sum:,}",
                f"   • + Triangular:   {global_analysis.triangular_number:,}",
                f"   • = GLOBAL SUM:   {global_sum:,}"
            ])
        
        summary_text = "\n".join(summary_lines)
        self.global_analysis_text.setPlainText(summary_text)
    
    def _update_filter_options(self):
        """Update filter combo box options."""
        if not self.current_result:
            return
        
        # Update calculation type filter
        self.calc_type_filter.clear()
        self.calc_type_filter.addItem("All")
        for calc_type in sorted(self.current_result.calculation_types_used):
            self.calc_type_filter.addItem(calc_type)
    
    def _apply_filters(self):
        """Apply current filters to the table."""
        if not self.current_result:
            return
        
        # Word filter
        word_filter = self.word_filter.text()
        if word_filter:
            self.proxy_model.setFilterRegularExpression(word_filter)
            self.proxy_model.setFilterKeyColumn(0)  # Word column
        else:
            self.proxy_model.setFilterRegularExpression("")
        
        # Additional filtering would be implemented here for other filters
        # For now, we'll keep it simple with just word filtering
    
    def _on_word_double_clicked(self, index):
        """Handle word double-click."""
        if not self.current_result:
            return
        
        # Get the word from the selected row
        source_index = self.proxy_model.mapToSource(index)
        row = source_index.row()
        
        if row < len(self.current_result.entries):
            entry = self.current_result.entries[row]
            self.word_selected.emit(entry.word, entry.gematria_values)
    
    def _on_verse_double_clicked(self, index):
        """Handle verse/line double-click to show detailed breakdown."""
        if not self.current_result or not self.current_text:
            return
        
        # Determine what type of analysis we're dealing with
        has_verses = bool(self.current_result.verse_analysis)
        has_lines = bool(self.current_result.line_analysis)
        has_chapters = bool(self.current_result.chapter_summaries)
        
        if has_verses:
            # Traditional verse analysis
            # Determine if we have chapters to know which column contains the verse number
            verse_column = 1 if has_chapters else 0
            verse_number_item = self.verse_table_model.item(index.row(), verse_column)
            if not verse_number_item:
                return
            
            verse_number_text = verse_number_item.text()
            
            # Skip chapter summary rows (they have text like "(1-66)" instead of a number)
            if not verse_number_text.isdigit():
                return
            
            verse_number = int(verse_number_text)
            
            try:
                # Get detailed verse breakdown
                verse_breakdown = self.dictionary_service.get_verse_breakdown(
                    self.current_text, 
                    verse_number
                )
                
                if verse_breakdown:
                    # Show the breakdown dialog
                    show_verse_breakdown(verse_breakdown, self)
                    logger.info(f"Opened verse breakdown for verse {verse_number}")
                else:
                    QMessageBox.warning(
                        self, 
                        "Verse Not Found", 
                        f"Could not find verse {verse_number} in the document."
                    )
            except Exception as e:
                logger.error(f"Error creating verse breakdown for verse {verse_number}: {e}")
                QMessageBox.critical(
                    self, 
                    "Error", 
                    f"Failed to create verse breakdown:\n{str(e)}"
                )
        
        elif has_lines:
            # Line analysis - show line breakdown
            line_number_item = self.verse_table_model.item(index.row(), 0)  # Line # is always first column
            if not line_number_item:
                return
            
            line_number_text = line_number_item.text()
            if not line_number_text.isdigit():
                return
            
            line_number = int(line_number_text)
            
            # Find the corresponding line entry
            line_entry = None
            for entry in self.current_result.line_analysis:
                if entry.line_number == line_number:
                    line_entry = entry
                    break
            
            if line_entry:
                # Show line details in a message box for now
                # TODO: Create a dedicated line breakdown dialog
                details = [
                    f"Line {line_entry.line_number}:",
                    f"Text: {line_entry.line_text}",
                    f"Words: {line_entry.word_count}",
                    f"Numbers: {line_entry.number_count}",
                    f"Numbers Total: {line_entry.numbers_total}"
                ]
                
                if line_entry.has_leading_number:
                    details.append(f"Leading Number: {line_entry.leading_number}")
                    if line_entry.is_likely_verse:
                        details.append("(Likely verse number)")
                
                details.append("")
                details.append("Gematria Values:")
                for calc_type, value in line_entry.gematria_sums.items():
                    details.append(f"  {calc_type}: {value}")
                
                details.append("")
                details.append("Line Totals:")
                for calc_type, total in line_entry.line_totals.items():
                    details.append(f"  {calc_type}: {total}")
                
                QMessageBox.information(
                    self,
                    f"Line {line_number} Details",
                    "\n".join(details)
                )
            else:
                QMessageBox.warning(
                    self,
                    "Line Not Found",
                    f"Could not find line {line_number} in the analysis."
                )
    
    def _import_text_file(self):
        """Import text from a file."""
        import os
        from pathlib import Path
        
        # Set default directory to assets/holy_books
        default_dir = Path(__file__).parent.parent.parent.parent / "assets" / "holy_books"
        if not default_dir.exists():
            # Fallback to current directory if assets/holy_books doesn't exist
            default_dir = Path.cwd()
        
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("Import Text File - Holy Books Collection")
        file_dialog.setFileMode(QFileDialog.FileMode.ExistingFile)
        file_dialog.setDirectory(str(default_dir))
        file_dialog.setNameFilters([
            "Text files (*.txt)",
            "Rich Text Format (*.rtf)", 
            "Word Documents (*.docx)",
            "All files (*.*)"
        ])
        
        # Add helpful tooltip
        file_dialog.setToolTip(
            "📚 Default location: assets/holy_books\n"
            "Browse the collection of holy books or navigate to other directories."
        )
        
        if file_dialog.exec() == QFileDialog.DialogCode.Accepted:
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                file_path = file_paths[0]
                self._load_file(file_path)
    
    def _load_file(self, file_path: str):
        """Load content from a file."""
        try:
            import os
            from pathlib import Path
            
            file_extension = Path(file_path).suffix.lower()
            
            if file_extension == '.txt':
                # Plain text file
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
            
            elif file_extension == '.rtf':
                # RTF file - basic RTF parsing
                with open(file_path, 'r', encoding='utf-8') as file:
                    rtf_content = file.read()
                    # Simple RTF to text conversion (removes RTF codes)
                    content = self._rtf_to_text(rtf_content)
            
            elif file_extension == '.docx':
                # Word document
                try:
                    import docx
                    doc = docx.Document(file_path)
                    content = '\n'.join([paragraph.text for paragraph in doc.paragraphs])
                except ImportError:
                    QMessageBox.warning(
                        self, 
                        "Missing Dependency", 
                        "To import Word documents, please install python-docx:\n"
                        "pip install python-docx"
                    )
                    return
            
            else:
                # Try to read as plain text
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
            
            # Set the content
            self.text_input.setPlainText(content)
            
            # Set the title from filename if not already set
            if not self.title_input.text().strip():
                filename = Path(file_path).stem
                self.title_input.setText(filename)
            
            logger.info(f"Successfully imported file: {file_path}")
            
        except UnicodeDecodeError:
            # Try different encodings
            encodings = ['latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        content = file.read()
                    self.text_input.setPlainText(content)
                    
                    if not self.title_input.text().strip():
                        filename = Path(file_path).stem
                        self.title_input.setText(filename)
                    
                    logger.info(f"Successfully imported file with {encoding} encoding: {file_path}")
                    return
                except UnicodeDecodeError:
                    continue
            
            QMessageBox.critical(
                self, 
                "Import Error", 
                f"Could not read the file. The file encoding is not supported.\n\n"
                f"File: {file_path}"
            )
            
        except Exception as e:
            logger.error(f"Error importing file {file_path}: {e}")
            QMessageBox.critical(
                self, 
                "Import Error", 
                f"Failed to import file:\n{str(e)}\n\n"
                f"File: {file_path}"
            )
    
    def _rtf_to_text(self, rtf_content: str) -> str:
        """Convert RTF content to plain text (basic implementation)."""
        import re
        
        # Remove RTF control words and groups
        # This is a basic implementation - for full RTF support, use a proper RTF library
        text = re.sub(r'\\[a-z]+\d*\s?', '', rtf_content)  # Remove control words
        text = re.sub(r'[{}]', '', text)  # Remove braces
        text = re.sub(r'\\[^a-z]', '', text)  # Remove escape sequences
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace
        
        return text.strip()
    
    def _clear_text(self):
        """Clear the text input area."""
        reply = QMessageBox.question(
            self,
            "Clear Text",
            "Are you sure you want to clear all text?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.text_input.clear()
            self.current_text = ""
            logger.debug("Text input cleared")
    
    def _clear_filters(self):
        """Clear all filters."""
        self.word_filter.clear()
        self.language_filter.setCurrentText("All Languages")
        self.value_filter.setValue(0)
        self.calc_type_filter.setCurrentText("All Types")
        self._apply_filters()
    
    def _export_results(self):
        """Export results to file."""
        if not self.current_result:
            QMessageBox.warning(self, "No Data", "No analysis results to export.")
            return
        
        # Show export format selection dialog
        export_format = self._show_export_format_dialog()
        if not export_format:
            return
        
        # Get export file path
        file_path = self._get_export_file_path(export_format)
        if not file_path:
            return
        
        try:
            # Export based on selected format
            if export_format == "CSV - Word Dictionary":
                self._export_word_dictionary_csv(file_path)
            elif export_format == "CSV - Verse Analysis":
                self._export_verse_analysis_csv(file_path)
            elif export_format == "JSON - Complete Analysis":
                self._export_complete_json(file_path)
            elif export_format == "Text - Detailed Report":
                self._export_detailed_report(file_path)
            elif export_format == "CSV - Global Summary":
                self._export_global_summary_csv(file_path)
            
            QMessageBox.information(
                self,
                "Export Successful",
                f"Results exported successfully to:\n{file_path}"
            )
            logger.info(f"Exported results to: {file_path}")
            
        except Exception as e:
            logger.error(f"Error exporting results: {e}")
            QMessageBox.critical(
                self,
                "Export Error",
                f"Failed to export results:\n{str(e)}"
            )
    
    def _show_export_format_dialog(self) -> Optional[str]:
        """Show dialog to select export format."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Select Export Format")
        dialog.setModal(True)
        dialog.resize(400, 300)
        
        layout = QVBoxLayout(dialog)
        
        # Header
        header_label = QLabel("Choose the export format:")
        header_label.setStyleSheet("font-weight: bold; font-size: 12pt; margin-bottom: 10px;")
        layout.addWidget(header_label)
        
        # Radio buttons for format selection
        button_group = QButtonGroup(dialog)
        
        formats = [
            ("CSV - Word Dictionary", "Export word dictionary with gematria values"),
            ("CSV - Verse Analysis", "Export verse-by-verse analysis"),
            ("CSV - Global Summary", "Export global analysis summary"),
            ("JSON - Complete Analysis", "Export complete analysis in JSON format"),
            ("Text - Detailed Report", "Export comprehensive text report")
        ]
        
        radio_buttons = []
        for format_name, description in formats:
            radio = QRadioButton(format_name)
            radio.setToolTip(description)
            button_group.addButton(radio)
            radio_buttons.append(radio)
            layout.addWidget(radio)
            
            # Add description
            desc_label = QLabel(f"   {description}")
            desc_label.setStyleSheet("color: #666; font-size: 9pt; margin-bottom: 8px;")
            layout.addWidget(desc_label)
        
        # Set default selection
        if radio_buttons:
            radio_buttons[0].setChecked(True)
        
        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        ok_button = QPushButton("Export")
        ok_button.setStyleSheet("QPushButton { background-color: #27ae60; color: white; font-weight: bold; padding: 8px 16px; }")
        cancel_button = QPushButton("Cancel")
        cancel_button.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; padding: 8px 16px; }")
        
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        # Connect signals
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)
        
        # Show dialog and get result
        if dialog.exec() == QDialog.DialogCode.Accepted:
            for i, radio in enumerate(radio_buttons):
                if radio.isChecked():
                    return formats[i][0]
        
        return None
    
    def _get_export_file_path(self, export_format: str) -> Optional[str]:
        """Get file path for export."""
        # Determine file extension based on format
        if "CSV" in export_format:
            extension = "csv"
            filter_text = "CSV files (*.csv)"
        elif "JSON" in export_format:
            extension = "json"
            filter_text = "JSON files (*.json)"
        else:
            extension = "txt"
            filter_text = "Text files (*.txt)"
        
        # Generate default filename
        doc_title = self.current_result.document_title.replace(" ", "_")
        format_suffix = export_format.split(" - ")[1].replace(" ", "_").lower() if " - " in export_format else "export"
        default_filename = f"{doc_title}_{format_suffix}.{extension}"
        
        # Show save dialog
        file_dialog = QFileDialog(self)
        file_dialog.setWindowTitle("Export Results")
        file_dialog.setFileMode(QFileDialog.FileMode.AnyFile)
        file_dialog.setAcceptMode(QFileDialog.AcceptMode.AcceptSave)
        file_dialog.setNameFilter(filter_text)
        file_dialog.selectFile(default_filename)
        
        if file_dialog.exec() == QFileDialog.DialogCode.Accepted:
            file_paths = file_dialog.selectedFiles()
            if file_paths:
                return file_paths[0]
        
        return None
    
    def _export_word_dictionary_csv(self, file_path: str):
        """Export word dictionary to CSV."""
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['Word', 'Normalized', 'Language', 'Frequency', 'Verses']
            
            # Add gematria value columns
            for calc_type in sorted(self.current_result.calculation_types_used):
                columns.append(f'Gematria_{calc_type.replace(" ", "_")}')
            
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(columns)
            
            # Write data
            for entry in self.current_result.entries:
                row = [
                    entry.word,
                    entry.normalized_word,
                    entry.language.value,
                    entry.frequency,
                    ';'.join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else ''
                ]
                
                # Add gematria values
                for calc_type in sorted(self.current_result.calculation_types_used):
                    value = entry.gematria_values.get(calc_type, '')
                    row.append(value)
                
                writer.writerow(row)
    
    def _export_verse_analysis_csv(self, file_path: str):
        """Export verse analysis to CSV."""
        import csv
        
        if not self.current_result.verse_analysis:
            raise ValueError("No verse analysis data available")
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            # Determine columns
            columns = ['Verse_Number', 'Word_Count', 'Verse_Text']
            
            # Add gematria sum columns
            if self.current_result.verse_analysis:
                calc_types = list(self.current_result.verse_analysis[0].gematria_sums.keys())
                for calc_type in calc_types:
                    columns.append(f'Gematria_{calc_type.replace(" ", "_")}')
            
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(columns)
            
            # Write data
            for verse_entry in self.current_result.verse_analysis:
                row = [
                    verse_entry.verse_number,
                    verse_entry.word_count,
                    verse_entry.verse_text
                ]
                
                # Add gematria sums
                for calc_type in calc_types:
                    value = verse_entry.gematria_sums.get(calc_type, 0)
                    row.append(value)
                
                writer.writerow(row)
    
    def _export_global_summary_csv(self, file_path: str):
        """Export global summary to CSV."""
        import csv
        
        if not self.current_result.global_analysis:
            raise ValueError("No global analysis data available")
        
        global_analysis = self.current_result.global_analysis
        
        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write header
            writer.writerow(['Metric', 'Value'])
            
            # Write basic metrics
            writer.writerow(['Document_Title', self.current_result.document_title])
            writer.writerow(['Total_Verses', global_analysis.total_verses])
            writer.writerow(['Triangular_Number', global_analysis.triangular_number])
            writer.writerow(['Total_Words', self.current_result.total_words])
            writer.writerow(['Unique_Words', self.current_result.unique_words])
            
            # Write calculation type totals
            for calc_type, total_sum in global_analysis.total_gematria_sums.items():
                writer.writerow([f'Total_Gematria_{calc_type.replace(" ", "_")}', total_sum])
            
            # Write global sums
            for calc_type, global_sum in global_analysis.global_sums.items():
                writer.writerow([f'Global_Sum_{calc_type.replace(" ", "_")}', global_sum])
    
    def _export_complete_json(self, file_path: str):
        """Export complete analysis to JSON."""
        import json
        from datetime import datetime
        
        # Convert result to dictionary
        export_data = {
            'export_info': {
                'timestamp': datetime.now().isoformat(),
                'format_version': '1.0',
                'exported_by': 'IsopGem Gematria Dictionary'
            },
            'document_info': {
                'title': self.current_result.document_title,
                'total_words': self.current_result.total_words,
                'unique_words': self.current_result.unique_words,
                'calculation_types_used': self.current_result.calculation_types_used
            },
            'word_dictionary': [],
            'verse_analysis': [],
            'global_analysis': None
        }
        
        # Add word dictionary
        for entry in self.current_result.entries:
            word_data = {
                'word': entry.word,
                'normalized_word': entry.normalized_word,
                'language': entry.language.value,
                'frequency': entry.frequency,
                'gematria_values': entry.gematria_values,
                'verse_numbers': entry.verse_numbers,
                'occurrences': entry.occurrences
            }
            export_data['word_dictionary'].append(word_data)
        
        # Add verse analysis
        if self.current_result.verse_analysis:
            for verse_entry in self.current_result.verse_analysis:
                verse_data = {
                    'verse_number': verse_entry.verse_number,
                    'verse_text': verse_entry.verse_text,
                    'word_count': verse_entry.word_count,
                    'gematria_sums': verse_entry.gematria_sums
                }
                export_data['verse_analysis'].append(verse_data)
        
        # Add global analysis
        if self.current_result.global_analysis:
            global_analysis = self.current_result.global_analysis
            export_data['global_analysis'] = {
                'total_verses': global_analysis.total_verses,
                'triangular_number': global_analysis.triangular_number,
                'total_gematria_sums': global_analysis.total_gematria_sums,
                'global_sums': global_analysis.global_sums
            }
        
        # Write to file
        with open(file_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, indent=2, ensure_ascii=False)
    
    def _export_detailed_report(self, file_path: str):
        """Export detailed text report."""
        from datetime import datetime
        
        with open(file_path, 'w', encoding='utf-8') as txtfile:
            # Header
            txtfile.write("=" * 80 + "\n")
            txtfile.write("GEMATRIA DICTIONARY ANALYSIS REPORT\n")
            txtfile.write("=" * 80 + "\n\n")
            
            # Export info
            txtfile.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write(f"Exported by: IsopGem Gematria Dictionary\n\n")
            
            # Document info
            txtfile.write("DOCUMENT INFORMATION\n")
            txtfile.write("-" * 40 + "\n")
            txtfile.write(f"Title: {self.current_result.document_title}\n")
            txtfile.write(f"Total Words: {self.current_result.total_words:,}\n")
            txtfile.write(f"Unique Words: {self.current_result.unique_words:,}\n")
            txtfile.write(f"Calculation Types: {', '.join(self.current_result.calculation_types_used)}\n\n")
            
            # Global analysis
            if self.current_result.global_analysis:
                global_analysis = self.current_result.global_analysis
                txtfile.write("GLOBAL ANALYSIS SUMMARY\n")
                txtfile.write("-" * 40 + "\n")
                txtfile.write(f"Total Verses: {global_analysis.total_verses}\n")
                txtfile.write(f"Triangular Number: {global_analysis.triangular_number:,}\n\n")
                
                txtfile.write("Gematria Totals by Calculation Type:\n")
                for calc_type, total_sum in global_analysis.total_gematria_sums.items():
                    global_sum = global_analysis.global_sums.get(calc_type, 0)
                    txtfile.write(f"  {calc_type}:\n")
                    txtfile.write(f"    Total Gematria: {total_sum:,}\n")
                    txtfile.write(f"    + Triangular:   {global_analysis.triangular_number:,}\n")
                    txtfile.write(f"    = Global Sum:   {global_sum:,}\n\n")
            
            # Word dictionary
            txtfile.write("WORD DICTIONARY\n")
            txtfile.write("-" * 40 + "\n")
            txtfile.write(f"{'Word':<20} {'Lang':<6} {'Freq':<6} {'Verses':<15}")
            for calc_type in sorted(self.current_result.calculation_types_used):
                txtfile.write(f" {calc_type:<10}")
            txtfile.write("\n")
            txtfile.write("-" * (61 + len(self.current_result.calculation_types_used) * 11) + "\n")
            
            for entry in self.current_result.entries:
                verses_str = ','.join(map(str, sorted(entry.verse_numbers)[:3])) if entry.verse_numbers else ''
                if len(entry.verse_numbers) > 3:
                    verses_str += '...'
                
                txtfile.write(f"{entry.word:<20} {entry.language.value:<6} {entry.frequency:<6} {verses_str:<15}")
                
                for calc_type in sorted(self.current_result.calculation_types_used):
                    value = entry.gematria_values.get(calc_type, '')
                    txtfile.write(f" {str(value):<10}")
                txtfile.write("\n")
            
            # Verse analysis
            if self.current_result.verse_analysis:
                txtfile.write(f"\n\nVERSE ANALYSIS ({len(self.current_result.verse_analysis)} verses)\n")
                txtfile.write("-" * 40 + "\n")
                
                calc_types = list(self.current_result.verse_analysis[0].gematria_sums.keys())
                txtfile.write(f"{'Verse':<6} {'Words':<6}")
                for calc_type in calc_types:
                    txtfile.write(f" {calc_type:<12}")
                txtfile.write(" Text\n")
                txtfile.write("-" * (12 + len(calc_types) * 13 + 50) + "\n")
                
                for verse_entry in self.current_result.verse_analysis:
                    text_preview = verse_entry.verse_text[:40] + "..." if len(verse_entry.verse_text) > 40 else verse_entry.verse_text
                    txtfile.write(f"{verse_entry.verse_number:<6} {verse_entry.word_count:<6}")
                    
                    for calc_type in calc_types:
                        value = verse_entry.gematria_sums.get(calc_type, 0)
                        txtfile.write(f" {value:<12}")
                    
                    txtfile.write(f" {text_preview}\n")
            
            txtfile.write(f"\n\n" + "=" * 80 + "\n")
            txtfile.write("End of Report\n")
            txtfile.write("=" * 80 + "\n")
    
    def load_document_text(self, text: str, title: str = ""):
        """
        Load document text into the dialog.
        
        Args:
            text: The document text
            title: Optional document title
        """
        self.text_input.setPlainText(text)
        self.current_text = text
        if title:
            self.title_input.setText(title)
    
    def get_current_result(self) -> Optional[GematriaDictionaryResult]:
        """Get the current analysis result."""
        return self.current_result
    
    def _populate_verse_data(self, calc_types: List[str], has_chapters: bool):
        """Populate the table with verse analysis data."""
        if has_chapters:
            # Group verses by chapter for organized display
            verses_by_chapter = {}
            for verse in self.current_result.verse_analysis:
                chapter_num = verse.chapter_number or 0  # Use 0 for verses without chapter
                if chapter_num not in verses_by_chapter:
                    verses_by_chapter[chapter_num] = []
                verses_by_chapter[chapter_num].append(verse)
            
            # Add chapter summaries and their verses
            for chapter_summary in sorted(self.current_result.chapter_summaries, key=lambda x: x.chapter_number):
                chapter_num = chapter_summary.chapter_number
                
                # Add chapter summary row
                chapter_row_items = []
                chapter_row_items.append(QStandardItem(f"📖 Chapter {chapter_num}"))
                chapter_row_items.append(QStandardItem(f"({chapter_summary.verse_range})"))
                chapter_row_items.append(QStandardItem(f"{chapter_summary.total_words} words"))
                chapter_row_items.append(QStandardItem(""))  # Numbers column for chapter
                chapter_row_items.append(QStandardItem(f"{chapter_summary.verse_count} verses"))
                
                # Add chapter gematria totals
                for calc_type in calc_types:
                    total = chapter_summary.gematria_totals.get(calc_type, 0)
                    global_sum = chapter_summary.global_sums.get(calc_type, 0)
                    chapter_row_items.append(QStandardItem(f"Σ{total} (⊕{global_sum})"))
                
                # Add chapter totals for Numbers Total and Verse Total
                chapter_row_items.append(QStandardItem(""))  # Numbers Total for chapter
                chapter_row_items.append(QStandardItem(""))  # Verse Total for chapter
                
                # Style chapter row
                for item in chapter_row_items:
                    item.setBackground(QStandardItem().background())  # Light background
                    font = item.font()
                    font.setBold(True)
                    item.setFont(font)
                
                self.verse_table_model.appendRow(chapter_row_items)
                
                # Add verses for this chapter
                chapter_verses = verses_by_chapter.get(chapter_num, [])
                for verse_entry in sorted(chapter_verses, key=lambda x: x.verse_number):
                    verse_row_items = []
                    
                    # Chapter column (empty for verse rows)
                    verse_row_items.append(QStandardItem(""))
                    
                    # Verse number
                    verse_row_items.append(QStandardItem(str(verse_entry.verse_number)))
                    
                    # Word count
                    verse_row_items.append(QStandardItem(str(verse_entry.word_count)))
                    
                    # Number count
                    verse_row_items.append(QStandardItem(str(verse_entry.number_count)))
                    
                    # Verse text (truncated for display)
                    text_preview = verse_entry.verse_text[:100] + "..." if len(verse_entry.verse_text) > 100 else verse_entry.verse_text
                    verse_row_items.append(QStandardItem(text_preview))
                    
                    # Gematria values (words only)
                    for calc_type in calc_types:
                        value = verse_entry.gematria_sums.get(calc_type, 0)
                        verse_row_items.append(QStandardItem(str(value)))
                    
                    # Numbers Total
                    verse_row_items.append(QStandardItem(str(verse_entry.numbers_total)))
                    
                    # Verse Total (sum of ALL calculation types + numbers)
                    if calc_types and verse_entry.gematria_sums:
                        # Sum all gematria values from all calculation types
                        total_gematria_all_types = sum(
                            verse_entry.gematria_sums.get(calc_type, 0) 
                            for calc_type in calc_types
                        )
                        verse_total = total_gematria_all_types + verse_entry.numbers_total
                        verse_row_items.append(QStandardItem(str(verse_total)))
                    else:
                        verse_row_items.append(QStandardItem(str(verse_entry.numbers_total)))
                    
                    self.verse_table_model.appendRow(verse_row_items)
        else:
            # No chapters - display verses without chapter column
            for verse_entry in self.current_result.verse_analysis:
                row_items = []
                
                # No chapter column when chapters don't exist
                # Start directly with verse number
                row_items.append(QStandardItem(str(verse_entry.verse_number)))
                
                # Word count
                row_items.append(QStandardItem(str(verse_entry.word_count)))
                
                # Number count
                row_items.append(QStandardItem(str(verse_entry.number_count)))
                
                # Verse text (truncated for display)
                text_preview = verse_entry.verse_text[:100] + "..." if len(verse_entry.verse_text) > 100 else verse_entry.verse_text
                row_items.append(QStandardItem(text_preview))
                
                # Gematria values (words only)
                for calc_type in calc_types:
                    value = verse_entry.gematria_sums.get(calc_type, 0)
                    row_items.append(QStandardItem(str(value)))
                
                # Numbers Total
                row_items.append(QStandardItem(str(verse_entry.numbers_total)))
                
                # Verse Total (sum of ALL calculation types + numbers)
                if calc_types and verse_entry.gematria_sums:
                    # Sum all gematria values from all calculation types
                    total_gematria_all_types = sum(
                        verse_entry.gematria_sums.get(calc_type, 0) 
                        for calc_type in calc_types
                    )
                    verse_total = total_gematria_all_types + verse_entry.numbers_total
                    row_items.append(QStandardItem(str(verse_total)))
                else:
                    row_items.append(QStandardItem(str(verse_entry.numbers_total)))
                
                self.verse_table_model.appendRow(row_items)
        
        # Resize columns
        self.verse_table_view.resizeColumnsToContents()
    
    def _populate_line_data(self, calc_types: List[str]):
        """Populate the table with line analysis data."""
        has_leading_numbers = any(line.has_leading_number for line in self.current_result.line_analysis)
        
        for line_entry in self.current_result.line_analysis:
            row_items = []
            
            # Line number
            row_items.append(QStandardItem(str(line_entry.line_number)))
            
            # Leading number (if column exists)
            if has_leading_numbers:
                if line_entry.has_leading_number and line_entry.leading_number is not None:
                    leading_item = QStandardItem(str(line_entry.leading_number))
                    # Style likely verses differently
                    if line_entry.is_likely_verse:
                        font = leading_item.font()
                        font.setBold(True)
                        leading_item.setFont(font)
                        leading_item.setToolTip("Likely verse number")
                    row_items.append(leading_item)
                else:
                    row_items.append(QStandardItem(""))
            
            # Word count
            row_items.append(QStandardItem(str(line_entry.word_count)))
            
            # Number count
            row_items.append(QStandardItem(str(line_entry.number_count)))
            
            # Line text (truncated for display)
            text_preview = line_entry.line_text[:100] + "..." if len(line_entry.line_text) > 100 else line_entry.line_text
            row_items.append(QStandardItem(text_preview))
            
            # Gematria values (words only)
            for calc_type in calc_types:
                value = line_entry.gematria_sums.get(calc_type, 0)
                row_items.append(QStandardItem(str(value)))
            
            # Numbers Total
            row_items.append(QStandardItem(str(line_entry.numbers_total)))
            
            # Line Total (sum of ALL calculation types + numbers)
            if calc_types and line_entry.gematria_sums:
                # Sum all gematria values from all calculation types
                total_gematria_all_types = sum(
                    line_entry.gematria_sums.get(calc_type, 0) 
                    for calc_type in calc_types
                )
                line_total = total_gematria_all_types + line_entry.numbers_total
                row_items.append(QStandardItem(str(line_total)))
            else:
                row_items.append(QStandardItem(str(line_entry.numbers_total)))
            
            self.verse_table_model.appendRow(row_items)
        
        # Resize columns
        self.verse_table_view.resizeColumnsToContents() 