"""
Verse Breakdown Dialog.

This module provides a dialog for displaying detailed breakdowns of individual verses,
showing each word and its gematria values.

Author: Assistant
Created: 2024-12-28
Last Modified: 2024-12-28
Dependencies: PyQt6, typing, loguru
"""

from typing import Optional
from loguru import logger

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QStandardItemModel, QStandardItem
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableView, QGroupBox, QTextEdit, QHeaderView, QMessageBox
)

from document_manager.models.gematria_dictionary_entry import VerseBreakdown


class VerseBreakdownDialog(QDialog):
    """Dialog for displaying detailed verse breakdown."""
    
    def __init__(self, verse_breakdown: VerseBreakdown, parent=None):
        """
        Initialize the dialog.
        
        Args:
            verse_breakdown: The verse breakdown data to display
            parent: Parent widget
        """
        super().__init__(parent)
        self.verse_breakdown = verse_breakdown
        
        self.setWindowTitle(f"Verse {verse_breakdown.verse_number} Breakdown")
        self.setModal(False)  # Non-modal as requested
        self.resize(900, 600)
        
        # Models
        self.table_model = QStandardItemModel()
        
        # UI setup
        self._init_ui()
        self._populate_data()
        
        logger.debug(f"VerseBreakdownDialog initialized for verse {verse_breakdown.verse_number}")
    
    def _init_ui(self):
        """Initialize the user interface."""
        layout = QVBoxLayout(self)
        
        # Header section
        header_group = QGroupBox(f"Verse {self.verse_breakdown.verse_number}")
        header_layout = QVBoxLayout(header_group)
        
        # Verse text
        self.verse_text = QTextEdit()
        self.verse_text.setPlainText(self.verse_breakdown.verse_text)
        self.verse_text.setReadOnly(True)
        self.verse_text.setMaximumHeight(100)
        self.verse_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        header_layout.addWidget(self.verse_text)
        
        layout.addWidget(header_group)
        
        # Word breakdown section
        breakdown_group = QGroupBox("Word-by-Word Breakdown")
        breakdown_layout = QVBoxLayout(breakdown_group)
        
        # Statistics
        word_count = len(self.verse_breakdown.words)
        calc_types = ", ".join(self.verse_breakdown.calculation_types_used)
        
        self.stats_label = QLabel(
            f"📊 {word_count} words/numbers analyzed | Calculation types: {calc_types}"
        )
        self.stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        breakdown_layout.addWidget(self.stats_label)
        
        # Table
        self.table_view = QTableView()
        self.table_view.setModel(self.table_model)
        self.table_view.setSortingEnabled(False)  # Keep original order
        self.table_view.setAlternatingRowColors(True)
        self.table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        
        # Set table styling
        self.table_view.setStyleSheet("""
            QTableView {
                gridline-color: #dee2e6;
                selection-background-color: #e3f2fd;
            }
            QTableView::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)
        
        breakdown_layout.addWidget(self.table_view)
        
        layout.addWidget(breakdown_group)
        
        # Totals section
        totals_group = QGroupBox("Verse Totals")
        totals_layout = QVBoxLayout(totals_group)
        
        self.totals_text = QTextEdit()
        self.totals_text.setReadOnly(True)
        self.totals_text.setMaximumHeight(120)
        self.totals_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
                font-size: 11pt;
            }
        """)
        totals_layout.addWidget(self.totals_text)
        
        layout.addWidget(totals_group)
        
        # Button layout
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.close_btn = QPushButton("Close")
        self.close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def _populate_data(self):
        """Populate the table with word breakdown data."""
        # Setup headers
        headers = ["#", "Word/Number", "Type", "Normalized"]
        
        # Add gematria value columns
        for calc_type in self.verse_breakdown.calculation_types_used:
            headers.append(calc_type)
        
        self.table_model.setHorizontalHeaderLabels(headers)
        
        # Populate data
        for word_entry in self.verse_breakdown.words:
            row_items = []
            
            # Position (1-based for display)
            position_item = QStandardItem(str(word_entry.position_in_verse + 1))
            position_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            row_items.append(position_item)
            
            # Word/Number
            word_item = QStandardItem(word_entry.word)
            if word_entry.is_number:
                word_item.setFont(QFont("Courier New", 10, QFont.Weight.Bold))
                word_item.setBackground(Qt.GlobalColor.lightGray)
            row_items.append(word_item)
            
            # Type
            type_text = "Number" if word_entry.is_number else "Word"
            type_item = QStandardItem(type_text)
            type_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            if word_entry.is_number:
                type_item.setBackground(Qt.GlobalColor.lightGray)
            row_items.append(type_item)
            
            # Normalized
            normalized_item = QStandardItem(word_entry.normalized_word)
            if word_entry.is_number:
                normalized_item.setBackground(Qt.GlobalColor.lightGray)
            row_items.append(normalized_item)
            
            # Gematria values
            for calc_type in self.verse_breakdown.calculation_types_used:
                value = word_entry.gematria_values.get(calc_type, 0)
                value_item = QStandardItem(str(value))
                value_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                
                # Highlight numbers with different styling
                if word_entry.is_number:
                    value_item.setFont(QFont("Courier New", 10, QFont.Weight.Bold))
                    value_item.setBackground(Qt.GlobalColor.lightGray)
                
                row_items.append(value_item)
            
            self.table_model.appendRow(row_items)
        
        # Resize columns to content
        self.table_view.resizeColumnsToContents()
        
        # Set minimum column widths
        header = self.table_view.horizontalHeader()
        for i in range(self.table_model.columnCount()):
            header.setMinimumSectionSize(60)
        
        # Update totals display
        self._update_totals_display()
    
    def _update_totals_display(self):
        """Update the totals display."""
        totals_lines = [
            f"🔢 VERSE {self.verse_breakdown.verse_number} TOTALS",
            f"{'=' * 40}",
            f""
        ]
        
        # Add totals for each calculation type
        for calc_type in self.verse_breakdown.calculation_types_used:
            total = self.verse_breakdown.total_gematria_sums.get(calc_type, 0)
            totals_lines.append(f"📊 {calc_type}: {total:,}")
        
        # Add breakdown by type
        word_total = sum(
            word.gematria_values.get(self.verse_breakdown.calculation_types_used[0], 0)
            for word in self.verse_breakdown.words
            if not word.is_number
        )
        number_total = sum(
            word.number_value or 0
            for word in self.verse_breakdown.words
            if word.is_number
        )
        
        if self.verse_breakdown.calculation_types_used:
            calc_type = self.verse_breakdown.calculation_types_used[0]
            totals_lines.extend([
                f"",
                f"📝 Breakdown for {calc_type}:",
                f"   • Words: {word_total:,}",
                f"   • Numbers: {number_total:,}",
                f"   • Total: {word_total + number_total:,}"
            ])
        
        totals_text = "\n".join(totals_lines)
        self.totals_text.setPlainText(totals_text)


def show_verse_breakdown(verse_breakdown: VerseBreakdown, parent=None) -> VerseBreakdownDialog:
    """
    Show a verse breakdown dialog.
    
    Args:
        verse_breakdown: The verse breakdown data
        parent: Parent widget
        
    Returns:
        The created dialog
    """
    dialog = VerseBreakdownDialog(verse_breakdown, parent)
    dialog.show()
    return dialog 