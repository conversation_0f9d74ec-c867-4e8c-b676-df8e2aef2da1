"""
Gematria Dictionary Panel.

This module provides a panel wrapper for the Gematria Dictionary dialog
to integrate with the window management system.

Author: Assistant
Created: 2024-12-28
Last Modified: 2024-12-28
Dependencies: PyQt6, loguru
"""

from loguru import logger
from PyQt6.QtWidgets import QWidget, QVBoxLayout

from document_manager.ui.dialogs.gematria_dictionary_dialog import GematriaDictionaryDialog


class GematriaDictionaryPanel(QWidget):
    """Panel wrapper for the Gematria Dictionary dialog."""
    
    def __init__(self, parent=None):
        """Initialize the panel."""
        super().__init__(parent)
        self.setWindowTitle("Gematria Dictionary")
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create and embed the dialog
        self.dialog = GematriaDictionaryDialog(self)
        
        # Remove dialog window decorations since it's embedded
        self.dialog.setWindowFlags(self.dialog.windowFlags() & ~self.dialog.windowFlags())
        
        # Add dialog to layout
        layout.addWidget(self.dialog)
        
        logger.debug("GematriaDictionaryPanel initialized")
    
    def load_document_text(self, text: str, title: str = ""):
        """
        Load document text into the dictionary.
        
        Args:
            text: The document text
            title: Optional document title
        """
        self.dialog.load_document_text(text, title)
    
    def get_current_result(self):
        """Get the current analysis result."""
        return self.dialog.get_current_result() 