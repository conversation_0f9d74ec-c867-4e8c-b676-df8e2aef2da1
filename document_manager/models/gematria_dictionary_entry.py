"""
Gematria Dictionary Entry model.

This module defines the data structure for entries in the Gematria Dictionary,
which stores words from documents along with their calculated gematria values.

Author: Assistant
Created: 2024-12-28
Last Modified: 2024-12-28
Dependencies: pydantic, typing
"""

from typing import Dict, List, Optional
from pydantic import BaseModel, Field
from enum import Enum

from gematria.models.calculation_type import CalculationType, Language


class GematriaDictionaryEntry(BaseModel):
    """Model representing a word entry in the Gematria Dictionary."""
    
    word: str = Field(..., description="The original word from the document")
    normalized_word: str = Field(..., description="Normalized version of the word (cleaned)")
    language: Language = Field(..., description="Detected language of the word")
    frequency: int = Field(..., description="Number of times this word appears")
    gematria_values: Dict[str, int] = Field(default_factory=dict, description="Gematria values by calculation type")
    verse_numbers: List[int] = Field(default_factory=list, description="Verse numbers where this word appears")
    occurrences: List[str] = Field(default_factory=list, description="All occurrences of this word (showing variations)")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class VerseWordEntry(BaseModel):
    """Model representing a single word within a verse breakdown."""
    
    word: str = Field(..., description="The original word")
    normalized_word: str = Field(..., description="Normalized version for calculation")
    is_number: bool = Field(default=False, description="Whether this is a pure number")
    number_value: Optional[int] = Field(None, description="Face value if it's a number")
    gematria_values: Dict[str, int] = Field(default_factory=dict, description="Gematria values by calculation type")
    position_in_verse: int = Field(..., description="Position of this word in the verse (0-based)")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class VerseBreakdown(BaseModel):
    """Model representing the detailed breakdown of a single verse."""
    
    verse_number: int = Field(..., description="The verse number")
    verse_text: str = Field(..., description="The full text of the verse")
    words: List[VerseWordEntry] = Field(default_factory=list, description="Individual word entries")
    total_gematria_sums: Dict[str, int] = Field(default_factory=dict, description="Total gematria values by calculation type")
    calculation_types_used: List[str] = Field(default_factory=list, description="List of calculation types applied")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class VerseAnalysisEntry(BaseModel):
    """Model representing the gematria analysis of a single verse."""
    
    verse_number: int = Field(..., description="The verse number")
    verse_text: str = Field(..., description="The full text of the verse")
    word_count: int = Field(..., description="Number of words in this verse")
    number_count: int = Field(default=0, description="Number of standalone numbers in this verse")
    gematria_sums: Dict[str, int] = Field(default_factory=dict, description="Word gematria values by calculation type")
    numbers_total: int = Field(default=0, description="Total face value of all numbers in this verse")
    verse_totals: Dict[str, int] = Field(default_factory=dict, description="Total verse values (gematria + numbers) by calculation type")
    chapter_number: Optional[int] = Field(None, description="Chapter number this verse belongs to")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class GlobalAnalysisSummary(BaseModel):
    """Model representing the global analysis summary for the entire document."""
    
    total_verses: int = Field(..., description="Total number of verses in the document")
    triangular_number: int = Field(..., description="Triangular number of total verses (1+2+...+n)")
    total_gematria_sums: Dict[str, int] = Field(default_factory=dict, description="Sum of all verse gematria values by calculation type")
    global_sums: Dict[str, int] = Field(default_factory=dict, description="Global sums (total gematria + triangular number) by calculation type")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class ChapterSummary(BaseModel):
    """Model representing the summary of a chapter."""
    
    chapter_number: int = Field(..., description="The chapter number")
    chapter_title: Optional[str] = Field(None, description="Chapter title or heading")
    verse_count: int = Field(..., description="Number of verses in this chapter")
    verse_range: str = Field(..., description="Range of verses (e.g., '1-66')")
    total_words: int = Field(..., description="Total words in this chapter")
    gematria_totals: Dict[str, int] = Field(default_factory=dict, description="Total gematria values for the chapter by calculation type")
    triangular_number: int = Field(..., description="Triangular number for this chapter's verse count")
    global_sums: Dict[str, int] = Field(default_factory=dict, description="Chapter global sums (gematria + triangular)")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class TextStructureType(Enum):
    """Enumeration of different text structure types."""
    VERSE_NUMBERED = "verse_numbered"  # Traditional verse numbering (1. text, 2. text)
    LINE_NUMBERED = "line_numbered"    # Lines with numbers that aren't verses
    PARAGRAPH_BASED = "paragraph_based"  # Paragraphs without numbering
    CONTINUOUS_TEXT = "continuous_text"  # Continuous text without clear divisions
    MIXED_STRUCTURE = "mixed_structure"  # Mixed or unclear structure


class LineAnalysisEntry(BaseModel):
    """Model representing the gematria analysis of a single line."""
    
    line_number: int = Field(..., description="The line number (1-based)")
    line_text: str = Field(..., description="The full text of the line")
    word_count: int = Field(..., description="Number of words in this line")
    number_count: int = Field(default=0, description="Number of standalone numbers in this line")
    gematria_sums: Dict[str, int] = Field(default_factory=dict, description="Word gematria values by calculation type")
    numbers_total: int = Field(default=0, description="Total face value of all numbers in this line")
    line_totals: Dict[str, int] = Field(default_factory=dict, description="Total line values (gematria + numbers) by calculation type")
    has_leading_number: bool = Field(default=False, description="Whether this line starts with a number")
    leading_number: Optional[int] = Field(None, description="The leading number if present")
    is_likely_verse: bool = Field(default=False, description="Whether this line is likely a verse")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class TextStructureAnalysis(BaseModel):
    """Model representing the analysis of text structure."""
    
    structure_type: TextStructureType = Field(..., description="Detected structure type")
    confidence: float = Field(..., description="Confidence in structure detection (0.0-1.0)")
    total_lines: int = Field(..., description="Total number of non-empty lines")
    numbered_lines: int = Field(default=0, description="Number of lines with leading numbers")
    sequential_numbering: bool = Field(default=False, description="Whether numbering is sequential")
    verse_like_pattern: bool = Field(default=False, description="Whether pattern suggests verses")
    chapter_divisions: bool = Field(default=False, description="Whether chapter divisions are present")
    analysis_notes: List[str] = Field(default_factory=list, description="Notes about the structure analysis")
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True


class GematriaDictionaryResult(BaseModel):
    """Model representing the complete dictionary result for a document."""
    
    document_title: str = Field(..., description="Title or name of the analyzed document")
    total_words: int = Field(..., description="Total number of words processed")
    unique_words: int = Field(..., description="Number of unique words found")
    entries: List[GematriaDictionaryEntry] = Field(
        default_factory=list,
        description="List of dictionary entries"
    )
    calculation_types_used: List[str] = Field(
        default_factory=list,
        description="List of calculation types applied"
    )
    verse_analysis: List[VerseAnalysisEntry] = Field(
        default_factory=list,
        description="Analysis of individual verses"
    )
    line_analysis: List[LineAnalysisEntry] = Field(
        default_factory=list,
        description="Line-by-line analysis for non-verse texts"
    )
    text_structure: Optional[TextStructureAnalysis] = Field(
        None,
        description="Analysis of the document's text structure"
    )
    chapter_summaries: List[ChapterSummary] = Field(
        default_factory=list,
        description="Summary of chapters in the document"
    )
    global_analysis: Optional[GlobalAnalysisSummary] = Field(
        None,
        description="Global analysis summary"
    )
    
    class Config:
        """Pydantic configuration."""
        arbitrary_types_allowed = True 