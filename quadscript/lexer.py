"""
@file quadscript/lexer.py
@description Lexer for QuadScript domain-specific language
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies re, enum
"""

import re
from enum import Enum, auto
from dataclasses import dataclass
from typing import List, Optional, Iterator


class TokenType(Enum):
    """Token types for QuadScript language."""
    # Literals
    NUMBER = auto()
    STRING = auto()
    BOOLEAN = auto()
    
    # Identifiers
    IDENTIFIER = auto()
    
    # Keywords
    FIND = auto()
    WHERE = auto()
    AND = auto()
    OR = auto()
    NOT = auto()
    IF = auto()
    THEN = auto()
    ELSE = auto()
    END = auto()
    FOR = auto()
    IN = auto()
    DO = auto()
    WHILE = auto()
    FUNCTION = auto()
    PROCEDURE = auto()
    RETURNS = auto()
    CALL = auto()
    PRINT = auto()
    RETURN = auto()
    
    # Data operations
    QUADSETS = auto()
    SORT = auto()
    BY = auto()
    LIMIT = auto()
    TO = auto()
    GROUP = auto()
    STATS = auto()
    COUNT = auto()
    AVG = auto()
    MAX = auto()
    MIN = auto()
    SUM = auto()
    
    # Database operations
    SAVE = auto()
    LOAD = auto()
    FROM = auto()
    AS = auto()
    UPDATE = auto()
    SET = auto()
    IMPORT = auto()
    EXPORT = auto()
    FORMAT = auto()
    
    # Visualization
    CHART = auto()
    HISTOGRAM = auto()
    SCATTER = auto()
    BAR = auto()
    OF = auto()
    
    # Mathematical functions
    TERNARY_DIGITS = auto()
    IS_PRIME = auto()
    IS_PERFECT_SQUARE = auto()
    ALL_PRIME = auto()
    UNIFORM_TERNARY_DIGITS = auto()
    DIVISIBLE_BY = auto()
    
    # Operators
    EQUALS = auto()
    NOT_EQUALS = auto()
    LESS_THAN = auto()
    GREATER_THAN = auto()
    LESS_EQUAL = auto()
    GREATER_EQUAL = auto()
    PLUS = auto()
    MINUS = auto()
    MULTIPLY = auto()
    DIVIDE = auto()
    MODULO = auto()
    
    # Delimiters
    LEFT_PAREN = auto()
    RIGHT_PAREN = auto()
    LEFT_BRACKET = auto()
    RIGHT_BRACKET = auto()
    LEFT_BRACE = auto()
    RIGHT_BRACE = auto()
    COMMA = auto()
    DOT = auto()
    COLON = auto()
    SEMICOLON = auto()
    RANGE = auto()  # ..
    
    # Special
    NEWLINE = auto()
    EOF = auto()
    COMMENT = auto()


@dataclass
class Token:
    """Represents a token in QuadScript."""
    type: TokenType
    value: str
    line: int
    column: int


class QuadScriptLexer:
    """Lexer for QuadScript domain-specific language."""
    
    # Keywords mapping
    KEYWORDS = {
        'FIND': TokenType.FIND,
        'WHERE': TokenType.WHERE,
        'AND': TokenType.AND,
        'OR': TokenType.OR,
        'NOT': TokenType.NOT,
        'IF': TokenType.IF,
        'THEN': TokenType.THEN,
        'ELSE': TokenType.ELSE,
        'END': TokenType.END,
        'FOR': TokenType.FOR,
        'IN': TokenType.IN,
        'DO': TokenType.DO,
        'WHILE': TokenType.WHILE,
        'FUNCTION': TokenType.FUNCTION,
        'PROCEDURE': TokenType.PROCEDURE,
        'RETURNS': TokenType.RETURNS,
        'CALL': TokenType.CALL,
        'PRINT': TokenType.PRINT,
        'RETURN': TokenType.RETURN,
        'quadsets': TokenType.QUADSETS,
        'SORT': TokenType.SORT,
        'BY': TokenType.BY,
        'LIMIT': TokenType.LIMIT,
        'TO': TokenType.TO,
        'GROUP': TokenType.GROUP,
        'STATS': TokenType.STATS,
        'COUNT': TokenType.COUNT,
        'AVG': TokenType.AVG,
        'MAX': TokenType.MAX,
        'MIN': TokenType.MIN,
        'SUM': TokenType.SUM,
        'SAVE': TokenType.SAVE,
        'LOAD': TokenType.LOAD,
        'FROM': TokenType.FROM,
        'AS': TokenType.AS,
        'UPDATE': TokenType.UPDATE,
        'SET': TokenType.SET,
        'IMPORT': TokenType.IMPORT,
        'EXPORT': TokenType.EXPORT,
        'FORMAT': TokenType.FORMAT,
        'CHART': TokenType.CHART,
        'histogram': TokenType.HISTOGRAM,
        'scatter': TokenType.SCATTER,
        'bar': TokenType.BAR,
        'OF': TokenType.OF,
        'ternary_digits': TokenType.TERNARY_DIGITS,
        'is_prime': TokenType.IS_PRIME,
        'is_perfect_square': TokenType.IS_PERFECT_SQUARE,
        'all_prime': TokenType.ALL_PRIME,
        'uniform_ternary_digits': TokenType.UNIFORM_TERNARY_DIGITS,
        'divisible_by': TokenType.DIVISIBLE_BY,
        'true': TokenType.BOOLEAN,
        'false': TokenType.BOOLEAN,
    }
    
    # Token patterns
    TOKEN_PATTERNS = [
        # Comments
        (r'#.*', TokenType.COMMENT),
        
        # Numbers (integers and floats)
        (r'\d+\.\d+', TokenType.NUMBER),
        (r'\d+', TokenType.NUMBER),
        
        # Strings
        (r'"[^"]*"', TokenType.STRING),
        (r"'[^']*'", TokenType.STRING),
        
        # Range operator
        (r'\.\.', TokenType.RANGE),
        
        # Comparison operators
        (r'>=', TokenType.GREATER_EQUAL),
        (r'<=', TokenType.LESS_EQUAL),
        (r'!=', TokenType.NOT_EQUALS),
        (r'==', TokenType.EQUALS),
        (r'=', TokenType.EQUALS),
        (r'>', TokenType.GREATER_THAN),
        (r'<', TokenType.LESS_THAN),
        
        # Arithmetic operators
        (r'\+', TokenType.PLUS),
        (r'-', TokenType.MINUS),
        (r'\*', TokenType.MULTIPLY),
        (r'/', TokenType.DIVIDE),
        (r'%', TokenType.MODULO),
        
        # Delimiters
        (r'\(', TokenType.LEFT_PAREN),
        (r'\)', TokenType.RIGHT_PAREN),
        (r'\[', TokenType.LEFT_BRACKET),
        (r'\]', TokenType.RIGHT_BRACKET),
        (r'\{', TokenType.LEFT_BRACE),
        (r'\}', TokenType.RIGHT_BRACE),
        (r',', TokenType.COMMA),
        (r'\.', TokenType.DOT),
        (r':', TokenType.COLON),
        (r';', TokenType.SEMICOLON),
        
        # Identifiers (must come after keywords)
        (r'[a-zA-Z_][a-zA-Z0-9_]*', TokenType.IDENTIFIER),
        
        # Newlines
        (r'\n', TokenType.NEWLINE),
        
        # Whitespace (ignored)
        (r'[ \t]+', None),
    ]
    
    def __init__(self, text: str):
        """Initialize lexer with source text."""
        self.text = text
        self.position = 0
        self.line = 1
        self.column = 1
        self.tokens: List[Token] = []
    
    def tokenize(self) -> List[Token]:
        """Tokenize the entire input text."""
        while self.position < len(self.text):
            self._next_token()
        
        # Add EOF token
        self.tokens.append(Token(TokenType.EOF, '', self.line, self.column))
        return self.tokens
    
    def _next_token(self) -> Optional[Token]:
        """Get the next token from the input."""
        if self.position >= len(self.text):
            return None
        
        # Try to match each pattern
        for pattern, token_type in self.TOKEN_PATTERNS:
            regex = re.compile(pattern)
            match = regex.match(self.text, self.position)
            
            if match:
                value = match.group(0)
                
                # Skip whitespace
                if token_type is None:
                    self._advance(len(value))
                    return self._next_token()
                
                # Skip comments
                if token_type == TokenType.COMMENT:
                    self._advance(len(value))
                    return self._next_token()
                
                # Create token
                token = Token(token_type, value, self.line, self.column)
                
                # Check if identifier is actually a keyword
                if token_type == TokenType.IDENTIFIER:
                    keyword_type = self.KEYWORDS.get(value.upper())
                    if keyword_type:
                        token.type = keyword_type
                    elif value.lower() in ['true', 'false']:
                        token.type = TokenType.BOOLEAN
                
                self.tokens.append(token)
                self._advance(len(value))
                return token
        
        # No pattern matched - error
        char = self.text[self.position]
        raise SyntaxError(f"Unexpected character '{char}' at line {self.line}, column {self.column}")
    
    def _advance(self, count: int):
        """Advance position and update line/column tracking."""
        for _ in range(count):
            if self.position < len(self.text):
                if self.text[self.position] == '\n':
                    self.line += 1
                    self.column = 1
                else:
                    self.column += 1
                self.position += 1


# Example usage and testing
if __name__ == "__main__":
    # Test QuadScript code
    test_code = """
    # Find large quadsets
    large_quads = FIND quadsets WHERE sum > 1000000
    
    # Analyze ternary patterns
    uniform_quads = FIND quadsets WHERE uniform_ternary_digits = true
    
    # Mathematical analysis
    IF sum(large_quads) > 5000000 THEN
        PRINT "Found very large quadsets!"
    END
    
    # Export results
    EXPORT large_quads TO "results.csv" FORMAT csv
    """
    
    lexer = QuadScriptLexer(test_code)
    tokens = lexer.tokenize()
    
    print("🔤 QuadScript Lexer Test Results:")
    print("=" * 50)
    
    for token in tokens:
        if token.type != TokenType.NEWLINE:  # Skip newlines for cleaner output
            print(f"{token.type.name:20} | {token.value:15} | Line {token.line:2}, Col {token.column:2}")
    
    print(f"\n✅ Successfully tokenized {len(tokens)} tokens!") 