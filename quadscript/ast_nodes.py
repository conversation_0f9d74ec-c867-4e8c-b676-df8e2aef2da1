"""
@file quadscript/ast_nodes.py
@description Abstract Syntax Tree nodes for QuadScript language
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies abc, typing
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Any, Union
from dataclasses import dataclass


class ASTNode(ABC):
    """Base class for all AST nodes."""
    
    @abstractmethod
    def accept(self, visitor):
        """Accept a visitor for the visitor pattern."""
        pass


# Expressions
class Expression(ASTNode):
    """Base class for all expressions."""
    pass


@dataclass
class LiteralExpression(Expression):
    """Literal values (numbers, strings, booleans)."""
    value: Any
    
    def accept(self, visitor):
        return visitor.visit_literal(self)


@dataclass
class IdentifierExpression(Expression):
    """Variable or function identifiers."""
    name: str
    
    def accept(self, visitor):
        return visitor.visit_identifier(self)


@dataclass
class BinaryExpression(Expression):
    """Binary operations (arithmetic, comparison, logical)."""
    left: Expression
    operator: str
    right: Expression
    
    def accept(self, visitor):
        return visitor.visit_binary(self)


@dataclass
class UnaryExpression(Expression):
    """Unary operations (NOT, -)."""
    operator: str
    operand: Expression
    
    def accept(self, visitor):
        return visitor.visit_unary(self)


@dataclass
class FunctionCallExpression(Expression):
    """Function calls."""
    name: str
    arguments: List[Expression]
    
    def accept(self, visitor):
        return visitor.visit_function_call(self)


@dataclass
class ArrayExpression(Expression):
    """Array literals [1, 2, 3, 4]."""
    elements: List[Expression]
    
    def accept(self, visitor):
        return visitor.visit_array(self)


@dataclass
class RangeExpression(Expression):
    """Range expressions (1..100)."""
    start: Expression
    end: Expression
    
    def accept(self, visitor):
        return visitor.visit_range(self)


@dataclass
class PropertyAccessExpression(Expression):
    """Property access (quadset.sum, number.ternary_digits)."""
    object: Expression
    property: str
    
    def accept(self, visitor):
        return visitor.visit_property_access(self)


# Statements
class Statement(ASTNode):
    """Base class for all statements."""
    pass


@dataclass
class AssignmentStatement(Statement):
    """Variable assignment."""
    name: str
    value: Expression
    
    def accept(self, visitor):
        return visitor.visit_assignment(self)


@dataclass
class PrintStatement(Statement):
    """Print statement."""
    expression: Expression
    
    def accept(self, visitor):
        return visitor.visit_print(self)


@dataclass
class IfStatement(Statement):
    """If-then-else statement."""
    condition: Expression
    then_branch: List[Statement]
    else_branch: Optional[List[Statement]] = None
    
    def accept(self, visitor):
        return visitor.visit_if(self)


@dataclass
class ForStatement(Statement):
    """For loop statement."""
    variable: str
    iterable: Expression
    body: List[Statement]
    
    def accept(self, visitor):
        return visitor.visit_for(self)


@dataclass
class WhileStatement(Statement):
    """While loop statement."""
    condition: Expression
    body: List[Statement]
    
    def accept(self, visitor):
        return visitor.visit_while(self)


@dataclass
class ReturnStatement(Statement):
    """Return statement."""
    value: Optional[Expression] = None
    
    def accept(self, visitor):
        return visitor.visit_return(self)


@dataclass
class ExpressionStatement(Statement):
    """Expression used as statement."""
    expression: Expression
    
    def accept(self, visitor):
        return visitor.visit_expression_statement(self)


# QuadScript-specific statements
@dataclass
class FindStatement(Statement):
    """FIND quadsets WHERE condition."""
    target: str  # Usually "quadsets"
    condition: Expression
    
    def accept(self, visitor):
        return visitor.visit_find(self)


@dataclass
class SortStatement(Statement):
    """SORT collection BY field."""
    collection: Expression
    field: str
    descending: bool = False
    
    def accept(self, visitor):
        return visitor.visit_sort(self)


@dataclass
class LimitStatement(Statement):
    """LIMIT collection TO count."""
    collection: Expression
    count: Expression
    
    def accept(self, visitor):
        return visitor.visit_limit(self)


@dataclass
class GroupStatement(Statement):
    """GROUP collection BY field."""
    collection: Expression
    field: str
    
    def accept(self, visitor):
        return visitor.visit_group(self)


@dataclass
class StatsStatement(Statement):
    """STATS collection { ... }."""
    collection: Expression
    fields: dict  # field_name -> expression
    
    def accept(self, visitor):
        return visitor.visit_stats(self)


@dataclass
class SaveStatement(Statement):
    """SAVE data TO database AS name."""
    data: Expression
    target: str
    name: str
    
    def accept(self, visitor):
        return visitor.visit_save(self)


@dataclass
class LoadStatement(Statement):
    """LOAD FROM database WHERE condition."""
    source: str
    condition: Optional[Expression] = None
    
    def accept(self, visitor):
        return visitor.visit_load(self)


@dataclass
class ExportStatement(Statement):
    """EXPORT data TO filename FORMAT format."""
    data: Expression
    filename: str
    format: str
    
    def accept(self, visitor):
        return visitor.visit_export(self)


@dataclass
class ImportStatement(Statement):
    """IMPORT FROM filename FORMAT format."""
    filename: str
    format: str
    
    def accept(self, visitor):
        return visitor.visit_import(self)


@dataclass
class ChartStatement(Statement):
    """CHART type OF field FROM collection."""
    chart_type: str  # histogram, scatter, bar
    field: str
    collection: Expression
    
    def accept(self, visitor):
        return visitor.visit_chart(self)


# Function and procedure definitions
@dataclass
class FunctionDefinition(Statement):
    """FUNCTION name(params) RETURNS type ... END."""
    name: str
    parameters: List[str]
    return_type: Optional[str]
    body: List[Statement]
    
    def accept(self, visitor):
        return visitor.visit_function_definition(self)


@dataclass
class ProcedureDefinition(Statement):
    """PROCEDURE name(params) ... END."""
    name: str
    parameters: List[str]
    body: List[Statement]
    
    def accept(self, visitor):
        return visitor.visit_procedure_definition(self)


@dataclass
class CallStatement(Statement):
    """CALL procedure_name(args)."""
    name: str
    arguments: List[Expression]
    
    def accept(self, visitor):
        return visitor.visit_call(self)


# Program structure
@dataclass
class Program(ASTNode):
    """Root node representing the entire program."""
    statements: List[Statement]
    
    def accept(self, visitor):
        return visitor.visit_program(self)


# Visitor interface
class ASTVisitor(ABC):
    """Visitor interface for traversing AST."""
    
    @abstractmethod
    def visit_literal(self, node: LiteralExpression):
        pass
    
    @abstractmethod
    def visit_identifier(self, node: IdentifierExpression):
        pass
    
    @abstractmethod
    def visit_binary(self, node: BinaryExpression):
        pass
    
    @abstractmethod
    def visit_unary(self, node: UnaryExpression):
        pass
    
    @abstractmethod
    def visit_function_call(self, node: FunctionCallExpression):
        pass
    
    @abstractmethod
    def visit_array(self, node: ArrayExpression):
        pass
    
    @abstractmethod
    def visit_range(self, node: RangeExpression):
        pass
    
    @abstractmethod
    def visit_property_access(self, node: PropertyAccessExpression):
        pass
    
    @abstractmethod
    def visit_assignment(self, node: AssignmentStatement):
        pass
    
    @abstractmethod
    def visit_print(self, node: PrintStatement):
        pass
    
    @abstractmethod
    def visit_if(self, node: IfStatement):
        pass
    
    @abstractmethod
    def visit_for(self, node: ForStatement):
        pass
    
    @abstractmethod
    def visit_while(self, node: WhileStatement):
        pass
    
    @abstractmethod
    def visit_return(self, node: ReturnStatement):
        pass
    
    @abstractmethod
    def visit_expression_statement(self, node: ExpressionStatement):
        pass
    
    @abstractmethod
    def visit_find(self, node: FindStatement):
        pass
    
    @abstractmethod
    def visit_sort(self, node: SortStatement):
        pass
    
    @abstractmethod
    def visit_limit(self, node: LimitStatement):
        pass
    
    @abstractmethod
    def visit_group(self, node: GroupStatement):
        pass
    
    @abstractmethod
    def visit_stats(self, node: StatsStatement):
        pass
    
    @abstractmethod
    def visit_save(self, node: SaveStatement):
        pass
    
    @abstractmethod
    def visit_load(self, node: LoadStatement):
        pass
    
    @abstractmethod
    def visit_export(self, node: ExportStatement):
        pass
    
    @abstractmethod
    def visit_import(self, node: ImportStatement):
        pass
    
    @abstractmethod
    def visit_chart(self, node: ChartStatement):
        pass
    
    @abstractmethod
    def visit_function_definition(self, node: FunctionDefinition):
        pass
    
    @abstractmethod
    def visit_procedure_definition(self, node: ProcedureDefinition):
        pass
    
    @abstractmethod
    def visit_call(self, node: CallStatement):
        pass
    
    @abstractmethod
    def visit_program(self, node: Program):
        pass


# Utility functions for AST manipulation
def print_ast(node: ASTNode, indent: int = 0) -> str:
    """Pretty print AST for debugging."""
    spaces = "  " * indent
    
    if isinstance(node, LiteralExpression):
        return f"{spaces}Literal({node.value})"
    elif isinstance(node, IdentifierExpression):
        return f"{spaces}Identifier({node.name})"
    elif isinstance(node, BinaryExpression):
        result = f"{spaces}Binary({node.operator})\n"
        result += print_ast(node.left, indent + 1) + "\n"
        result += print_ast(node.right, indent + 1)
        return result
    elif isinstance(node, FindStatement):
        result = f"{spaces}Find({node.target})\n"
        result += print_ast(node.condition, indent + 1)
        return result
    elif isinstance(node, Program):
        result = f"{spaces}Program\n"
        for stmt in node.statements:
            result += print_ast(stmt, indent + 1) + "\n"
        return result.rstrip()
    else:
        return f"{spaces}{type(node).__name__}"


# Example usage
if __name__ == "__main__":
    # Create a simple AST for: FIND quadsets WHERE sum > 1000000
    condition = BinaryExpression(
        left=IdentifierExpression("sum"),
        operator=">",
        right=LiteralExpression(1000000)
    )
    
    find_stmt = FindStatement(
        target="quadsets",
        condition=condition
    )
    
    program = Program([find_stmt])
    
    print("🌳 QuadScript AST Example:")
    print("=" * 40)
    print(print_ast(program))
    print("\n✅ AST structure created successfully!") 