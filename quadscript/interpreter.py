"""
@file quadscript/interpreter.py
@description Interpreter for QuadScript domain-specific language
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies typing, math, sqlite3
"""

import math
import sqlite3
import os
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass

from .ast_nodes import *


@dataclass
class Quadset:
    """Represents a quadset with four members."""
    members: List[int]
    name: Optional[str] = None
    
    @property
    def sum(self) -> int:
        return sum(self.members)
    
    @property
    def max_member(self) -> int:
        return max(self.members)
    
    @property
    def min_member(self) -> int:
        return min(self.members)
    
    @property
    def avg(self) -> float:
        return sum(self.members) / len(self.members)
    
    def __str__(self):
        return f"[{', '.join(map(str, self.members))}]"


class QuadsetCollection:
    """Collection of quadsets with filtering and analysis capabilities."""
    
    def __init__(self, quadsets: List[Quadset]):
        self.quadsets = quadsets
    
    def size(self) -> int:
        return len(self.quadsets)
    
    def filter(self, condition_func) -> 'QuadsetCollection':
        """Filter quadsets based on a condition function."""
        filtered = [q for q in self.quadsets if condition_func(q)]
        return QuadsetCollection(filtered)
    
    def sort_by(self, field: str, descending: bool = False) -> 'QuadsetCollection':
        """Sort quadsets by a field."""
        if field == "sum":
            key_func = lambda q: q.sum
        elif field == "max_member":
            key_func = lambda q: q.max_member
        elif field == "min_member":
            key_func = lambda q: q.min_member
        else:
            key_func = lambda q: getattr(q, field, 0)
        
        sorted_quads = sorted(self.quadsets, key=key_func, reverse=descending)
        return QuadsetCollection(sorted_quads)
    
    def limit(self, count: int) -> 'QuadsetCollection':
        """Limit collection to first N quadsets."""
        return QuadsetCollection(self.quadsets[:count])
    
    def __iter__(self):
        return iter(self.quadsets)


class MathUtils:
    """Mathematical utility functions for QuadScript."""
    
    @staticmethod
    def ternary_digits(number: int) -> int:
        """Calculate number of ternary digits."""
        if number <= 0:
            return 1
        count = 0
        while number > 0:
            number //= 3
            count += 1
        return count
    
    @staticmethod
    def to_ternary(number: int) -> str:
        """Convert number to ternary string."""
        if number == 0:
            return "0"
        
        result = ""
        while number > 0:
            result = str(number % 3) + result
            number //= 3
        return result
    
    @staticmethod
    def from_ternary(ternary_str: str) -> int:
        """Convert ternary string to decimal number."""
        result = 0
        power = 0
        for digit in reversed(ternary_str):
            result += int(digit) * (3 ** power)
            power += 1
        return result
    
    @staticmethod
    def is_prime(number: int) -> bool:
        """Check if number is prime."""
        if number < 2:
            return False
        if number == 2:
            return True
        if number % 2 == 0:
            return False
        
        for i in range(3, int(math.sqrt(number)) + 1, 2):
            if number % i == 0:
                return False
        return True
    
    @staticmethod
    def is_perfect_square(number: int) -> bool:
        """Check if number is a perfect square."""
        if number < 0:
            return False
        root = int(math.sqrt(number))
        return root * root == number
    
    @staticmethod
    def is_triangular(number: int) -> bool:
        """Check if number is triangular."""
        if number < 0:
            return False
        # n = (sqrt(8*number + 1) - 1) / 2
        discriminant = 8 * number + 1
        sqrt_discriminant = int(math.sqrt(discriminant))
        return sqrt_discriminant * sqrt_discriminant == discriminant and (sqrt_discriminant - 1) % 2 == 0
    
    @staticmethod
    def is_fibonacci(number: int) -> bool:
        """Check if number is in Fibonacci sequence."""
        if number < 0:
            return False
        
        # A number is Fibonacci if one of (5*n^2 + 4) or (5*n^2 - 4) is a perfect square
        return (MathUtils.is_perfect_square(5 * number * number + 4) or 
                MathUtils.is_perfect_square(5 * number * number - 4))


class QuadScriptInterpreter(ASTVisitor):
    """Interpreter for QuadScript language."""
    
    def __init__(self):
        self.variables: Dict[str, Any] = {}
        self.functions: Dict[str, FunctionDefinition] = {}
        self.procedures: Dict[str, ProcedureDefinition] = {}
        self.database_path: Optional[str] = None
        self.math_utils = MathUtils()
        self.quadsets = {}
        
        # Sample quadsets for testing
        self._initialize_sample_data()
    
    def _initialize_sample_data(self):
        """Initialize with real quadset data from database."""
        db_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "quadsets.db")
        
        if os.path.exists(db_path):
            print(f"📊 Loading quadset database: {db_path}")
            self._load_database_quadsets(db_path)
        else:
            print("⚠️ Quadset database not found, using sample data")
            self._load_sample_quadsets()
    
    def _load_database_quadsets(self, db_path: str):
        """Load quadsets from the real database."""
        try:
            import sqlite3
            # Create connection with thread safety enabled
            conn = sqlite3.connect(db_path, check_same_thread=False)
            cursor = conn.cursor()
            
            # Get database statistics first
            cursor.execute("SELECT COUNT(*) FROM quadsets")
            total_count = cursor.fetchone()[0]
            print(f"✅ Connected to quadset database with {total_count:,} records")
            
            # Load a reasonable sample for interactive use (first 10,000 records)
            # Full database queries will be handled differently
            cursor.execute("""
                SELECT base_number, conrune, reversal, reversal_conrune,
                       base_conrune_diff, reversal_reversal_conrune_diff,
                       transition_result_decimal, quadset_sum, ternary_digits
                FROM quadsets 
                ORDER BY base_number 
                LIMIT 10000
            """)
            
            records = cursor.fetchall()
            
            for record in records:
                base, conrune, reversal, rev_conrune, diff1, diff2, transition, qsum, digits = record
                
                quadset_data = {
                    'base': base,
                    'conrune': conrune,
                    'reversal': reversal,
                    'reversal_conrune': rev_conrune,
                    'base_conrune_diff': diff1,
                    'reversal_reversal_conrune_diff': diff2,
                    'transition': transition,
                    'sum': qsum,
                    'ternary_digits': digits
                }
                
                self.variables[f"quadset_{base}"] = quadset_data
            
            # Store database path for creating new connections as needed
            self.database_path = db_path
            
            # Close the initial connection - we'll create new ones as needed
            conn.close()
            
            print(f"📈 Loaded {len(records):,} quadsets into memory for fast access")
            print(f"🔗 Database connection maintained for complex queries")
            
        except Exception as e:
            print(f"❌ Error loading database: {e}")
            print("🔄 Falling back to sample data")
            self._load_sample_quadsets()
    
    def _get_database_connection(self):
        """Get a thread-safe database connection."""
        if not self.database_path:
            raise RuntimeError("No database path available")
        
        import sqlite3
        # Create a new connection for this thread
        return sqlite3.connect(self.database_path, check_same_thread=False)

    def execute_database_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """Execute a direct SQL query on the quadset database."""
        if not self.database_path:
            raise RuntimeError("No database connection available")
        
        try:
            # Create a new connection for this query
            conn = self._get_database_connection()
            cursor = conn.cursor()
            
            cursor.execute(query, params)
            results = cursor.fetchall()
            
            # Convert to list of dictionaries for easier handling
            columns = [desc[0] for desc in cursor.description]
            result_dicts = [dict(zip(columns, row)) for row in results]
            
            # Close the connection
            conn.close()
            
            return result_dicts
        
        except Exception as e:
            raise RuntimeError(f"Database query failed: {e}")
    
    def _load_sample_quadsets(self):
        """Fallback sample data if database is unavailable."""
        # Keep the existing sample data as fallback
        sample_quadsets = [
            (1, 2, 243, 486, 1, 243, 488, 732, 1),
            (10, 20, 270, 540, 10, 270, 560, 840, 3),
            (100, 173, 300, 519, 73, 219, 152, 1092, 5),
            (1000, 1730, 3000, 5190, 730, 2190, 1520, 10920, 7)
        ]
        
        for base, conrune, reversal, rev_conrune, diff1, diff2, transition, qsum, digits in sample_quadsets:
            self.variables[f"quadset_{base}"] = {
                'base': base,
                'conrune': conrune,
                'reversal': reversal,
                'reversal_conrune': rev_conrune,
                'base_conrune_diff': diff1,
                'reversal_reversal_conrune_diff': diff2,
                'transition': transition,
                'sum': qsum,
                'ternary_digits': digits
            }
    
    def connect_database(self, db_path: str):
        """Connect to quadset database."""
        self.database_path = db_path
    
    def execute(self, program: Program) -> Any:
        """Execute a QuadScript program."""
        return program.accept(self)
    
    # Expression visitors
    def visit_literal(self, node: LiteralExpression) -> Any:
        return node.value
    
    def visit_identifier(self, node: IdentifierExpression) -> Any:
        if node.name in self.variables:
            return self.variables[node.name]
        else:
            raise NameError(f"Undefined variable: {node.name}")
    
    def visit_binary(self, node: BinaryExpression) -> Any:
        left = node.left.accept(self)
        right = node.right.accept(self)
        
        if node.operator == "+":
            return left + right
        elif node.operator == "-":
            return left - right
        elif node.operator == "*":
            return left * right
        elif node.operator == "/":
            return left / right
        elif node.operator == "%":
            return left % right
        elif node.operator == "==":
            return left == right
        elif node.operator == "!=":
            return left != right
        elif node.operator == "<":
            return left < right
        elif node.operator == ">":
            return left > right
        elif node.operator == "<=":
            return left <= right
        elif node.operator == ">=":
            return left >= right
        elif node.operator == "AND":
            return left and right
        elif node.operator == "OR":
            return left or right
        else:
            raise ValueError(f"Unknown binary operator: {node.operator}")
    
    def visit_unary(self, node: UnaryExpression) -> Any:
        operand = node.operand.accept(self)
        
        if node.operator == "-":
            return -operand
        elif node.operator == "NOT":
            return not operand
        else:
            raise ValueError(f"Unknown unary operator: {node.operator}")
    
    def visit_function_call(self, node: FunctionCallExpression) -> Any:
        args = [arg.accept(self) for arg in node.arguments]
        
        # Built-in mathematical functions
        if node.name == "ternary_digits":
            return self.math_utils.ternary_digits(args[0])
        elif node.name == "to_ternary":
            return self.math_utils.to_ternary(args[0])
        elif node.name == "from_ternary":
            return self.math_utils.from_ternary(args[0])
        elif node.name == "is_prime":
            return self.math_utils.is_prime(args[0])
        elif node.name == "is_perfect_square":
            return self.math_utils.is_perfect_square(args[0])
        elif node.name == "is_triangular":
            return self.math_utils.is_triangular(args[0])
        elif node.name == "is_fibonacci":
            return self.math_utils.is_fibonacci(args[0])
        elif node.name == "sum":
            if isinstance(args[0], Quadset):
                return args[0].sum
            elif isinstance(args[0], QuadsetCollection):
                return sum(q.sum for q in args[0])
            else:
                return sum(args[0])
        elif node.name == "max":
            if isinstance(args[0], Quadset):
                return args[0].max_member
            else:
                return max(args[0])
        elif node.name == "min":
            if isinstance(args[0], Quadset):
                return args[0].min_member
            else:
                return min(args[0])
        elif node.name == "avg":
            if isinstance(args[0], Quadset):
                return args[0].avg
            else:
                return sum(args[0]) / len(args[0])
        
        # User-defined functions
        elif node.name in self.functions:
            func_def = self.functions[node.name]
            # Create new scope for function execution
            old_vars = self.variables.copy()
            
            # Bind parameters
            for i, param in enumerate(func_def.parameters):
                if i < len(args):
                    self.variables[param] = args[i]
            
            # Execute function body
            result = None
            for stmt in func_def.body:
                result = stmt.accept(self)
                if isinstance(stmt, ReturnStatement):
                    break
            
            # Restore scope
            self.variables = old_vars
            return result
        
        else:
            raise NameError(f"Unknown function: {node.name}")
    
    def visit_array(self, node: ArrayExpression) -> List[Any]:
        return [element.accept(self) for element in node.elements]
    
    def visit_range(self, node: RangeExpression) -> range:
        start = node.start.accept(self)
        end = node.end.accept(self)
        return range(start, end + 1)
    
    def visit_property_access(self, node: PropertyAccessExpression) -> Any:
        obj = node.object.accept(self)
        
        if isinstance(obj, Quadset):
            if node.property == "sum":
                return obj.sum
            elif node.property == "max_member":
                return obj.max_member
            elif node.property == "min_member":
                return obj.min_member
            elif node.property == "avg":
                return obj.avg
            elif node.property == "ternary_digits":
                return [self.math_utils.ternary_digits(m) for m in obj.members]
            elif node.property == "uniform_ternary_digits":
                digits = [self.math_utils.ternary_digits(m) for m in obj.members]
                return len(set(digits)) == 1
        elif isinstance(obj, QuadsetCollection):
            if node.property == "size":
                return obj.size()
        elif isinstance(obj, int):
            if node.property == "ternary_digits":
                return self.math_utils.ternary_digits(obj)
            elif node.property == "divisible_by":
                return lambda divisor: obj % divisor == 0
        
        raise AttributeError(f"Unknown property: {node.property}")
    
    # Statement visitors
    def visit_assignment(self, node: AssignmentStatement) -> None:
        value = node.value.accept(self)
        self.variables[node.name] = value
    
    def visit_print(self, node: PrintStatement) -> None:
        value = node.expression.accept(self)
        print(value)
    
    def visit_if(self, node: IfStatement) -> Any:
        condition = node.condition.accept(self)
        
        if condition:
            result = None
            for stmt in node.then_branch:
                result = stmt.accept(self)
            return result
        elif node.else_branch:
            result = None
            for stmt in node.else_branch:
                result = stmt.accept(self)
            return result
    
    def visit_for(self, node: ForStatement) -> None:
        iterable = node.iterable.accept(self)
        
        for item in iterable:
            self.variables[node.variable] = item
            for stmt in node.body:
                stmt.accept(self)
    
    def visit_while(self, node: WhileStatement) -> None:
        while node.condition.accept(self):
            for stmt in node.body:
                stmt.accept(self)
    
    def visit_return(self, node: ReturnStatement) -> Any:
        if node.value:
            return node.value.accept(self)
        return None
    
    def visit_expression_statement(self, node: ExpressionStatement) -> Any:
        return node.expression.accept(self)
    
    # QuadScript-specific visitors
    def visit_find(self, node: FindStatement) -> QuadsetCollection:
        if node.target == "quadsets":
            all_quadsets = self.variables.get("all_quadsets", QuadsetCollection([]))
            
            # Create a filter function from the condition
            def condition_func(quadset):
                # Temporarily bind 'quadset' variables for condition evaluation
                old_vars = self.variables.copy()
                self.variables["sum"] = quadset.sum
                self.variables["max_member"] = quadset.max_member
                self.variables["min_member"] = quadset.min_member
                self.variables["uniform_ternary_digits"] = getattr(quadset, 'uniform_ternary_digits', False)
                self.variables["all_prime"] = all(self.math_utils.is_prime(m) for m in quadset.members)
                
                try:
                    result = node.condition.accept(self)
                    return result
                finally:
                    self.variables = old_vars
            
            return all_quadsets.filter(condition_func)
        else:
            raise ValueError(f"Unknown target: {node.target}")
    
    def visit_sort(self, node: SortStatement) -> QuadsetCollection:
        collection = node.collection.accept(self)
        return collection.sort_by(node.field, node.descending)
    
    def visit_limit(self, node: LimitStatement) -> QuadsetCollection:
        collection = node.collection.accept(self)
        count = node.count.accept(self)
        return collection.limit(count)
    
    def visit_group(self, node: GroupStatement) -> Dict[Any, QuadsetCollection]:
        collection = node.collection.accept(self)
        groups = {}
        
        for quadset in collection:
            if node.field == "sum":
                key = quadset.sum
            elif node.field == "max_member":
                key = quadset.max_member
            else:
                key = getattr(quadset, node.field, None)
            
            if key not in groups:
                groups[key] = []
            groups[key].append(quadset)
        
        return {k: QuadsetCollection(v) for k, v in groups.items()}
    
    def visit_stats(self, node: StatsStatement) -> Dict[str, Any]:
        collection = node.collection.accept(self)
        stats = {}
        
        for field_name, expression in node.fields.items():
            # Implement basic stats calculations
            if field_name == "count":
                stats[field_name] = collection.size()
            elif field_name == "avg_sum":
                stats[field_name] = sum(q.sum for q in collection) / collection.size()
            elif field_name == "max_sum":
                stats[field_name] = max(q.sum for q in collection)
            elif field_name == "min_sum":
                stats[field_name] = min(q.sum for q in collection)
        
        return stats
    
    def visit_save(self, node: SaveStatement) -> None:
        # Placeholder for database save operation
        data = node.data.accept(self)
        print(f"Saving {data} to {node.target} as {node.name}")
    
    def visit_load(self, node: LoadStatement) -> Any:
        # Placeholder for database load operation
        print(f"Loading from {node.source}")
        return QuadsetCollection([])
    
    def visit_export(self, node: ExportStatement) -> None:
        data = node.data.accept(self)
        print(f"Exporting {data} to {node.filename} in {node.format} format")
    
    def visit_import(self, node: ImportStatement) -> Any:
        print(f"Importing from {node.filename} in {node.format} format")
        return QuadsetCollection([])
    
    def visit_chart(self, node: ChartStatement) -> None:
        collection = node.collection.accept(self)
        print(f"Creating {node.chart_type} chart of {node.field} from {collection.size()} quadsets")
    
    def visit_function_definition(self, node: FunctionDefinition) -> None:
        self.functions[node.name] = node
    
    def visit_procedure_definition(self, node: ProcedureDefinition) -> None:
        self.procedures[node.name] = node
    
    def visit_call(self, node: CallStatement) -> Any:
        if node.name in self.procedures:
            proc_def = self.procedures[node.name]
            args = [arg.accept(self) for arg in node.arguments]
            
            # Create new scope
            old_vars = self.variables.copy()
            
            # Bind parameters
            for i, param in enumerate(proc_def.parameters):
                if i < len(args):
                    self.variables[param] = args[i]
            
            # Execute procedure body
            for stmt in proc_def.body:
                stmt.accept(self)
            
            # Restore scope
            self.variables = old_vars
        else:
            raise NameError(f"Unknown procedure: {node.name}")
    
    def visit_program(self, node: Program) -> Any:
        result = None
        for statement in node.statements:
            result = statement.accept(self)
        return result

    def find_quadsets_by_sum(self, target_sum: int, limit: int = 100) -> List[Dict]:
        """Find quadsets with a specific sum."""
        query = """
            SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
            FROM quadsets 
            WHERE quadset_sum = ?
            AND base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            ORDER BY base_number
            LIMIT ?
        """
        return self.execute_database_query(query, (target_sum, limit))
    
    def find_quadsets_by_transition(self, transition_value: int, limit: int = 100) -> List[Dict]:
        """Find quadsets with a specific transition result."""
        query = """
            SELECT base_number, base_conrune_diff, reversal_reversal_conrune_diff, 
                   transition_result_decimal, quadset_sum
            FROM quadsets 
            WHERE transition_result_decimal = ?
            AND base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            ORDER BY base_number
            LIMIT ?
        """
        return self.execute_database_query(query, (transition_value, limit))
    
    def find_quadsets_by_ternary_digits(self, digit_count: int, limit: int = 100) -> List[Dict]:
        """Find quadsets with a specific number of ternary digits."""
        query = """
            SELECT base_number, base_ternary, ternary_digits, quadset_sum
            FROM quadsets 
            WHERE ternary_digits = ?
            AND base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            ORDER BY base_number
            LIMIT ?
        """
        return self.execute_database_query(query, (digit_count, limit))
    
    def find_palindromic_quadsets(self, limit: int = 100) -> List[Dict]:
        """Find quadsets where base equals reversal (palindromic in ternary)."""
        query = """
            SELECT base_number, base_ternary, reversal, quadset_sum
            FROM quadsets 
            WHERE base_number = reversal
            AND base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            ORDER BY base_number
            LIMIT ?
        """
        return self.execute_database_query(query, (limit,))
    
    def find_self_conrune_quadsets(self, limit: int = 100) -> List[Dict]:
        """Find quadsets where base equals conrune."""
        query = """
            SELECT base_number, base_ternary, conrune, quadset_sum
            FROM quadsets 
            WHERE base_number = conrune
            AND base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            ORDER BY base_number
            LIMIT ?
        """
        return self.execute_database_query(query, (limit,))
    
    def get_quadset_statistics(self) -> Dict:
        """Get comprehensive statistics about the quadset database."""
        if not self.database_path:
            return {"error": "No database connection"}
        
        stats = {}
        
        try:
            # Create a new connection for this operation
            conn = self._get_database_connection()
            cursor = conn.cursor()
            
            # Basic counts - count unique quadsets (divide by 4 since each quadset has 4 rows)
            cursor.execute("SELECT COUNT(*) as total FROM quadsets")
            result = cursor.fetchone()
            stats['total_quadsets'] = result[0] // 4  # Each unique quadset has 4 rows
            
            # Sum statistics - use only the minimum base_number per unique quadset
            cursor.execute("""
                SELECT MIN(quadset_sum) as min_sum, MAX(quadset_sum) as max_sum, 
                       AVG(quadset_sum) as avg_sum 
                FROM quadsets 
                WHERE base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
            """)
            result = cursor.fetchone()
            stats['min_sum'] = result[0]
            stats['max_sum'] = result[1]
            stats['avg_sum'] = result[2]
            
            # Ternary digit distribution - count unique quadsets only
            cursor.execute("""
                SELECT ternary_digits, COUNT(*) as count 
                FROM quadsets 
                WHERE base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
                GROUP BY ternary_digits 
                ORDER BY ternary_digits
            """)
            results = cursor.fetchall()
            stats['ternary_digit_distribution'] = {row[0]: row[1] for row in results}
            
            # Most common transitions - count unique quadsets only
            cursor.execute("""
                SELECT transition_result_decimal, COUNT(*) as frequency
                FROM quadsets 
                WHERE base_number = LEAST(base_number, conrune, reversal, reversal_conrune)
                GROUP BY transition_result_decimal 
                ORDER BY frequency DESC 
                LIMIT 10
            """)
            results = cursor.fetchall()
            stats['top_transitions'] = [{'transition': row[0], 'frequency': row[1]} for row in results]
            
            # Close the connection
            conn.close()
            
            return stats
            
        except Exception as e:
            return {"error": f"Failed to get statistics: {e}"}


# Example usage
if __name__ == "__main__":
    # Create a simple program: large_quads = FIND quadsets WHERE sum > 1000000
    condition = BinaryExpression(
        left=IdentifierExpression("sum"),
        operator=">",
        right=LiteralExpression(1000000)
    )
    
    find_stmt = FindStatement(
        target="quadsets",
        condition=condition
    )
    
    assignment = AssignmentStatement(
        name="large_quads",
        value=find_stmt
    )
    
    print_stmt = PrintStatement(
        expression=FunctionCallExpression(
            name="sum",
            arguments=[IdentifierExpression("large_quads")]
        )
    )
    
    program = Program([assignment, print_stmt])
    
    # Execute the program
    interpreter = QuadScriptInterpreter()
    print("🚀 QuadScript Interpreter Test:")
    print("=" * 40)
    print("Executing: large_quads = FIND quadsets WHERE sum > 1000000")
    
    result = interpreter.execute(program)
    
    print(f"\n✅ Program executed successfully!")
    print(f"Found {interpreter.variables['large_quads'].size()} large quadsets") 