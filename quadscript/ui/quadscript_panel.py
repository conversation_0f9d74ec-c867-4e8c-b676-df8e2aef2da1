"""
@file quadscript/ui/quadscript_panel.py
@description QuadScript interactive panel for quadset analysis
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies PyQt6, quadscript
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Any, Optional

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTextEdit, QPushButton, QLabel, QComboBox, QSpinBox,
    QTableWidget, QTableWidgetItem, QTabWidget,
    QSplitter, QGroupBox, QLineEdit, QProgressBar,
    QScrollArea, QFrame, QMessageBox, QCheckBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QTextCursor, QPalette

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from quadscript.interpreter import QuadScriptInterpreter
from quadscript.lexer import QuadScriptLexer


class DatabaseQueryThread(QThread):
    """Background thread for database queries to prevent UI freezing."""
    
    query_finished = pyqtSignal(list)
    query_error = pyqtSignal(str)
    
    def __init__(self, interpreter: QuadScriptInterpreter, query_type: str, **kwargs):
        super().__init__()
        self.interpreter = interpreter
        self.query_type = query_type
        self.kwargs = kwargs
    
    def run(self):
        """Execute the database query in background."""
        try:
            if self.query_type == "by_sum":
                results = self.interpreter.find_quadsets_by_sum(
                    self.kwargs['target_sum'], 
                    self.kwargs.get('limit', 100)
                )
            elif self.query_type == "by_transition":
                results = self.interpreter.find_quadsets_by_transition(
                    self.kwargs['transition_value'], 
                    self.kwargs.get('limit', 100)
                )
            elif self.query_type == "by_digits":
                results = self.interpreter.find_quadsets_by_ternary_digits(
                    self.kwargs['digit_count'], 
                    self.kwargs.get('limit', 100)
                )
            elif self.query_type == "palindromic":
                results = self.interpreter.find_palindromic_quadsets(
                    self.kwargs.get('limit', 100)
                )
            elif self.query_type == "self_conrune":
                results = self.interpreter.find_self_conrune_quadsets(
                    self.kwargs.get('limit', 100)
                )
            elif self.query_type == "custom":
                results = self.interpreter.execute_database_query(
                    self.kwargs['query'], 
                    self.kwargs.get('params', ())
                )
            else:
                raise ValueError(f"Unknown query type: {self.query_type}")
            
            self.query_finished.emit(results)
            
        except Exception as e:
            self.query_error.emit(str(e))


class QuadScriptPanel(QWidget):
    """Interactive QuadScript panel for quadset analysis."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.interpreter: Optional[QuadScriptInterpreter] = None
        self.query_thread: Optional[DatabaseQueryThread] = None
        
        self.init_ui()
        self.init_interpreter()
        
    def init_ui(self):
        """Initialize the user interface."""
        self.setWindowTitle("QuadScript - Quadset Analysis")
        self.setMinimumSize(1200, 800)
        
        # Main layout
        main_layout = QVBoxLayout(self)
        
        # Header
        header_layout = QHBoxLayout()
        title_label = QLabel("🚀 QuadScript - Quadset Analysis Engine")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        # Connection status
        self.status_label = QLabel("🔄 Initializing...")
        header_layout.addWidget(self.status_label)
        header_layout.addStretch()
        
        main_layout.addLayout(header_layout)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # Create tabs
        self.create_database_tab()
        self.create_query_tab()
        self.create_script_tab()
        self.create_analysis_tab()
        
    def create_database_tab(self):
        """Create database overview tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Database statistics
        stats_group = QGroupBox("📊 Database Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.stats_labels = {}
        stats_fields = [
            ("total_quadsets", "Total Quadsets:"),
            ("min_sum", "Minimum Sum:"),
            ("max_sum", "Maximum Sum:"),
            ("avg_sum", "Average Sum:"),
            ("unique_transitions", "Unique Transitions:"),
            ("unique_sums", "Unique Sums:")
        ]
        
        for i, (key, label) in enumerate(stats_fields):
            stats_layout.addWidget(QLabel(label), i // 2, (i % 2) * 2)
            value_label = QLabel("Loading...")
            self.stats_labels[key] = value_label
            stats_layout.addWidget(value_label, i // 2, (i % 2) * 2 + 1)
        
        layout.addWidget(stats_group)
        
        # Ternary digit distribution
        distribution_group = QGroupBox("🔢 Ternary Digit Distribution")
        distribution_layout = QVBoxLayout(distribution_group)
        
        self.distribution_table = QTableWidget()
        self.distribution_table.setColumnCount(2)
        self.distribution_table.setHorizontalHeaderLabels(["Digits", "Count"])
        distribution_layout.addWidget(self.distribution_table)
        
        layout.addWidget(distribution_group)
        
        # Top transitions
        transitions_group = QGroupBox("🔄 Most Common Transitions")
        transitions_layout = QVBoxLayout(transitions_group)
        
        self.transitions_table = QTableWidget()
        self.transitions_table.setColumnCount(2)
        self.transitions_table.setHorizontalHeaderLabels(["Transition Value", "Frequency"])
        transitions_layout.addWidget(self.transitions_table)
        
        layout.addWidget(transitions_group)
        
        self.tab_widget.addTab(tab, "📊 Database")
        
    def create_query_tab(self):
        """Create query interface tab."""
        tab = QWidget()
        layout = QHBoxLayout(tab)
        
        # Left panel - Query controls
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setMaximumWidth(400)
        
        # Query type selection
        query_group = QGroupBox("🔍 Quick Queries")
        query_layout = QVBoxLayout(query_group)
        
        # By sum
        sum_layout = QHBoxLayout()
        sum_layout.addWidget(QLabel("Find by Sum:"))
        self.sum_input = QSpinBox()
        self.sum_input.setRange(1, 50000000)
        self.sum_input.setValue(1092)
        sum_layout.addWidget(self.sum_input)
        sum_btn = QPushButton("Search")
        sum_btn.clicked.connect(self.search_by_sum)
        sum_layout.addWidget(sum_btn)
        query_layout.addLayout(sum_layout)
        
        # By transition
        transition_layout = QHBoxLayout()
        transition_layout.addWidget(QLabel("Find by Transition:"))
        self.transition_input = QSpinBox()
        self.transition_input.setRange(1, 50000000)
        self.transition_input.setValue(168)
        transition_layout.addWidget(self.transition_input)
        transition_btn = QPushButton("Search")
        transition_btn.clicked.connect(self.search_by_transition)
        transition_layout.addWidget(transition_btn)
        query_layout.addLayout(transition_layout)
        
        # By ternary digits
        digits_layout = QHBoxLayout()
        digits_layout.addWidget(QLabel("Find by Digits:"))
        self.digits_input = QSpinBox()
        self.digits_input.setRange(1, 16)
        self.digits_input.setValue(5)
        digits_layout.addWidget(self.digits_input)
        digits_btn = QPushButton("Search")
        digits_btn.clicked.connect(self.search_by_digits)
        digits_layout.addWidget(digits_btn)
        query_layout.addLayout(digits_layout)
        
        # Special patterns
        patterns_layout = QVBoxLayout()
        palindrome_btn = QPushButton("🪞 Find Palindromic Quadsets")
        palindrome_btn.clicked.connect(self.search_palindromic)
        patterns_layout.addWidget(palindrome_btn)
        
        self_conrune_btn = QPushButton("🔄 Find Self-Conrune Quadsets")
        self_conrune_btn.clicked.connect(self.search_self_conrune)
        patterns_layout.addWidget(self_conrune_btn)
        
        query_layout.addLayout(patterns_layout)
        
        # Limit setting
        limit_layout = QHBoxLayout()
        limit_layout.addWidget(QLabel("Result Limit:"))
        self.limit_input = QSpinBox()
        self.limit_input.setRange(1, 10000)
        self.limit_input.setValue(100)
        limit_layout.addWidget(self.limit_input)
        query_layout.addLayout(limit_layout)
        
        left_layout.addWidget(query_group)
        
        # Custom SQL query
        sql_group = QGroupBox("💻 Custom SQL Query")
        sql_layout = QVBoxLayout(sql_group)
        
        self.sql_input = QTextEdit()
        self.sql_input.setMaximumHeight(150)
        self.sql_input.setPlainText("""SELECT base_number, quadset_sum 
FROM quadsets 
ORDER BY quadset_sum DESC 
LIMIT 10""")
        sql_layout.addWidget(self.sql_input)
        
        sql_btn = QPushButton("Execute SQL")
        sql_btn.clicked.connect(self.execute_custom_sql)
        sql_layout.addWidget(sql_btn)
        
        left_layout.addWidget(sql_group)
        left_layout.addStretch()
        
        layout.addWidget(left_panel)
        
        # Right panel - Results
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        
        # Results header
        results_header = QHBoxLayout()
        results_header.addWidget(QLabel("📋 Query Results"))
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        results_header.addWidget(self.progress_bar)
        
        right_layout.addLayout(results_header)
        
        # Results table
        self.results_table = QTableWidget()
        right_layout.addWidget(self.results_table)
        
        layout.addWidget(right_panel)
        
        self.tab_widget.addTab(tab, "🔍 Queries")
        
    def create_script_tab(self):
        """Create QuadScript scripting tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Script editor
        editor_group = QGroupBox("📝 QuadScript Editor")
        editor_layout = QVBoxLayout(editor_group)
        
        self.script_editor = QTextEdit()
        self.script_editor.setFont(QFont("Consolas", 10))
        self.script_editor.setPlainText("""// QuadScript Example
// Find quadsets with specific properties

// Access a quadset
let q100 = quadset_100
print "Quadset 100:", q100

// Mathematical operations
let sum_check = q100.base + q100.conrune + q100.reversal + q100.reversal_conrune
print "Sum verification:", sum_check, "vs stored:", q100.sum

// Conditional analysis
if q100.ternary_digits > 3 then
    print "Quadset 100 has", q100.ternary_digits, "ternary digits"
end""")
        editor_layout.addWidget(self.script_editor)
        
        # Script controls
        controls_layout = QHBoxLayout()
        
        tokenize_btn = QPushButton("🔤 Tokenize")
        tokenize_btn.clicked.connect(self.tokenize_script)
        controls_layout.addWidget(tokenize_btn)
        
        execute_btn = QPushButton("🚀 Execute")
        execute_btn.clicked.connect(self.execute_script)
        controls_layout.addWidget(execute_btn)
        
        clear_btn = QPushButton("🗑️ Clear")
        clear_btn.clicked.connect(self.clear_script)
        controls_layout.addWidget(clear_btn)
        
        controls_layout.addStretch()
        editor_layout.addLayout(controls_layout)
        
        layout.addWidget(editor_group)
        
        # Output
        output_group = QGroupBox("📤 Script Output")
        output_layout = QVBoxLayout(output_group)
        
        self.script_output = QTextEdit()
        self.script_output.setReadOnly(True)
        self.script_output.setFont(QFont("Consolas", 9))
        output_layout.addWidget(self.script_output)
        
        layout.addWidget(output_group)
        
        self.tab_widget.addTab(tab, "📝 Scripts")
        
    def create_analysis_tab(self):
        """Create analysis and patterns tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Analysis controls
        controls_group = QGroupBox("🎯 Pattern Analysis")
        controls_layout = QGridLayout(controls_group)
        
        # Mathematical pattern buttons
        patterns = [
            ("All Even Members", self.analyze_all_even),
            ("All Odd Members", self.analyze_all_odd),
            ("Prime Base Numbers", self.analyze_prime_bases),
            ("Perfect Square Sums", self.analyze_perfect_square_sums),
            ("Fibonacci Transitions", self.analyze_fibonacci_transitions),
            ("Large Differences", self.analyze_large_differences)
        ]
        
        for i, (name, callback) in enumerate(patterns):
            btn = QPushButton(name)
            btn.clicked.connect(callback)
            controls_layout.addWidget(btn, i // 2, i % 2)
        
        layout.addWidget(controls_group)
        
        # Analysis results
        results_group = QGroupBox("📊 Analysis Results")
        results_layout = QVBoxLayout(results_group)
        
        self.analysis_output = QTextEdit()
        self.analysis_output.setReadOnly(True)
        self.analysis_output.setFont(QFont("Consolas", 9))
        results_layout.addWidget(self.analysis_output)
        
        layout.addWidget(results_group)
        
        self.tab_widget.addTab(tab, "🎯 Analysis")
        
    def init_interpreter(self):
        """Initialize the QuadScript interpreter."""
        try:
            self.interpreter = QuadScriptInterpreter()
            
            if self.interpreter.database_path:
                self.status_label.setText("✅ Connected to Database")
                self.load_database_statistics()
            else:
                self.status_label.setText("⚠️ Database Not Available")
                
        except Exception as e:
            self.status_label.setText(f"❌ Error: {str(e)}")
            QMessageBox.warning(self, "Initialization Error", f"Failed to initialize QuadScript:\n{str(e)}")
    
    def load_database_statistics(self):
        """Load and display database statistics."""
        if not self.interpreter or not self.interpreter.database_path:
            return
        
        try:
            stats = self.interpreter.get_quadset_statistics()
            
            # Check if stats contain error
            if 'error' in stats:
                QMessageBox.warning(self, "Statistics Error", stats['error'])
                return
            
            # Update statistics labels
            self.stats_labels["total_quadsets"].setText(f"{stats['total_quadsets']:,}")
            self.stats_labels["min_sum"].setText(f"{stats['min_sum']:,}")
            self.stats_labels["max_sum"].setText(f"{stats['max_sum']:,}")
            self.stats_labels["avg_sum"].setText(f"{stats['avg_sum']:,.1f}")
            
            # Update ternary digit distribution
            distribution = stats['ternary_digit_distribution']
            self.distribution_table.setRowCount(len(distribution))
            
            for i, (digits, count) in enumerate(distribution.items()):
                self.distribution_table.setItem(i, 0, QTableWidgetItem(str(digits)))
                self.distribution_table.setItem(i, 1, QTableWidgetItem(f"{count:,}"))
            
            # Update top transitions
            transitions = stats['top_transitions']
            self.transitions_table.setRowCount(len(transitions))
            
            for i, transition in enumerate(transitions):
                self.transitions_table.setItem(i, 0, QTableWidgetItem(f"{transition['transition']:,}"))
                self.transitions_table.setItem(i, 1, QTableWidgetItem(f"{transition['frequency']:,}"))
            
        except Exception as e:
            QMessageBox.warning(self, "Statistics Error", f"Failed to load statistics:\n{str(e)}")
    
    def start_query(self, query_type: str, **kwargs):
        """Start a database query in background thread."""
        if not self.interpreter or not self.interpreter.database_path:
            QMessageBox.warning(self, "Database Error", "No database connection available")
            return
        
        # Show progress
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Start query thread
        self.query_thread = DatabaseQueryThread(self.interpreter, query_type, **kwargs)
        self.query_thread.query_finished.connect(self.display_query_results)
        self.query_thread.query_error.connect(self.handle_query_error)
        self.query_thread.start()
    
    def display_query_results(self, results: List[Dict]):
        """Display query results in the table."""
        self.progress_bar.setVisible(False)
        
        if not results:
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(1)
            self.results_table.setHorizontalHeaderLabels(["No Results"])
            return
        
        # Set up table
        columns = list(results[0].keys())
        self.results_table.setColumnCount(len(columns))
        self.results_table.setHorizontalHeaderLabels(columns)
        self.results_table.setRowCount(len(results))
        
        # Populate table
        for row, result in enumerate(results):
            for col, (key, value) in enumerate(result.items()):
                self.results_table.setItem(row, col, QTableWidgetItem(str(value)))
        
        # Auto-resize columns
        self.results_table.resizeColumnsToContents()
    
    def handle_query_error(self, error_message: str):
        """Handle query errors."""
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "Query Error", f"Query failed:\n{error_message}")
    
    # Query methods
    def search_by_sum(self):
        """Search quadsets by sum."""
        target_sum = self.sum_input.value()
        limit = self.limit_input.value()
        self.start_query("by_sum", target_sum=target_sum, limit=limit)
    
    def search_by_transition(self):
        """Search quadsets by transition value."""
        transition_value = self.transition_input.value()
        limit = self.limit_input.value()
        self.start_query("by_transition", transition_value=transition_value, limit=limit)
    
    def search_by_digits(self):
        """Search quadsets by ternary digits."""
        digit_count = self.digits_input.value()
        limit = self.limit_input.value()
        self.start_query("by_digits", digit_count=digit_count, limit=limit)
    
    def search_palindromic(self):
        """Search palindromic quadsets."""
        limit = self.limit_input.value()
        self.start_query("palindromic", limit=limit)
    
    def search_self_conrune(self):
        """Search self-conrune quadsets."""
        limit = self.limit_input.value()
        self.start_query("self_conrune", limit=limit)
    
    def execute_custom_sql(self):
        """Execute custom SQL query."""
        query = self.sql_input.toPlainText().strip()
        if not query:
            QMessageBox.warning(self, "SQL Error", "Please enter a SQL query")
            return
        
        self.start_query("custom", query=query)
    
    # Script methods
    def tokenize_script(self):
        """Tokenize the current script."""
        script = self.script_editor.toPlainText()
        
        try:
            lexer = QuadScriptLexer(script)
            tokens = lexer.tokenize()
            
            output = f"🔤 Tokenization Results ({len(tokens)} tokens):\n\n"
            for i, token in enumerate(tokens, 1):
                output += f"{i:3d}. {token.type.name:20} : '{token.value}'\n"
            
            self.script_output.setPlainText(output)
            
        except Exception as e:
            self.script_output.setPlainText(f"❌ Tokenization Error:\n{str(e)}")
    
    def execute_script(self):
        """Execute the current script."""
        script = self.script_editor.toPlainText()
        
        try:
            # For now, just show that we would execute the script
            # Full execution would require a complete parser
            output = f"🚀 Script Execution:\n\n"
            output += f"Script Length: {len(script)} characters\n"
            output += f"Lines: {len(script.splitlines())}\n\n"
            output += "Note: Full script execution requires parser implementation.\n"
            output += "Currently showing tokenization and variable access capabilities.\n\n"
            
            # Show available variables
            if self.interpreter:
                quadset_vars = [var for var in self.interpreter.variables.keys() if var.startswith('quadset_')]
                output += f"Available quadset variables: {len(quadset_vars)}\n"
                output += f"First 5: {quadset_vars[:5]}\n"
            
            self.script_output.setPlainText(output)
            
        except Exception as e:
            self.script_output.setPlainText(f"❌ Execution Error:\n{str(e)}")
    
    def clear_script(self):
        """Clear the script editor."""
        self.script_editor.clear()
        self.script_output.clear()
    
    # Analysis methods
    def analyze_all_even(self):
        """Analyze quadsets with all even members."""
        self.run_analysis("All Even Members", """
            SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
            FROM quadsets 
            WHERE base_number % 2 = 0 
              AND conrune % 2 = 0 
              AND reversal % 2 = 0 
              AND reversal_conrune % 2 = 0
            LIMIT 20
        """)
    
    def analyze_all_odd(self):
        """Analyze quadsets with all odd members."""
        self.run_analysis("All Odd Members", """
            SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
            FROM quadsets 
            WHERE base_number % 2 = 1 
              AND conrune % 2 = 1 
              AND reversal % 2 = 1 
              AND reversal_conrune % 2 = 1
            LIMIT 20
        """)
    
    def analyze_prime_bases(self):
        """Analyze quadsets with prime base numbers."""
        # Simple prime check for demonstration
        self.run_analysis("Prime Base Numbers", """
            SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum
            FROM quadsets 
            WHERE base_number IN (2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97)
            LIMIT 20
        """)
    
    def analyze_perfect_square_sums(self):
        """Analyze quadsets with perfect square sums."""
        self.run_analysis("Perfect Square Sums", """
            SELECT base_number, conrune, reversal, reversal_conrune, quadset_sum,
                   CAST(SQRT(quadset_sum) AS INTEGER) as sqrt_sum
            FROM quadsets 
            WHERE CAST(SQRT(quadset_sum) AS INTEGER) * CAST(SQRT(quadset_sum) AS INTEGER) = quadset_sum
            LIMIT 20
        """)
    
    def analyze_fibonacci_transitions(self):
        """Analyze quadsets with Fibonacci-like transitions."""
        fibonacci_numbers = [1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987, 1597, 2584, 4181, 6765]
        fibonacci_str = ",".join(map(str, fibonacci_numbers))
        
        self.run_analysis("Fibonacci Transitions", f"""
            SELECT base_number, transition_result_decimal, quadset_sum
            FROM quadsets 
            WHERE transition_result_decimal IN ({fibonacci_str})
            LIMIT 20
        """)
    
    def analyze_large_differences(self):
        """Analyze quadsets with large differences."""
        self.run_analysis("Large Differences", """
            SELECT base_number, base_conrune_diff, reversal_reversal_conrune_diff, 
                   transition_result_decimal, quadset_sum
            FROM quadsets 
            WHERE base_conrune_diff > 10000 OR reversal_reversal_conrune_diff > 10000
            ORDER BY (base_conrune_diff + reversal_reversal_conrune_diff) DESC
            LIMIT 20
        """)
    
    def run_analysis(self, title: str, query: str):
        """Run an analysis query and display results."""
        if not self.interpreter or not self.interpreter.database_path:
            self.analysis_output.setPlainText("❌ No database connection available")
            return
        
        try:
            results = self.interpreter.execute_database_query(query)
            
            output = f"🎯 {title} Analysis\n"
            output += "=" * 50 + "\n\n"
            
            if not results:
                output += "No results found.\n"
            else:
                output += f"Found {len(results)} results:\n\n"
                
                # Display results in a formatted way
                for i, result in enumerate(results, 1):
                    output += f"{i:2d}. "
                    for key, value in result.items():
                        output += f"{key}: {value}, "
                    output = output.rstrip(", ") + "\n"
            
            self.analysis_output.setPlainText(output)
            
        except Exception as e:
            self.analysis_output.setPlainText(f"❌ Analysis Error:\n{str(e)}")


if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    panel = QuadScriptPanel()
    panel.show()
    sys.exit(app.exec()) 