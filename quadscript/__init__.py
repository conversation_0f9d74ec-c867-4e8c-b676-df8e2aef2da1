"""
@file quadscript/__init__.py
@description QuadScript package initialization
<AUTHOR> Assistant
@created 2024-01-20
@lastModified 2024-01-20
@dependencies None
"""

from .lexer import QuadScriptLexer, TokenType, Token
from .ast_nodes import *
from .interpreter import QuadScriptInterpreter, Quadset, QuadsetCollection, MathUtils

__version__ = "1.0.0"
__author__ = "AI Assistant"
__description__ = "QuadScript: A domain-specific language for quadset analysis"

__all__ = [
    # Lexer
    "QuadScriptLexer",
    "TokenType", 
    "Token",
    
    # AST Nodes
    "ASTNode",
    "Expression",
    "Statement",
    "Program",
    "LiteralExpression",
    "IdentifierExpression", 
    "BinaryExpression",
    "UnaryExpression",
    "FunctionCallExpression",
    "ArrayExpression",
    "RangeExpression",
    "PropertyAccessExpression",
    "AssignmentStatement",
    "PrintStatement",
    "IfStatement",
    "ForStatement",
    "WhileStatement",
    "ReturnStatement",
    "ExpressionStatement",
    "FindStatement",
    "SortStatement",
    "LimitStatement",
    "GroupStatement",
    "StatsStatement",
    "SaveStatement",
    "LoadStatement",
    "ExportStatement",
    "ImportStatement",
    "ChartStatement",
    "FunctionDefinition",
    "ProcedureDefinition",
    "CallStatement",
    "ASTVisitor",
    
    # Interpreter
    "QuadScriptInterpreter",
    "Quadset",
    "QuadsetCollection",
    "MathUtils",
] 