#!/usr/bin/env python3
"""
Simple script to check database contents and tuple issues.
"""

import sqlite3
import os

def check_database():
    """Check the database for calculation records."""
    db_paths = [
        '/home/<USER>/.isopgem/data/isopgem.db',
        '/home/<USER>/Desktop/IsopGem/data/isopgem.db',
        '/home/<USER>/Desktop/IsopGem/calculations.db'
    ]
    
    for db_path in db_paths:
        if os.path.exists(db_path):
            print(f"Found database: {db_path}")
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if calculations table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='calculations';")
                if cursor.fetchone():
                    print("Found calculations table")
                    
                    # Get recent calculations
                    cursor.execute("SELECT id, input_text, custom_method_name FROM calculations ORDER BY timestamp DESC LIMIT 5;")
                    rows = cursor.fetchall()
                    
                    print(f"Found {len(rows)} calculations:")
                    for row in rows:
                        print(f"  ID: {row[0]}, Input: {row[1]}, Custom Method: {repr(row[2])}")
                else:
                    print("No calculations table found")
                    
                conn.close()
            except Exception as e:
                print(f"Error accessing database: {e}")
        else:
            print(f"Database not found: {db_path}")

if __name__ == "__main__":
    check_database()
