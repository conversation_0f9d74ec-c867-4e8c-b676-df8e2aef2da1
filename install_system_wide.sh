#!/bin/bash

# IsopGem System-Wide Installation Script
# This script installs IsopGem to /usr/local/bin for system-wide access

set -e

echo "🌍 Installing IsopGem System-Wide..."
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if executable exists
if [ ! -f "dist/IsopGem/IsopGem" ]; then
    print_error "IsopGem executable not found at dist/IsopGem/IsopGem"
    print_error "Please build the executable first using: ./build_executable.sh"
    exit 1
fi

# Check if running as root or with sudo
if [ "$EUID" -ne 0 ]; then
    print_error "This script requires root privileges."
    print_error "Please run with sudo: sudo ./install_system_wide.sh"
    exit 1
fi

print_status "Installing IsopGem to /usr/local/..."

# Create installation directory
mkdir -p /usr/local/lib/IsopGem

# Copy the entire distribution
cp -r dist/IsopGem/* /usr/local/lib/IsopGem/

# Create a launcher script in /usr/local/bin
cat > /usr/local/bin/IsopGem << 'EOF'
#!/bin/bash
# IsopGem system launcher
exec /usr/local/lib/IsopGem/IsopGem "$@"
EOF

# Make the launcher executable
chmod +x /usr/local/bin/IsopGem

print_success "IsopGem installed to /usr/local/lib/IsopGem/"
print_success "Launcher created at /usr/local/bin/IsopGem"

# Create a system-wide desktop entry
SYSTEM_DESKTOP_DIR="/usr/share/applications"
SYSTEM_DESKTOP_FILE="$SYSTEM_DESKTOP_DIR/isopgem.desktop"

if [ -d "$SYSTEM_DESKTOP_DIR" ]; then
    print_status "Creating system-wide desktop entry..."
    
    CURRENT_DIR=$(pwd)
    
    cat > "$SYSTEM_DESKTOP_FILE" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=IsopGem
Comment=Sacred Geometry & Gematria Tool
Exec=/usr/local/bin/IsopGem
Icon=$CURRENT_DIR/resources/images/icon.png
Terminal=false
StartupNotify=true
Categories=Education;Science;Math;
Keywords=geometry;gematria;sacred;mathematics;astrology;
StartupWMClass=IsopGem
EOF

    chmod 644 "$SYSTEM_DESKTOP_FILE"
    print_success "System-wide desktop entry created at $SYSTEM_DESKTOP_FILE"
    
    # Update desktop database if available
    if command -v update-desktop-database &> /dev/null; then
        print_status "Updating system desktop database..."
        update-desktop-database /usr/share/applications
        print_success "System desktop database updated!"
    fi
else
    print_warning "System applications directory not found, skipping system desktop entry"
fi

echo ""
echo "===================================="
print_success "System-Wide Installation Complete! 🎉"
echo ""
echo "IsopGem is now installed system-wide and can be run from anywhere with:"
echo "  IsopGem"
echo ""
echo "The application should also appear in your system application menu."
echo ""
echo "To uninstall:"
echo "  sudo rm -rf /usr/local/lib/IsopGem"
echo "  sudo rm /usr/local/bin/IsopGem"
echo "  sudo rm /usr/share/applications/isopgem.desktop"
echo "===================================="