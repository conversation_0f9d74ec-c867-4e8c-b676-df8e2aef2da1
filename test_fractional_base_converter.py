#!/usr/bin/env python3
"""
Test script for the enhanced fractional number base converter functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geometry.calculator.advanced_scientific_calculator import AdvancedScientificCalculator

def test_fractional_conversions():
    """Test fractional base conversions."""
    calc = AdvancedScientificCalculator()
    
    print("=== Fractional Number Base Converter Test ===\n")
    
    # Test cases: (number, from_base, to_base, description)
    test_cases = [
        # Basic fractional conversions
        ("12.75", 10, 2, "12.75 decimal to binary"),
        ("12.5", 10, 2, "12.5 decimal to binary"),
        ("10.25", 10, 2, "10.25 decimal to binary"),
        ("3.14159", 10, 16, "Pi approximation to hex"),
        
        # Binary fractional to decimal
        ("1100.11", 2, 10, "1100.11 binary to decimal"),
        ("101.101", 2, 10, "101.101 binary to decimal"),
        ("1010.1010", 2, 10, "1010.1010 binary to decimal"),
        
        # Hex fractional to decimal
        ("FF.8", 16, 10, "FF.8 hex to decimal"),
        ("A.4", 16, 10, "A.4 hex to decimal"),
        ("10.F", 16, 10, "10.F hex to decimal"),
        
        # Decimal to various bases
        ("5.625", 10, 8, "5.625 decimal to octal"),
        ("7.5", 10, 8, "7.5 decimal to octal"),
        ("15.75", 10, 16, "15.75 decimal to hex"),
        
        # Cross-base conversions (not through decimal)
        ("10.8", 16, 2, "10.8 hex to binary"),
        ("77.4", 8, 16, "77.4 octal to hex"),
        
        # Edge cases
        ("0.5", 10, 2, "0.5 decimal to binary"),
        ("0.25", 10, 2, "0.25 decimal to binary"),
        ("0.125", 10, 2, "0.125 decimal to binary"),
        ("0.1", 10, 2, "0.1 decimal to binary (repeating)"),
        
        # Negative fractional numbers
        ("-12.75", 10, 2, "Negative fractional to binary"),
        ("-FF.8", 16, 10, "Negative hex fractional to decimal"),
        
        # Integer part only (should still work)
        ("42.", 10, 2, "Integer with decimal point"),
        (".75", 10, 2, "Fractional part only"),
        
        # Higher bases
        ("10.5", 36, 10, "Base 36 fractional to decimal"),
        ("Z.Z", 36, 10, "Z.Z base 36 to decimal"),
    ]
    
    passed = 0
    failed = 0
    
    for number, from_base, to_base, description in test_cases:
        try:
            result = calc.convert_base(number, from_base, to_base)
            error = calc.get_error()
            
            if error:
                print(f"✗ {description}: {number} (base {from_base}) → Error: {error}")
                failed += 1
                calc.clear_error()
            else:
                print(f"✓ {description}: {number} (base {from_base}) → {result} (base {to_base})")
                passed += 1
                
        except Exception as e:
            print(f"✗ {description}: {number} (base {from_base}) → Exception: {str(e)}")
            failed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    return failed == 0

def test_specific_examples():
    """Test specific well-known fractional conversions."""
    calc = AdvancedScientificCalculator()
    
    print("\n=== Specific Example Verification ===\n")
    
    # Well-known conversions with expected results
    known_cases = [
        ("12.75", 10, 2, "1100.11"),  # 12 + 0.75 = 1100 + 0.11
        ("5.625", 10, 2, "101.101"),  # 5 + 0.625 = 101 + 0.101
        ("255.5", 10, 16, "FF.8"),    # 255 + 0.5 = FF + 0.8
        ("10.25", 10, 8, "12.2"),     # 10 + 0.25 = 12 + 0.2
    ]
    
    for number, from_base, to_base, expected in known_cases:
        result = calc.convert_base(number, from_base, to_base)
        error = calc.get_error()
        
        if error:
            print(f"✗ {number} (base {from_base}) → Error: {error}")
            calc.clear_error()
        elif result == expected:
            print(f"✓ {number} (base {from_base}) → {result} (base {to_base}) [EXACT MATCH]")
        else:
            print(f"~ {number} (base {from_base}) → {result} (base {to_base}) [Expected: {expected}]")
            print(f"  Note: Fractional conversions may have precision differences")

def demonstrate_fractional_features():
    """Demonstrate the fractional conversion capabilities."""
    calc = AdvancedScientificCalculator()
    
    print("\n=== Fractional Conversion Demonstration ===\n")
    
    # Show how decimal fractions convert to binary
    print("Decimal fractions to binary:")
    print("-" * 30)
    decimal_fractions = ["0.5", "0.25", "0.125", "0.75", "0.375", "0.1", "0.3"]
    
    for frac in decimal_fractions:
        binary_result = calc.convert_base(frac, 10, 2)
        print(f"{frac:>6} → {binary_result}")
    
    # Show how binary fractions convert to decimal
    print(f"\nBinary fractions to decimal:")
    print("-" * 30)
    binary_fractions = ["0.1", "0.01", "0.11", "0.101", "0.111", "0.1010"]
    
    for frac in binary_fractions:
        decimal_result = calc.convert_base(frac, 2, 10)
        print(f"{frac:>8} → {decimal_result}")
    
    # Show hexadecimal fractional conversions
    print(f"\nHexadecimal fractions to decimal:")
    print("-" * 35)
    hex_fractions = ["A.8", "F.F", "10.4", "C.C", "1.A"]
    
    for frac in hex_fractions:
        decimal_result = calc.convert_base(frac, 16, 10)
        print(f"{frac:>6} → {decimal_result}")

def test_precision_and_edge_cases():
    """Test precision handling and edge cases."""
    calc = AdvancedScientificCalculator()
    
    print("\n=== Precision and Edge Cases ===\n")
    
    # Test repeating decimals
    print("Repeating decimal conversions (limited precision):")
    print("-" * 50)
    
    repeating_cases = [
        ("0.1", 10, 2, "1/10 in binary (repeating)"),
        ("0.3", 10, 2, "3/10 in binary (repeating)"),
        ("0.1", 10, 3, "1/10 in base 3"),
        ("1.0", 3, 10, "1.0 base 3 to decimal"),
    ]
    
    for number, from_base, to_base, description in repeating_cases:
        result = calc.convert_base(number, from_base, to_base)
        print(f"{description}: {number} (base {from_base}) → {result} (base {to_base})")
    
    # Test very small fractions
    print(f"\nVery small fractions:")
    print("-" * 25)
    small_fractions = ["0.001", "0.0001", "0.00001"]
    
    for frac in small_fractions:
        binary_result = calc.convert_base(frac, 10, 2)
        print(f"{frac} → {binary_result} (binary)")

if __name__ == "__main__":
    print("Testing Enhanced Fractional Number Base Converter")
    print("=" * 55)
    
    # Run all tests
    success = test_fractional_conversions()
    test_specific_examples()
    demonstrate_fractional_features()
    test_precision_and_edge_cases()
    
    print("\n" + "=" * 55)
    if success:
        print("✅ Fractional base converter is working!")
        print("🎉 Now supports decimal points in base conversions!")
    else:
        print("❌ Some tests failed - check implementation")
    
    print("\nKey Features:")
    print("• Supports fractional numbers in any base (2-36)")
    print("• Handles decimal points properly")
    print("• Maintains precision up to 20 decimal places")
    print("• Works with negative fractional numbers")
    print("• Validates input for each base correctly")
