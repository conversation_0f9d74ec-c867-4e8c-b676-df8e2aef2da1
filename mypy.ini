[mypy]
python_version = 3.11
warn_return_any = True
warn_unused_configs = True
# Temporarily relaxed for initial type fixing phase
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
strict_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
# Module resolution fixes
explicit_package_bases = True
namespace_packages = True
mypy_path = .
# Exclude venv, test files, and UI components
exclude = (^venv/|^tests/|test_.*.py$|astrology/ui/.*|gematria/ui/.*|geometry/ui/.*|document_manager/ui/.*|shared/ui/.*)

# Explicitly ignore all UI-related files
[mypy.ui.*]
ignore_errors = True

[mypy.*.ui.*]
ignore_errors = True

# Type Fixing Roadmap (To be uncommented as the codebase improves)
# Phase 1: Current - Fix critical errors (type conflicts, attribute errors)
# Phase 2: disallow_untyped_defs = True (for core modules like services)
# Phase 3: disallow_incomplete_defs = True (for all modules)
# Phase 4: disallow_untyped_decorators = True (fully typed codebase)

# Core business logic - stricter typing
[mypy.document_manager.models.*]
disallow_untyped_defs = True
disallow_any_expr = True

[mypy.document_manager.repositories.*]
disallow_untyped_defs = True
disallow_any_expr = True

[mypy.document_manager.services.*]
disallow_untyped_defs = True
disallow_any_expr = True

[mypy.gematria.models.*]
disallow_untyped_defs = True
disallow_any_expr = True

[mypy.gematria.services.*]
disallow_untyped_defs = True
disallow_any_expr = True

# Completely ignore errors in UI modules
[mypy.gematria.ui.*]
ignore_errors = True

[mypy.document_manager.ui.*]
ignore_errors = True

[mypy.tq.ui.*]
ignore_errors = True

[mypy.geometry.ui.*]
ignore_errors = True

[mypy.astrology.ui.*]
ignore_errors = True

[mypy.shared.ui.*]
ignore_errors = True

# Ignore errors in specific component types
[mypy.*.panels.*]
ignore_errors = True

[mypy.*.dialogs.*]
ignore_errors = True

[mypy.*.widgets.*]
ignore_errors = True

[mypy.*.tabs.*]
ignore_errors = True

# PyQt6 related overrides
[mypy-PyQt6.*]
ignore_missing_imports = True

[mypy.plugins.PyQt6.*]
ignore_missing_imports = True

# Add exceptions for third-party libraries without stubs
[mypy-tqdm.*]
ignore_missing_imports = True

[mypy-loguru.*]
ignore_missing_imports = True

[mypy-docutils.*]
ignore_missing_imports = True

[mypy-lxml.*]
ignore_missing_imports = True

[mypy-setup]
ignore_errors = True

[mypy-tests.*]
ignore_errors = True

# Disable unreachable check for specific files
[mypy-gematria.ui.panels.search_panel]
disable_error_code = unreachable

[mypy-document_manager.services.document_service]
disable_error_code = unreachable

[mypy-fitz.*]
ignore_missing_imports = True

[mypy-odf.*]
ignore_missing_imports = True

[mypy-odf.opendocument.*]
ignore_missing_imports = True

[mypy-document_manager.ui.dialogs.category_manager_dialog]
disable_error_code = arg-type

[mypy-document_manager.ui.dialogs.document_viewer_dialog]
disable_error_code = arg-type
