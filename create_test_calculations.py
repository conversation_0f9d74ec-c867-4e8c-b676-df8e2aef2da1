#!/usr/bin/env python3
"""
Create a test calculation with a problematic tuple method name.
This helps to verify that our fix works correctly.
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

logging.basicConfig(level=logging.DEBUG, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Import required modules
from gematria.models.calculation_result import CalculationResult
from gematria.services.calculation_database_service import CalculationDatabaseService
from shared.services.service_locator import ServiceLocator
from gematria.ui.dialogs.calculation_details_dialog import CalculationDetailsDialog

# Initialize services
def initialize_services():
    """Initialize required services."""
    try:
        # Set up calculation database service
        db_service = CalculationDatabaseService()
        ServiceLocator.register(CalculationDatabaseService, db_service)
        logger.info("Services initialized successfully")
        return db_service
    except Exception as e:
        logger.error(f"Failed to initialize services: {str(e)}")
        return None

def create_test_calculations(db_service):
    """Create test calculations with problematic method names."""
    test_calculations = []
    
    # Case 1: Simple tuple
    calc1 = CalculationResult(
        input_text="Test with tuple method name",
        calculation_type="CUSTOM_CIPHER",
        result_value=123,
        timestamp=datetime.now(),
        custom_method_name=("Tuple Method Name", "Extra Data")
    )
    test_calculations.append(calc1)
    
    # Case 2: Tuple with Custom: prefix
    calc2 = CalculationResult(
        input_text="Test with Custom: prefix",
        calculation_type="CUSTOM_CIPHER",
        result_value=456,
        timestamp=datetime.now(),
        custom_method_name="Custom: ('Tuple Inside String', 'Extra Data')"
    )
    test_calculations.append(calc2)
    
    # Case 3: Nested tuple
    calc3 = CalculationResult(
        input_text="Test with nested tuple",
        calculation_type="CUSTOM_CIPHER",
        result_value=789,
        timestamp=datetime.now(),
        custom_method_name=(("Nested", "Tuple"), "Extra Data")
    )
    test_calculations.append(calc3)
    
    # Save the calculations
    for calc in test_calculations:
        try:
            success = db_service.save_calculation(calc)
            if success:
                logger.info(f"Saved calculation with ID {calc.id} and method name {calc.custom_method_name!r}")
            else:
                logger.error(f"Failed to save calculation with method name {calc.custom_method_name!r}")
        except Exception as e:
            logger.error(f"Error saving calculation: {str(e)}")
    
    return test_calculations

def main():
    """Main function."""
    print("Creating test calculations with problematic tuple method names")
    print("=" * 60)
    
    # Initialize services
    db_service = initialize_services()
    if not db_service:
        print("Failed to initialize services. Exiting.")
        return
    
    # Create test calculations
    test_calculations = create_test_calculations(db_service)
    
    print("\nTest calculations created successfully!")
    print(f"Created {len(test_calculations)} test calculations")
    print("\nInstructions:")
    print("1. Run the main application")
    print("2. Go to the Calculation History panel")
    print("3. Find and open the test calculations created just now")
    print("4. Verify that method names display properly without tuple syntax")

if __name__ == "__main__":
    main()
