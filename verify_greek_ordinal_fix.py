#!/usr/bin/env python3
"""
Final verification script specifically for the Greek Ordinal Value tuple issue.
This script tests our fixes to both the calculation_details_dialog.py and 
calculation_result.py files to ensure they properly extract the method name.
"""

import os
import sys
import logging
from enum import Enum

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

# Import the actual fixed class implementations
from gematria.models.calculation_result import CalculationResult
from gematria.ui.dialogs.calculation_details_dialog import CalculationDetailsDialog
from PyQt6.QtWidgets import QApplication, QDialog

class Language(Enum):
    """Mock Language enum to simulate the real one"""
    GREEK = 'Greek'

def test_calculation_result():
    """Test the to_display_dict method in CalculationResult"""
    print("\nTesting CalculationResult.to_display_dict()")
    print("-" * 60)
    
    # Create the problematic tuple from screenshot
    problematic_tuple = ('Greek Ordinal Value', 
                         'Greek: Ordinal Value. Each letter numbered by position in alphabet.',
                         Language.GREEK)
    
    # Create string representation with Custom: prefix
    problematic_string = f"Custom: {str(problematic_tuple)}"
    
    # Create both test calculations
    calc1 = CalculationResult(
        input_text="Test Direct Tuple",
        result_value=123,
        date_created=None,
        custom_method_name=problematic_tuple
    )
    
    calc2 = CalculationResult(
        input_text="Test String Representation",
        result_value=456,
        date_created=None,
        custom_method_name=problematic_string
    )
    
    # Test to_display_dict for each
    display_dict1 = calc1.to_display_dict()
    display_dict2 = calc2.to_display_dict()
    
    # Check results
    print(f"Test 1 (Direct Tuple): {display_dict1.get('Method', 'Not found')}")
    has_tuple_syntax1 = "(" in display_dict1.get('Method', '') and "," in display_dict1.get('Method', '')
    if has_tuple_syntax1:
        print("❌ FAILED: Result still contains tuple syntax")
    else:
        print("✅ PASSED: Clean result")
    
    print(f"\nTest 2 (String with Custom: prefix): {display_dict2.get('Method', 'Not found')}")
    has_tuple_syntax2 = "(" in display_dict2.get('Method', '') and "," in display_dict2.get('Method', '')
    if has_tuple_syntax2:
        print("❌ FAILED: Result still contains tuple syntax")
    else:
        print("✅ PASSED: Clean result")

def test_calculation_details_dialog():
    """Test the _get_method_name method in CalculationDetailsDialog"""
    # This requires a QApplication instance
    app = QApplication(sys.argv)
    
    print("\nTesting CalculationDetailsDialog._get_method_name()")
    print("-" * 60)
    
    # Create the problematic tuple from screenshot
    problematic_tuple = ('Greek Ordinal Value', 
                         'Greek: Ordinal Value. Each letter numbered by position in alphabet.',
                         Language.GREEK)
    
    # Create string representation with Custom: prefix
    problematic_string = f"Custom: {str(problematic_tuple)}"
    
    # Create both test calculations
    calc1 = CalculationResult(
        input_text="Test Direct Tuple",
        result_value=123,
        date_created=None,
        custom_method_name=problematic_tuple
    )
    
    calc2 = CalculationResult(
        input_text="Test String Representation",
        result_value=456,
        date_created=None,
        custom_method_name=problematic_string
    )
    
    # Create dialog instances and test method name extraction
    dialog1 = CalculationDetailsDialog(calc1)
    dialog2 = CalculationDetailsDialog(calc2)
    
    result1 = dialog1._get_method_name()
    result2 = dialog2._get_method_name()
    
    # Check results
    print(f"Test 1 (Direct Tuple): {result1}")
    has_tuple_syntax1 = "(" in result1 and "," in result1
    if has_tuple_syntax1:
        print("❌ FAILED: Result still contains tuple syntax")
    else:
        print("✅ PASSED: Clean result")
    
    print(f"\nTest 2 (String with Custom: prefix): {result2}")
    has_tuple_syntax2 = "(" in result2 and "," in result2
    if has_tuple_syntax2:
        print("❌ FAILED: Result still contains tuple syntax")
    else:
        print("✅ PASSED: Clean result")

def main():
    """Test both implementations"""
    print("TESTING GREEK ORDINAL VALUE TUPLE FIX")
    print("=" * 60)
    
    # Test CalculationResult.to_display_dict() first
    test_calculation_result()
    
    # Then test CalculationDetailsDialog._get_method_name()
    try:
        test_calculation_details_dialog()
    except Exception as e:
        print(f"\nError testing CalculationDetailsDialog: {str(e)}")
        print("This is expected if running in a non-GUI environment.")

if __name__ == "__main__":
    main()
