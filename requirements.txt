# IsopGem Requirements
# Main requirements file for easy installation

# Core dependencies
PyQt6==6.6.1
PyQt6-Qt6==6.6.1  # Qt6 binaries
PyQt6-sip==13.6.0  # SIP bindings
PyQt6-tools>=6.4.0  # Designer and other tools
PyQt6-Charts>=6.6.0  # For chart visualization
PyQt6-DataVisualization>=6.6.0  # For 3D visualization
PyQt6-WebEngine>=6.6.0  # For web content rendering and HTML support
pyyaml==6.0.1
pydantic==2.6.1
python-dotenv==1.0.1
loguru==0.7.2

# Data handling
numpy==1.26.3
pandas==2.2.0
openpyxl>=3.1.2  # For Excel file support
xlrd>=2.0.1  # For older Excel file formats
tabulate>=0.9.0  # For formatted table output
csvkit>=1.1.1  # CSV data processing utilities

# Database
sqlite3worker>=1.1.0
whoosh>=2.7.4  # For searching and indexing
sqlalchemy>=2.0.0  # SQL toolkit and ORM (optional based on repo structure)

# Document processing
PyMuPDF>=1.21.0  # For PDF processing
python-docx>=0.8.11  # For DOCX processing
odfpy>=1.4.1  # For LibreOffice documents
chardet>=5.2.0  # For character encoding detection
beautifulsoup4>=4.12.0  # For HTML parsing
lxml>=4.9.3  # XML and HTML processing

# Astronomy/Astrology
pyswisseph>=2.10.0  # Swiss Ephemeris for astronomical calculations
skyfield>=1.45.0  # Astronomical calculations
ephem>=4.1.4  # PyEphem for accurate astronomical calculations
kerykeion>=3.3.0  # Astrological calculations and chart generation
tzlocal>=5.2.0  # Timezone detection
pytz>=2023.4  # Timezone handling

# Mathematics and Visualization
sympy>=1.12.0  # Symbolic mathematics for number properties
matplotlib>=3.8.0  # For plotting and visualization
networkx>=3.1  # Graph operations and visualization

# 3D Geometry and Visualization
# Note: based on geometry tasks documentation, the following may be needed
pyqtgraph>=0.13.3  # Fast data visualization
vtk>=9.2.6  # 3D visualization toolkit
plyfile>=1.0.0  # For 3D file format support
trimesh>=4.0.0  # For working with triangular meshes

# Utilities
tqdm==4.66.1
pillow==10.2.0
typing-extensions>=4.8.0  # Extended typing support
dill>=0.3.7  # For object serialization
requests>=2.31.0  # For HTTP requests
pyinstaller>=6.0.0  # For creating standalone executables

# Include development tools
-r requirements/dev.txt

# Include testing tools
-r requirements/test.txt

# Optional: Natural language processing support
# nltk>=3.8.1
# spacy>=3.5.0
# transformers>=4.34.0  # For advanced NLP capabilities
