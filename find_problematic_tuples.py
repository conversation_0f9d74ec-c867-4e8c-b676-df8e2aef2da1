#!/usr/bin/env python3
"""
Script to find calculations with problematic tuple format method names.
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from gematria.services.calculation_database_service import CalculationDatabaseService
from shared.services.service_locator import ServiceLocator

def main():
    """Find calculations with problematic tuple method names"""
    logger.info("Initializing services...")
    calculation_service = CalculationDatabaseService()
    
    # Get all calculations
    logger.info("Fetching all calculations...")
    all_calculations = calculation_service.get_all_calculations()
    logger.info(f"Found {len(all_calculations)} calculations in total")
    
    # Find calculations with tuple-like custom method names
    logger.info("Searching for problematic method names...")
    problematic = []
    
    for calc in all_calculations:
        # Check for tuple-like custom method names
        if hasattr(calc, "custom_method_name") and calc.custom_method_name:
            if isinstance(calc.custom_method_name, str):
                if "Custom: (" in calc.custom_method_name and "," in calc.custom_method_name:
                    problematic.append({
                        "id": calc.id,
                        "input": calc.input_text,
                        "method": calc.custom_method_name,
                        "type": "string with Custom: prefix and tuple"
                    })
                elif calc.custom_method_name.startswith("(") and "," in calc.custom_method_name:
                    problematic.append({
                        "id": calc.id,
                        "input": calc.input_text,
                        "method": calc.custom_method_name,
                        "type": "string with tuple format"
                    })
            elif isinstance(calc.custom_method_name, tuple):
                problematic.append({
                    "id": calc.id,
                    "input": calc.input_text,
                    "method": str(calc.custom_method_name),
                    "type": "actual tuple"
                })
    
    # Display results
    logger.info(f"Found {len(problematic)} calculations with problematic method names")
    
    if problematic:
        print("\nProblematic calculations:")
        print("-" * 80)
        
        for i, item in enumerate(problematic):
            print(f"{i+1}. ID: {item['id']}")
            print(f"   Input: {item['input'][:50]}")
            print(f"   Method: {item['method']}")
            print(f"   Type: {item['type']}")
            print("-" * 80)
    else:
        print("No problematic calculations found!")

if __name__ == "__main__":
    main()
