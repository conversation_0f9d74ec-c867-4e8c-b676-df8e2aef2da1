"""
Ancient Measures Database for IsopGem Scientific Calculator.

This module contains comprehensive data for converting ancient measurement units
from various civilizations to modern metric equivalents.

Author: IsopGem Development Team
Created: 2024-01-XX
Last Modified: 2024-01-XX
Dependencies: None (pure data module)
"""

from typing import Dict, List, Tuple
import math

# Ancient measurement units organized by civilization and type
ANCIENT_MEASURES = {
    # EGYPTIAN MEASURES
    "egyptian": {
        "length": {
            # Based on the royal cubit (≈ 52.5 cm)
            "digit": 0.01875,  # 1/28 of royal cubit ≈ 1.875 cm
            "palm": 0.075,     # 4 digits ≈ 7.5 cm
            "hand": 0.1,       # ≈ 10 cm
            "span": 0.225,     # 3 palms ≈ 22.5 cm
            "foot": 0.3,       # ≈ 30 cm
            "cubit": 0.45,     # common cubit ≈ 45 cm
            "royal_cubit": 0.525,  # ≈ 52.5 cm
            "rod": 2.625,      # 5 royal cubits ≈ 2.625 m
            "khet": 52.5,      # 100 royal cubits ≈ 52.5 m
            "iter": 10500,     # 20,000 royal cubits ≈ 10.5 km
        },
        "area": {
            "setat": 2756.25,  # 1 square khet ≈ 2756.25 m²
            "aroura": 2756.25, # Same as setat
        },
        "volume": {
            "hin": 0.48,       # ≈ 0.48 liters
            "hekat": 4.8,      # 10 hin ≈ 4.8 liters
            "khar": 76.8,      # 16 hekat ≈ 76.8 liters
        },
        "weight": {
            "kite": 0.0091,    # ≈ 9.1 grams
            "deben": 0.091,    # 10 kite ≈ 91 grams
            "sep": 0.758,      # ≈ 758 grams
        }
    },
    
    # MESOPOTAMIAN/BABYLONIAN MEASURES
    "babylonian": {
        "length": {
            "barleycorn": 0.00278,  # ≈ 2.78 mm
            "finger": 0.0167,       # 6 barleycorns ≈ 1.67 cm
            "palm": 0.0833,         # 5 fingers ≈ 8.33 cm
            "foot": 0.333,          # 4 palms ≈ 33.3 cm
            "cubit": 0.5,           # 1.5 feet ≈ 50 cm
            "reed": 3.0,            # 6 cubits ≈ 3 m
            "gar": 3.6,             # 12 cubits ≈ 3.6 m
            "stadion": 180,         # 60 gar ≈ 180 m
            "beru": 10800,          # 60 stadion ≈ 10.8 km
        },
        "area": {
            "sar": 36,              # 1 square gar ≈ 36 m²
            "iku": 3600,            # 100 sar ≈ 3600 m²
            "bur": 216000,          # 60 iku ≈ 21.6 hectares
        },
        "volume": {
            "qa": 1.0,              # ≈ 1 liter
            "ban": 10.0,            # 10 qa ≈ 10 liters
            "bariga": 60.0,         # 6 ban ≈ 60 liters
            "gur": 300.0,           # 5 bariga ≈ 300 liters
        },
        "weight": {
            "grain": 0.000046,      # ≈ 0.046 grams
            "shekel": 8.33,         # ≈ 8.33 grams
            "mina": 500,            # 60 shekel ≈ 500 grams
            "talent": 30000,        # 60 mina ≈ 30 kg
        }
    },
    
    # GREEK MEASURES
    "greek": {
        "length": {
            "daktylos": 0.0185,     # finger ≈ 1.85 cm
            "kondylos": 0.037,      # knuckle, 2 daktylos ≈ 3.7 cm
            "palaiste": 0.074,      # palm, 4 daktylos ≈ 7.4 cm
            "dichas": 0.148,        # 2 palaiste ≈ 14.8 cm
            "spithame": 0.222,      # span, 3 palaiste ≈ 22.2 cm
            "pous": 0.296,          # foot, 4 palaiste ≈ 29.6 cm
            "pygme": 0.333,         # fist ≈ 33.3 cm
            "pechys": 0.444,        # cubit, 1.5 pous ≈ 44.4 cm
            "orgyia": 1.776,        # fathom, 6 pous ≈ 1.776 m
            "kalamos": 1.776,       # rod, same as orgyia
            "stadion": 177.6,       # 600 pous ≈ 177.6 m
            "plethron": 29.6,       # 100 pous ≈ 29.6 m
            "milion": 1480,         # Roman mile adopted ≈ 1.48 km
        },
        "area": {
            "plethron_sq": 876.16,  # 1 square plethron ≈ 876.16 m²
            "medimnos": 5265,       # ≈ 5265 m² (land measure)
        },
        "volume": {
            "kyathos": 0.045,       # ≈ 45 ml
            "kotyle": 0.27,         # 6 kyathos ≈ 270 ml
            "xestes": 0.54,         # 2 kotyle ≈ 540 ml
            "chous": 3.24,          # 6 xestes ≈ 3.24 liters
            "metretes": 38.88,      # 12 chous ≈ 38.88 liters
            "medimnos": 51.84,      # dry measure ≈ 51.84 liters
        },
        "weight": {
            "obolos": 0.72,         # ≈ 0.72 grams
            "drachme": 4.32,        # 6 obolos ≈ 4.32 grams
            "mna": 432,             # 100 drachme ≈ 432 grams
            "talanton": 25920,      # 60 mna ≈ 25.92 kg
        }
    },
    
    # ROMAN MEASURES
    "roman": {
        "length": {
            "uncia": 0.0246,        # inch ≈ 2.46 cm
            "palmus": 0.074,        # palm, 3 uncia ≈ 7.4 cm
            "pes": 0.296,           # foot, 12 uncia ≈ 29.6 cm
            "palmipes": 0.37,       # 1.25 pes ≈ 37 cm
            "cubitus": 0.444,       # cubit, 1.5 pes ≈ 44.4 cm
            "gradus": 0.74,         # step, 2.5 pes ≈ 74 cm
            "passus": 1.48,         # pace, 5 pes ≈ 1.48 m
            "decempeda": 2.96,      # 10 pes ≈ 2.96 m
            "actus": 35.52,         # 120 pes ≈ 35.52 m
            "stadium": 185,         # 625 pes ≈ 185 m
            "mille_passus": 1480,   # mile, 1000 passus ≈ 1.48 km
            "leuga": 2220,          # league ≈ 2.22 km
        },
        "area": {
            "pes_quadratus": 0.0876,    # square foot ≈ 876 cm²
            "actus_quadratus": 1262,    # square actus ≈ 1262 m²
            "iugerum": 2524,            # ≈ 2524 m² (0.25 hectares)
            "centuria": 50480000,       # 200 iugera ≈ 50.48 km²
        },
        "volume": {
            "ligula": 0.011,        # spoon ≈ 11 ml
            "cyathus": 0.045,       # cup, 4 ligula ≈ 45 ml
            "acetabulum": 0.068,    # vinegar cup ≈ 68 ml
            "hemina": 0.27,         # half pint ≈ 270 ml
            "sextarius": 0.54,      # pint, 2 hemina ≈ 540 ml
            "congius": 3.24,        # 6 sextarius ≈ 3.24 liters
            "urna": 12.96,          # 4 congius ≈ 12.96 liters
            "amphora": 25.92,       # 2 urna ≈ 25.92 liters
            "culleus": 518.4,       # 20 amphora ≈ 518.4 liters
            "modius": 8.73,         # dry measure ≈ 8.73 liters
        },
        "weight": {
            "siliqua": 0.189,       # ≈ 0.189 grams
            "obolus": 0.568,        # 3 siliqua ≈ 0.568 grams
            "scrupulum": 1.137,     # 2 obolus ≈ 1.137 grams
            "drachma": 3.41,        # 3 scrupulum ≈ 3.41 grams
            "uncia": 27.29,         # 8 drachma ≈ 27.29 grams
            "libra": 327.45,        # 12 uncia ≈ 327.45 grams
        }
    },
    
    # HEBREW/BIBLICAL MEASURES
    "hebrew": {
        "length": {
            "etzba": 0.02,          # finger ≈ 2 cm
            "tefach": 0.08,         # handbreadth, 4 etzba ≈ 8 cm
            "zeret": 0.24,          # span, 3 tefach ≈ 24 cm
            "amah": 0.48,           # cubit, 6 tefach ≈ 48 cm
            "kaneh": 2.88,          # reed, 6 amah ≈ 2.88 m
            "ris": 215.04,          # 7500 amah ≈ 215 m
            "parasang": 3840,       # ≈ 3.84 km
        },
        "area": {
            "bet_kor": 75000,       # ≈ 7.5 hectares
            "bet_seah": 2500,       # ≈ 2500 m²
        },
        "volume": {
            "log": 0.3,             # ≈ 300 ml
            "kab": 1.2,             # 4 log ≈ 1.2 liters
            "hin": 3.6,             # 3 kab ≈ 3.6 liters
            "seah": 7.2,            # 2 hin ≈ 7.2 liters
            "ephah": 21.6,          # 3 seah ≈ 21.6 liters
            "lethech": 108,         # 5 ephah ≈ 108 liters
            "kor": 216,             # 2 lethech ≈ 216 liters
            "bath": 21.6,           # liquid measure, same as ephah
        },
        "weight": {
            "gerah": 0.57,          # ≈ 0.57 grams
            "bekah": 5.7,           # 10 gerah ≈ 5.7 grams
            "shekel": 11.4,         # 2 bekah ≈ 11.4 grams
            "mina": 570,            # 50 shekel ≈ 570 grams
            "talent": 34200,        # 60 mina ≈ 34.2 kg
        }
    },
    
    # CHINESE MEASURES (Traditional)
    "chinese": {
        "length": {
            "li": 0.00033,          # 1/10 fen ≈ 0.33 mm
            "fen": 0.0033,          # 1/10 cun ≈ 3.3 mm
            "cun": 0.033,           # inch ≈ 3.3 cm
            "chi": 0.33,            # foot, 10 cun ≈ 33 cm
            "zhang": 3.3,           # 10 chi ≈ 3.3 m
            "yin": 33,              # 10 zhang ≈ 33 m
            "li_distance": 500,     # Chinese mile ≈ 500 m
        },
        "area": {
            "chi_sq": 0.1089,       # square chi ≈ 1089 cm²
            "mu": 666.67,           # ≈ 666.67 m²
            "qing": 6666.7,         # 10 mu ≈ 6666.7 m²
        },
        "volume": {
            "ge": 0.1,              # ≈ 100 ml
            "sheng": 1.0,           # 10 ge ≈ 1 liter
            "dou": 10.0,            # 10 sheng ≈ 10 liters
            "hu": 50.0,             # 5 dou ≈ 50 liters
            "dan": 100.0,           # 10 dou ≈ 100 liters
        },
        "weight": {
            "li_weight": 0.0005,    # ≈ 0.5 grams
            "fen_weight": 0.005,    # 10 li ≈ 5 grams
            "qian": 0.05,           # 10 fen ≈ 50 grams
            "liang": 0.5,           # 10 qian ≈ 500 grams
            "jin": 5.0,             # 10 liang ≈ 5 kg
            "dan_weight": 500,      # 100 jin ≈ 500 kg
        }
    },
    
    # INDIAN MEASURES (Ancient)
    "indian": {
        "length": {
            "angula": 0.019,        # finger ≈ 1.9 cm
            "vitasti": 0.19,        # span, 10 angula ≈ 19 cm
            "hasta": 0.457,         # cubit, 24 angula ≈ 45.7 cm
            "aratni": 0.457,        # same as hasta
            "kishku": 0.914,        # 2 hasta ≈ 91.4 cm
            "danda": 1.828,         # rod, 4 hasta ≈ 1.828 m
            "rajju": 10.97,         # 6 danda ≈ 10.97 m
            "krosa": 2194,          # 200 rajju ≈ 2.194 km
            "yojana": 13164,        # 6 krosa ≈ 13.164 km
        },
        "area": {
            "hasta_sq": 0.209,      # square hasta ≈ 2090 cm²
            "nivartana": 3600,      # ≈ 3600 m²
        },
        "volume": {
            "kudava": 0.192,        # ≈ 192 ml
            "prastha": 0.768,       # 4 kudava ≈ 768 ml
            "adhaka": 3.072,        # 4 prastha ≈ 3.072 liters
            "drona": 12.288,        # 4 adhaka ≈ 12.288 liters
            "khari": 49.152,        # 4 drona ≈ 49.152 liters
        },
        "weight": {
            "ratti": 0.118,         # ≈ 0.118 grams
            "masha": 0.972,         # ≈ 0.972 grams
            "karsha": 11.664,       # 12 masha ≈ 11.664 grams
            "pala": 46.656,         # 4 karsha ≈ 46.656 grams
            "prastha_weight": 373.25, # 8 pala ≈ 373.25 grams
            "adhaka_weight": 2985.6,  # 8 prastha ≈ 2.986 kg
        }
    },
    
    # MEDIEVAL ENGLISH MEASURES
    "medieval_english": {
        "length": {
            "barleycorn": 0.00847,  # ≈ 8.47 mm
            "inch": 0.0254,         # 3 barleycorns ≈ 2.54 cm
            "hand": 0.1016,         # 4 inches ≈ 10.16 cm
            "foot": 0.3048,         # 12 inches ≈ 30.48 cm
            "yard": 0.9144,         # 3 feet ≈ 91.44 cm
            "ell": 1.143,           # 1.25 yards ≈ 1.143 m
            "fathom": 1.8288,       # 6 feet ≈ 1.8288 m
            "rod": 5.0292,          # 16.5 feet ≈ 5.0292 m
            "chain": 20.1168,       # 4 rods ≈ 20.1168 m
            "furlong": 201.168,     # 10 chains ≈ 201.168 m
            "mile": 1609.344,       # 8 furlongs ≈ 1.609 km
            "league": 4828.032,     # 3 miles ≈ 4.828 km
        },
        "area": {
            "square_foot": 0.092903,    # ≈ 929.03 cm²
            "square_yard": 0.836127,    # ≈ 8361.27 cm²
            "rood": 1011.71,            # ≈ 1011.71 m²
            "acre": 4046.86,            # 4 roods ≈ 4046.86 m²
            "hide": 48562.3,            # 12 acres ≈ 48.56 hectares
        },
        "volume": {
            "gill": 0.142065,       # ≈ 142 ml
            "pint": 0.56826,        # 4 gills ≈ 568 ml
            "quart": 1.13652,       # 2 pints ≈ 1.137 liters
            "gallon": 4.54609,      # 4 quarts ≈ 4.546 liters
            "peck": 9.09218,        # 2 gallons ≈ 9.092 liters
            "bushel": 36.3687,      # 4 pecks ≈ 36.369 liters
            "quarter": 290.95,      # 8 bushels ≈ 290.95 liters
        },
        "weight": {
            "grain": 0.0647989,     # ≈ 64.8 mg
            "pennyweight": 1.55517,  # 24 grains ≈ 1.555 grams
            "ounce": 28.3495,       # ≈ 28.35 grams
            "pound": 453.592,       # 16 ounces ≈ 453.59 grams
            "stone": 6350.29,       # 14 pounds ≈ 6.35 kg
            "quarter_weight": 12700.6, # 2 stones ≈ 12.7 kg
            "hundredweight": 50802.3,  # 4 quarters ≈ 50.8 kg
            "ton": 1016047,         # 20 hundredweight ≈ 1016 kg
        }
    }
}

# Conversion categories for UI organization
CONVERSION_CATEGORIES = {
    "Length": ["egyptian", "babylonian", "greek", "roman", "hebrew", "chinese", "indian", "medieval_english"],
    "Area": ["egyptian", "babylonian", "greek", "roman", "hebrew", "chinese", "indian", "medieval_english"],
    "Volume": ["egyptian", "babylonian", "greek", "roman", "hebrew", "chinese", "indian", "medieval_english"],
    "Weight": ["egyptian", "babylonian", "greek", "roman", "hebrew", "chinese", "indian", "medieval_english"]
}

# Civilization display names
CIVILIZATION_NAMES = {
    "egyptian": "Ancient Egyptian",
    "babylonian": "Babylonian/Mesopotamian",
    "greek": "Ancient Greek",
    "roman": "Ancient Roman",
    "hebrew": "Hebrew/Biblical",
    "chinese": "Traditional Chinese",
    "indian": "Ancient Indian",
    "medieval_english": "Medieval English"
}

def get_all_units_for_category(category: str) -> Dict[str, Dict[str, float]]:
    """Get all units for a specific category across all civilizations.
    
    Args:
        category: Category name (length, area, volume, weight)
        
    Returns:
        Dictionary mapping civilization_unit to conversion factor
    """
    units = {}
    category_lower = category.lower()
    
    for civ_name, civ_data in ANCIENT_MEASURES.items():
        if category_lower in civ_data:
            for unit_name, factor in civ_data[category_lower].items():
                display_name = f"{CIVILIZATION_NAMES[civ_name]} {unit_name}"
                units[display_name] = factor
                
    return units

def convert_ancient_unit(value: float, from_unit: str, to_unit: str, category: str) -> float:
    """Convert between ancient units.
    
    Args:
        value: Value to convert
        from_unit: Source unit (format: "civilization unit")
        to_unit: Target unit (format: "civilization unit") 
        category: Category (length, area, volume, weight)
        
    Returns:
        Converted value in target unit
    """
    # Parse civilization and unit from display names
    from_parts = from_unit.split(" ", 2)
    to_parts = to_unit.split(" ", 2)
    
    if len(from_parts) < 3 or len(to_parts) < 3:
        raise ValueError("Invalid unit format. Expected 'Civilization Name unit'")
    
    from_civ = None
    to_civ = None
    from_unit_name = from_parts[2]
    to_unit_name = to_parts[2]
    
    # Find civilization keys
    for civ_key, civ_display in CIVILIZATION_NAMES.items():
        if from_unit.startswith(civ_display):
            from_civ = civ_key
        if to_unit.startswith(civ_display):
            to_civ = civ_key
    
    if not from_civ or not to_civ:
        raise ValueError("Unknown civilization in unit names")
    
    category_lower = category.lower()
    
    # Get conversion factors
    if (category_lower not in ANCIENT_MEASURES[from_civ] or 
        from_unit_name not in ANCIENT_MEASURES[from_civ][category_lower]):
        raise ValueError(f"Unknown source unit: {from_unit}")
        
    if (category_lower not in ANCIENT_MEASURES[to_civ] or 
        to_unit_name not in ANCIENT_MEASURES[to_civ][category_lower]):
        raise ValueError(f"Unknown target unit: {to_unit}")
    
    from_factor = ANCIENT_MEASURES[from_civ][category_lower][from_unit_name]
    to_factor = ANCIENT_MEASURES[to_civ][category_lower][to_unit_name]
    
    # Convert: value * from_factor / to_factor
    return value * from_factor / to_factor

def get_unit_description(civilization: str, unit: str, category: str) -> str:
    """Get a description of an ancient unit.
    
    Args:
        civilization: Civilization key
        unit: Unit name
        category: Category (length, area, volume, weight)
        
    Returns:
        Description string with modern equivalent
    """
    if civilization not in ANCIENT_MEASURES:
        return "Unknown civilization"
        
    category_lower = category.lower()
    if category_lower not in ANCIENT_MEASURES[civilization]:
        return "Unknown category"
        
    if unit not in ANCIENT_MEASURES[civilization][category_lower]:
        return "Unknown unit"
    
    factor = ANCIENT_MEASURES[civilization][category_lower][unit]
    civ_name = CIVILIZATION_NAMES[civilization]
    
    # Format the modern equivalent based on category
    if category_lower == "length":
        if factor < 0.01:
            modern = f"{factor * 1000:.2f} mm"
        elif factor < 1:
            modern = f"{factor * 100:.2f} cm"
        elif factor < 1000:
            modern = f"{factor:.3f} m"
        else:
            modern = f"{factor / 1000:.3f} km"
    elif category_lower == "area":
        if factor < 1:
            modern = f"{factor * 10000:.0f} cm²"
        elif factor < 10000:
            modern = f"{factor:.2f} m²"
        else:
            modern = f"{factor / 10000:.3f} hectares"
    elif category_lower == "volume":
        if factor < 1:
            modern = f"{factor * 1000:.0f} ml"
        else:
            modern = f"{factor:.3f} liters"
    elif category_lower == "weight":
        if factor < 1:
            modern = f"{factor * 1000:.2f} grams"
        elif factor < 1000:
            modern = f"{factor:.2f} grams"
        else:
            modern = f"{factor / 1000:.3f} kg"
    else:
        modern = f"{factor}"
    
    return f"{civ_name} {unit} ≈ {modern}" 