"""
Tests for the Ternary Dimensional Analysis Panel in the TQ pillar.

This file contains tests for verifying the functionality of the
Ternary Dimensional Analysis Panel, focusing on digit interpretation,
pattern analysis, and UI interactions.
"""

import pytest
from PyQt6.QtCore import Qt

from tq.services.ternary_dimension_interpreter import TernaryDimensionInterpreter
from tq.ui.panels.ternary_dimension_panel import TernaryDimensionalAnalysisPanel


class TestTernaryDimensionInterpreter:
    """Tests for the TernaryDimensionInterpreter class."""
    
    def test_interpret_digit(self):
        """Test the digit interpretation functionality."""
        interpreter = TernaryDimensionInterpreter()
        
        # Test different digit interpretations
        assert interpreter.interpret_digit(0, 0)['name'] == "Aperture"
        assert interpreter.interpret_digit(1, 0)['name'] == "Surge"
        assert interpreter.interpret_digit(2, 0)['name'] == "Lattice"
        
        # Test positions affect interpretations
        first_pos = interpreter.interpret_digit(1, 0)
        middle_pos = interpreter.interpret_digit(1, 3)
        last_pos = interpreter.interpret_digit(1, 8)
        assert first_pos['position'] == "Seed"
        assert middle_pos['position'] == "Weave"
        assert last_pos['position'] == "Nova"
    
    def test_analyze_ternary(self):
        """Test the ternary number analysis functionality."""
        interpreter = TernaryDimensionInterpreter()
        
        # Test analysis of simple ternary number
        analysis = interpreter.analyze_ternary([1, 0, 2])
        assert len(analysis["digits"]) == 3  # One interpretation per digit
        assert analysis["digits"][2]["name"] == "Surge" # Remember list is reversed
        assert analysis["digits"][1]["name"] == "Aperture"
        assert analysis["digits"][0]["name"] == "Lattice"
        assert "balance" in analysis
        assert "overall" in analysis
        
        # Test analysis of longer ternary number
        analysis = interpreter.analyze_ternary([1, 2, 0, 1, 2])
        assert len(analysis["digits"]) == 5  # One interpretation per digit
    
    def test_analyze_patterns(self):
        """Test the pattern analysis functionality."""
        interpreter = TernaryDimensionInterpreter()
        
        # Test pattern analysis
        analysis = interpreter.analyze_patterns([1, 1, 1, 0, 0, 2, 2, 2])
        assert len(analysis["patterns"]) == 2
        assert analysis["patterns"][0]["digit"] == 1
        assert analysis["patterns"][0]["length"] == 3
        assert analysis["patterns"][1]["digit"] == 2
        assert analysis["patterns"][1]["length"] == 3
        
        # Test empty pattern
        empty_patterns = interpreter.analyze_patterns([])
        assert not empty_patterns["patterns"]


@pytest.mark.skip(reason="Requires QApplication instance")
class TestTernaryDimensionalAnalysisPanel:
    """Tests for the TernaryDimensionalAnalysisPanel UI component."""
    
    def test_decimal_to_ternary_conversion(self, qtbot):
        """Test decimal to ternary conversion in the panel."""
        # This test would require a QApplication instance
        panel = TernaryDimensionalAnalysisPanel()
        qtbot.addWidget(panel)
        
        # Set decimal input and trigger analysis
        panel.decimal_input.setText("28")
        qtbot.mouseClick(panel.analyze_button, Qt.MouseButton.LeftButton)
        
        # Verify ternary output is correct (28 in decimal = 1001 in ternary)
        assert panel.ternary_display.text() == "1001"
        
        # Verify analysis is generated by checking if results are visible
        assert panel.position_group.isVisible()
        assert panel.pattern_group.isVisible()
        assert panel.pattern_text.toPlainText() != "" 