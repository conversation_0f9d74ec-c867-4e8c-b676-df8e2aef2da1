#!/usr/bin/env python3
"""
Create a test calculation with the problematic format seen in the screenshot.
This creates a calculation with the custom method name in the exact format that's 
showing the issue in the UI.
"""

import os
import sys
import logging
from enum import Enum, auto

# Set up logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

from gematria.models.calculation_result import CalculationResult
from gematria.services.calculation_database_service import CalculationDatabaseService
from shared.services.service_locator import ServiceLocator

class Language(Enum):
    """Mock Language enum to simulate the real one"""
    GREEK = 'Greek'

def main():
    """Create test calculations with the problematic format"""
    logger.info("Creating test calculation with problematic format...")
    
    # Initialize database service
    calculation_service = CalculationDatabaseService()
    
    # Create the problematic custom method tuple with the exact format from screenshot
    problematic_tuple = ('Greek Ordinal Value', 
                         'Greek: Ordinal Value. Each letter numbered by position in alphabet.',
                         Language.GREEK)
    
    # Create three versions:
    # 1. Direct tuple object
    calc1 = CalculationResult(
        input_text="Test Direct Tuple",
        calculation_type="CUSTOM_CIPHER",
        result_value=123,
        custom_method_name=problematic_tuple
    )
    
    # 2. String representation of the tuple with Custom: prefix
    problematic_string = f"Custom: {str(problematic_tuple)}"
    calc2 = CalculationResult(
        input_text="Test String with Custom Prefix",
        calculation_type="CUSTOM_CIPHER",
        result_value=456,
        custom_method_name=problematic_string
    )
    
    # 3. Stringified version without prefix
    calc3 = CalculationResult(
        input_text="Test String Tuple",
        calculation_type="CUSTOM_CIPHER",
        result_value=789,
        custom_method_name=str(problematic_tuple)
    )
    
    # Save the calculations
    calculation_service.add_calculation(calc1)
    calculation_service.add_calculation(calc2)
    calculation_service.add_calculation(calc3)
    
    logger.info("Created test calculations with the following formats:")
    logger.info(f"1. Direct tuple object: {problematic_tuple}")
    logger.info(f"2. String with Custom prefix: {problematic_string}")
    logger.info(f"3. String tuple: {str(problematic_tuple)}")
    
    print("\nTest calculations created successfully!")
    print("Please open the application and check the calculation history panel")
    print("Open the calculation details for each test calculation to see if the method name displays correctly")

if __name__ == "__main__":
    main()
