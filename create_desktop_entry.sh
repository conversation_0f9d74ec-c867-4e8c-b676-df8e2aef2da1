#!/bin/bash

# IsopGem Desktop Entry Creator
# This script creates a desktop entry for easy application launching

set -e

echo "🖥️ Creating Desktop Entry for IsopGem..."
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the current directory (where IsopGem is located)
CURRENT_DIR=$(pwd)
EXECUTABLE_PATH="$CURRENT_DIR/dist/IsopGem/IsopGem"
LAUNCHER_PATH="$CURRENT_DIR/launch_isopgem.sh"

# Check if executable exists
if [ ! -f "$EXECUTABLE_PATH" ]; then
    print_error "IsopGem executable not found at $EXECUTABLE_PATH"
    print_error "Please build the executable first using: ./build_executable.sh"
    exit 1
fi

# Check if launcher exists
if [ ! -f "$LAUNCHER_PATH" ]; then
    print_error "Launcher script not found at $LAUNCHER_PATH"
    exit 1
fi

# Create desktop entry content
DESKTOP_ENTRY="[Desktop Entry]
Version=1.0
Type=Application
Name=IsopGem
Comment=Sacred Geometry & Gematria Tool
Exec=$LAUNCHER_PATH
Icon=$CURRENT_DIR/resources/images/icon.png
Terminal=false
StartupNotify=true
Categories=Education;Science;Math;
Keywords=geometry;gematria;sacred;mathematics;astrology;
StartupWMClass=IsopGem"

# Create desktop entry file
DESKTOP_FILE="$HOME/.local/share/applications/isopgem.desktop"

print_status "Creating desktop entry at: $DESKTOP_FILE"

# Create the applications directory if it doesn't exist
mkdir -p "$HOME/.local/share/applications"

# Write the desktop entry
echo "$DESKTOP_ENTRY" > "$DESKTOP_FILE"

# Make it executable
chmod +x "$DESKTOP_FILE"

print_success "Desktop entry created successfully!"

# Update desktop database
if command -v update-desktop-database &> /dev/null; then
    print_status "Updating desktop database..."
    update-desktop-database "$HOME/.local/share/applications"
    print_success "Desktop database updated!"
else
    print_status "update-desktop-database not found, skipping database update"
fi

echo ""
echo "========================================"
print_success "Desktop Entry Created! 🎉"
echo ""
echo "IsopGem should now appear in your application menu."
echo "You can also run it by searching for 'IsopGem' in your desktop environment."
echo ""
echo "Desktop entry location: $DESKTOP_FILE"
echo "========================================" 