#!/usr/bin/env python3
"""
Final verification of our fix for tuple display in custom method names.
"""

# Direct test of our extraction function with the main problematic cases

# Test cases
test_cases = [
    # The key problematic case
    "Custom: ('Method Name in Tuple', 'Extra Data')",
    
    # Simple tuple
    ("Tuple Method", "Extra Data"),
    
    # Nested tuple 
    (("Nested", "Inner"), "Outer")
]

def extract_method_name(custom_method):
    """Extract method name using our updated algorithm."""
    # Handle direct tuple case
    if isinstance(custom_method, tuple):
        if len(custom_method) > 0:
            first_element = custom_method[0]
            if isinstance(first_element, tuple) and len(first_element) > 0:
                return str(first_element[0])
            return str(first_element)
        return "Custom Method"
        
    # Handle string representation of tuple
    elif isinstance(custom_method, str):
        # First check for "Custom: " prefix with tuple
        if custom_method.startswith("Custom: (") and "," in custom_method:
            try:
                start = custom_method.find("'", 8)  # Start after "Custom: ("
                if start != -1:
                    end = custom_method.find("'", start + 1)
                    if end != -1:
                        return custom_method[start + 1:end]
            except Exception:
                pass
                
        elif custom_method.startswith("(") and "," in custom_method:
            try:
                start = custom_method.find("'")
                if start != -1:
                    end = custom_method.find("'", start + 1)
                    if end != -1:
                        return custom_method[start + 1:end]
            except Exception:
                pass
                
    # Default fallback
    return custom_method

# Run tests
print("Testing method name extraction fix")
print("=" * 40)

for i, test in enumerate(test_cases):
    print(f"Test {i+1}:")
    print(f"Input: {repr(test)}")
    
    result = extract_method_name(test)
    
    print(f"Result: {repr(result)}")
    
    # Check for tuple syntax in result
    if isinstance(result, str):
        has_tuple_syntax = "(" in result and "," in result and ")" in result
        if has_tuple_syntax:
            print("❌ FAILED: Result still contains tuple syntax")
        else:
            print("✅ PASSED: Clean result")
    else:
        print(f"ℹ️ Result is not a string: {result}")
        
    print("-" * 40)
