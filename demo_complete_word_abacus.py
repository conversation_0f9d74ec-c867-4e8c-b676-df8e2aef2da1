#!/usr/bin/env python3
"""
Demo script for the beautifully redesigned Word Abacus widget
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow
from PyQt6.QtCore import QTimer

def demo_complete_word_abacus():
    """Demo the beautifully redesigned Word Abacus widget."""
    app = QApplication(sys.argv)
    
    # Create main window
    window = QMainWindow()
    window.setWindowTitle("✨ Word Abacus - Beautiful Modern Interface")
    window.setGeometry(100, 100, 1200, 900)
    
    # Import and create the widget
    from gematria.services.gematria_service import GematriaService
    from gematria.services.custom_cipher_service import CustomCipherService
    from gematria.services.history_service import HistoryService
    from gematria.ui.widgets.word_abacus_widget import WordAbacusWidget
    
    # Create services
    gematria_service = GematriaService()
    custom_cipher_service = CustomCipherService()
    history_service = HistoryService()
    
    # Create the widget
    widget = WordAbacusWidget(
        gematria_service, 
        custom_cipher_service, 
        history_service
    )
    
    # Set as central widget
    window.setCentralWidget(widget)
    
    # Show the window
    window.show()
    
    # Add some demo text after a moment
    def add_demo_text():
        widget._input_field.setText("shalom")
        widget._transliterate_chk.setChecked(True)
        print("✨ Word Abacus with beautiful modern interface is ready!")
        print("🎯 Action buttons: 🧮 Calculate | 💾 Save")
        print("🎯 Toolbar buttons:")
        print("   📋 Import Word/Phrase List")
        print("   ❓ Help")
        print("🔧 Custom Ciphers is now properly located on the Gematria Tab!")
        print("🖱️ NEW: Right-click context menus!")
        print("   📊 Right-click result display for Quadset Analysis & PolyCalc")
        print("   📋 Right-click history table for copy & send options")
        print("🎉 The old widget has been successfully replaced!")
        print("🌟 Try the beautiful new interface!")
    
    QTimer.singleShot(1000, add_demo_text)
    
    # Take a screenshot after everything loads
    def take_screenshot():
        widget.grab().save('word_abacus_complete.png')
        print("📸 Screenshot saved as word_abacus_complete.png")
    
    QTimer.singleShot(2000, take_screenshot)
    
    app.exec()

if __name__ == "__main__":
    demo_complete_word_abacus() 