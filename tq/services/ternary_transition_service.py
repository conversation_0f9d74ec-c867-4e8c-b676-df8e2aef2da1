"""
Purpose: Service to manage the Ternary Transitions window and handle communication between panels.

This service follows the singleton pattern and provides methods to:
1. Get or create the Ternary Transitions window
2. Set transition numbers
3. Trigger transition calculations
"""

import logging

from PyQt6.QtCore import QObject, Qt, QTimer

from tq.ui.dialogs.ternary_transition_window import TernaryTransitionWindow

logger = logging.getLogger(__name__)


class TernaryTransitionService(QObject):
    _instance = None

    def __init__(self):
        super().__init__()
        self.window = None

    @classmethod
    def get_instance(cls):
        """Get the singleton instance of the service."""
        if cls._instance is None:
            cls._instance = TernaryTransitionService()
        return cls._instance

    def get_window(self):
        """Get or create the Ternary Transitions window."""
        # Check if we have a window and if it's still valid
        if self.window is not None:
            try:
                # Try to access a property to see if the window is still valid
                # If the window has been deleted, this will raise a RuntimeError
                _ = self.window.isVisible()
                # If we get here, the window is still valid
                return self.window
            except RuntimeError:
                # Window has been deleted, clear our reference
                logger.debug("Previous TernaryTransitionWindow was deleted, creating new one")
                self.window = None
        
        # Create a new window
        logger.debug("Creating new TernaryTransitionWindow")
        self.window = TernaryTransitionWindow()
        
        # Connect to the destroyed signal to clean up our reference
        self.window.destroyed.connect(self._on_window_destroyed)
        
        return self.window
    
    def _on_window_destroyed(self):
        """Handle window destruction by clearing our reference."""
        logger.debug("TernaryTransitionWindow was destroyed, clearing reference")
        self.window = None

    def set_transition_numbers(self, first_number: int, second_number: int):
        """Set the transition numbers and show the window.

        Args:
            first_number (int): The first number for the transition
            second_number (int): The second number for the transition
        """
        logger.debug(
            f"TernaryTransitionService: Setting numbers {first_number} and {second_number}"
        )

        # Get or create a valid window using the robust method
        self.window = self.get_window()
        logger.debug("Got valid TernaryTransitionWindow")

        # Set the numbers and show
        logger.debug("Calling window.set_transition_numbers")
        self.window.set_transition_numbers(first_number, second_number)
        logger.debug("Window set_transition_numbers complete")

        # Ensure window is visible and focused using stronger focus methods
        self.window.show()

        # Use multiple methods to force the window to the front
        self.window.setWindowState(
            self.window.windowState() & ~Qt.WindowState.WindowMinimized
        )
        self.window.raise_()
        self.window.activateWindow()

        # If the window has an ensure_on_top method (inherits from AuxiliaryWindow), use it
        if hasattr(self.window, "ensure_on_top"):
            self.window.ensure_on_top()

        # Use timer to attempt focus again after event loop processes other events
        QTimer.singleShot(100, lambda: self._delayed_focus())

        logger.debug("Window visibility and focus operations complete")

    def _delayed_focus(self):
        """Apply delayed focus to ensure window is on top."""
        if self.window is not None:
            try:
                # Check if window is still valid and visible
                if self.window.isVisible():
                    self.window.raise_()
                    self.window.activateWindow()
                    logger.debug("Applied delayed focus to ternary transition window")
            except RuntimeError:
                # Window has been deleted, clear our reference
                logger.debug("Window was deleted during delayed focus, clearing reference")
                self.window = None
