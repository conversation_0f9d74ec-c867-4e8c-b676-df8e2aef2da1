IsopGem - Quick Start Guide
===========================

🚀 GETTING STARTED (3 Simple Steps):

1. EXTRACT THE FILES
   - Extract this archive to any folder (e.g., ~/Applications/IsopGem)
   - Open a terminal in that folder

2. MAKE EXECUTABLE
   chmod +x launch_isopgem.sh

3. RUN THE APPLICATION
   ./launch_isopgem.sh

🖥️ OPTIONAL - CREATE DESKTOP SHORTCUT:
   chmod +x create_desktop_entry.sh
   ./create_desktop_entry.sh

📋 SYSTEM REQUIREMENTS:
   - Linux x86-64 (Ubuntu, Debian, Fedora, etc.)
   - No Python installation required
   - ~250MB disk space
   - OpenGL support (usually pre-installed)

🐛 TROUBLESHOOTING:
   - If you get "Permission denied": chmod +x launch_isopgem.sh
   - If missing libraries: sudo apt install libgl1-mesa-glx libglib2.0-0
   - For other issues, see EXECUTABLE_BUILD_GUIDE.md

🎉 That's it! IsopGem should now be running.

For detailed documentation, see EXECUTABLE_BUILD_GUIDE.md
