#!/bin/bash

# IsopGem Launcher Script
# This script launches the IsopGem executable from the dist directory

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Path to the executable
EXECUTABLE="$SCRIPT_DIR/dist/IsopGem/IsopGem"

# Check if executable exists
if [ ! -f "$EXECUTABLE" ]; then
    echo "Error: IsopGem executable not found at $EXECUTABLE"
    echo "Please build the executable first using: ./build_executable.sh"
    exit 1
fi

# Launch the application
echo "Starting IsopGem..."
cd "$SCRIPT_DIR"
exec "$EXECUTABLE" "$@"
