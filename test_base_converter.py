#!/usr/bin/env python3
"""
Test script for the enhanced number base converter functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from geometry.calculator.advanced_scientific_calculator import AdvancedScientificCalculator

def test_base_conversions():
    """Test various base conversions to verify functionality."""
    calc = AdvancedScientificCalculator()
    
    print("=== Enhanced Number Base Converter Test ===\n")
    
    # Test cases: (number, from_base, to_base, expected_result)
    test_cases = [
        # Basic conversions
        ("42", 10, 2, "101010"),
        ("42", 10, 8, "52"),
        ("42", 10, 16, "2A"),
        ("101010", 2, 10, "42"),
        ("52", 8, 10, "42"),
        ("2A", 16, 10, "42"),
        
        # More exotic bases
        ("42", 10, 3, "1120"),
        ("42", 10, 5, "132"),
        ("42", 10, 7, "60"),
        ("42", 10, 12, "36"),
        ("42", 10, 20, "22"),
        ("42", 10, 36, "16"),
        
        # Reverse conversions
        ("1120", 3, 10, "42"),
        ("132", 5, 10, "42"),
        ("60", 7, 10, "42"),
        ("36", 12, 10, "42"),
        ("22", 20, 10, "42"),
        ("16", 36, 10, "42"),
        
        # Base to base (not through decimal)
        ("FF", 16, 2, "11111111"),
        ("377", 8, 16, "FF"),
        ("ZZ", 36, 10, "1295"),
        
        # Negative numbers
        ("-42", 10, 2, "-101010"),
        ("-42", 10, 16, "-2A"),
        
        # Zero
        ("0", 10, 2, "0"),
        ("0", 16, 8, "0"),
        
        # Large numbers
        ("1000", 10, 2, "1111101000"),
        ("1000", 10, 36, "RS"),
        
        # Edge cases with maximum base (36)
        ("Z", 36, 10, "35"),
        ("10", 36, 10, "36"),
        ("ZZ", 36, 10, "1295"),
    ]
    
    passed = 0
    failed = 0
    
    for number, from_base, to_base, expected in test_cases:
        result = calc.convert_base(number, from_base, to_base)
        
        if result == expected:
            print(f"✓ {number} (base {from_base}) → {result} (base {to_base})")
            passed += 1
        else:
            print(f"✗ {number} (base {from_base}) → {result} (base {to_base}) [Expected: {expected}]")
            if calc.get_error():
                print(f"  Error: {calc.get_error()}")
                calc.clear_error()
            failed += 1
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total:  {passed + failed}")
    
    if failed == 0:
        print("🎉 All tests passed!")
    else:
        print(f"❌ {failed} test(s) failed")
    
    return failed == 0

def test_error_handling():
    """Test error handling for invalid inputs."""
    calc = AdvancedScientificCalculator()
    
    print("\n=== Error Handling Tests ===\n")
    
    error_cases = [
        # Invalid base ranges
        ("42", 1, 10, "Base must be between 2 and 36"),
        ("42", 10, 37, "Base must be between 2 and 36"),
        ("42", 0, 10, "Base must be between 2 and 36"),
        
        # Invalid digits for base
        ("9", 2, 10, "Invalid digit for base 2"),
        ("A", 10, 16, "Invalid digit for base 10"),
        ("G", 16, 10, "Invalid digit for base 16"),
        ("Z", 35, 10, "Invalid digit for base 35"),
    ]
    
    for number, from_base, to_base, expected_error in error_cases:
        result = calc.convert_base(number, from_base, to_base)
        error = calc.get_error()
        
        if error and expected_error in error:
            print(f"✓ {number} (base {from_base}) → Error: {error}")
        else:
            print(f"✗ {number} (base {from_base}) → {result} [Expected error: {expected_error}]")
        
        calc.clear_error()

def demonstrate_features():
    """Demonstrate the enhanced features."""
    calc = AdvancedScientificCalculator()
    
    print("\n=== Feature Demonstration ===\n")
    
    # Show conversion of the same number to multiple bases
    number = "255"
    from_base = 10
    
    print(f"Converting {number} (decimal) to various bases:")
    print("-" * 40)
    
    bases_to_show = [2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 16, 20, 24, 32, 36]
    
    for base in bases_to_show:
        result = calc.convert_base(number, from_base, base)
        base_name = {
            2: "Binary",
            8: "Octal", 
            10: "Decimal",
            16: "Hexadecimal"
        }.get(base, f"Base {base}")
        
        print(f"{base_name:>12} ({base:>2}): {result}")
    
    print(f"\n=== Alphabet Demonstration (Base 36) ===")
    print("Base 36 uses digits 0-9 and letters A-Z")
    print("-" * 40)
    
    # Show how letters work in base 36
    for i in range(36):
        decimal_val = str(i)
        base36_val = calc.convert_base(decimal_val, 10, 36)
        print(f"Decimal {i:>2} = Base36 {base36_val}")
        if i == 15:  # Show first 16 for brevity
            print("... (and so on up to Z=35)")
            break

if __name__ == "__main__":
    print("Testing Enhanced Number Base Converter")
    print("=" * 50)
    
    # Run all tests
    success = test_base_conversions()
    test_error_handling()
    demonstrate_features()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Enhanced base converter is working correctly!")
    else:
        print("❌ Some tests failed - check implementation")
